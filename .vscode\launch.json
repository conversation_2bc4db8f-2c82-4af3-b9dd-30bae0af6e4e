{"version": "0.2.0", "configurations": [{"name": "DEV :MOBILE: DEBUG", "request": "launch", "type": "dart", "flutterMode": "debug", "args": ["-t", "lib/main.dart"]}, {"name": "DEV :MOBILE: RELEASE", "request": "launch", "type": "dart", "flutterMode": "release", "args": ["-t", "lib/main_dev.dart", "--flavor", "dev"]}, {"name": "PROD :MOBILE: DEBUG", "request": "launch", "type": "dart", "flutterMode": "debug", "args": ["-t", "lib/main_prod.dart", "--flavor", "prod"]}, {"name": "PROD :MOBILE: RELEASE", "request": "launch", "type": "dart", "flutterMode": "release", "args": ["-t", "lib/main_prod.dart", "--flavor", "prod"]}, {"name": "QA :MOBILE: DEBUG", "request": "launch", "type": "dart", "flutterMode": "debug", "args": ["-t", "lib/main_qa.dart", "--flavor", "qa"]}, {"name": "QA :MOBILE: Release", "request": "launch", "type": "dart", "flutterMode": "release", "args": ["-t", "lib/main_qa.dart", "--flavor", "qa"]}]}