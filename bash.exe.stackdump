Stack trace:
Frame         Function      Args
0007FFFF8E80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x2118E
0007FFFF8E80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x69BA
0007FFFF8E80  0002100469F2 (00021028DF99, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8E80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8E80  00021006A545 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9160  00021006B9A5 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD100E0000 ntdll.dll
7FFD0EF70000 KERNEL32.DLL
7FFD0D650000 KERNELBASE.dll
7FFD0A340000 apphelp.dll
7FFD0E8C0000 USER32.dll
7FFD0D580000 win32u.dll
000210040000 msys-2.0.dll
7FFD0DEE0000 GDI32.dll
7FFD0DA20000 gdi32full.dll
7FFD0DBF0000 msvcp_win.dll
7FFD0D430000 ucrtbase.dll
7FFD0FFE0000 advapi32.dll
7FFD0F390000 msvcrt.dll
7FFD0E170000 sechost.dll
7FFD0F040000 RPCRT4.dll
7FFD0C8B0000 CRYPTBASE.DLL
7FFD0D5B0000 bcryptPrimitives.dll
7FFD0EA90000 IMM32.DLL
