import 'package:flutter/material.dart';

/// A reusable custom dropdown widget that can be used throughout the app
class CustomDropdown<T> extends StatelessWidget {
  final T value;
  final List<DropdownMenuItem<T>> items;
  final String label;
  final String? hint;
  final bool isRequired;
  final Function(T?) onChanged;
  final String? Function(T?)? validator;
  final bool enabled;

  const CustomDropdown({
    super.key,
    required this.value,
    required this.items,
    required this.label,
    this.hint,
    this.isRequired = false,
    required this.onChanged,
    this.validator,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: isRequired ? '$label *' : label,
        hintText: hint,
      ),
      items: items,
      onChanged: enabled ? onChanged : null,
      validator: validator ??
          (isRequired
              ? (value) {
                  if (value == null) {
                    return 'Please select $label';
                  }
                  return null;
                }
              : null),
      isExpanded: true,
    );
  }

  /// Factory constructor for creating a string dropdown
  static CustomDropdown<String> fromStrings({
    required String value,
    required List<String> options,
    required String label,
    String? hint,
    bool isRequired = false,
    required Function(String?) onChanged,
    String? Function(String?)? validator,
    bool enabled = true,
  }) {
    return CustomDropdown<String>(
      value: value,
      items: options.map((option) {
        return DropdownMenuItem<String>(
          value: option,
          child: Text(option),
        );
      }).toList(),
      label: label,
      hint: hint,
      isRequired: isRequired,
      onChanged: onChanged,
      validator: validator,
      enabled: enabled,
    );
  }

  /// Factory constructor for creating a map dropdown
  static CustomDropdown<T> fromMap<T>({
    required T value,
    required Map<T, String> options,
    required String label,
    String? hint,
    bool isRequired = false,
    required Function(T?) onChanged,
    String? Function(T?)? validator,
    bool enabled = true,
  }) {
    return CustomDropdown<T>(
      value: value,
      items: options.entries.map((entry) {
        return DropdownMenuItem<T>(
          value: entry.key,
          child: Text(entry.value),
        );
      }).toList(),
      label: label,
      hint: hint,
      isRequired: isRequired,
      onChanged: onChanged,
      validator: validator,
      enabled: enabled,
    );
  }
}
