import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A reusable custom text field widget that can be used throughout the app
class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final bool isRequired;
  final TextInputType keyboardType;
  final int? maxLines;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final Widget? suffix;
  final Widget? prefix;
  final bool enabled;
  final Function(String)? onChanged;

  const CustomTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.keyboardType = TextInputType.text,
    this.maxLines = 1,
    this.validator,
    this.inputFormatters,
    this.obscureText = false,
    this.suffix,
    this.prefix,
    this.enabled = true,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: isRequired ? '$label *' : label,
        hintText: hint,
        suffixIcon: suffix,
        prefixIcon: prefix,
      ),
      keyboardType: keyboardType,
      maxLines: maxLines,
      obscureText: obscureText,
      enabled: enabled,
      validator: validator ?? (isRequired 
        ? (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter $label';
            }
            return null;
          }
        : null),
      inputFormatters: inputFormatters,
      onChanged: onChanged,
    );
  }

  /// Factory constructor for creating a numeric text field
  factory CustomTextField.numeric({
    required TextEditingController controller,
    required String label,
    String? hint,
    bool isRequired = false,
    bool allowDecimal = false,
    Function(String)? onChanged,
    bool enabled = true,
  }) {
    return CustomTextField(
      controller: controller,
      label: label,
      hint: hint,
      isRequired: isRequired,
      keyboardType: allowDecimal 
        ? const TextInputType.numberWithOptions(decimal: true)
        : TextInputType.number,
      inputFormatters: [
        if (!allowDecimal) FilteringTextInputFormatter.digitsOnly,
      ],
      validator: (value) {
        if (isRequired && (value == null || value.isEmpty)) {
          return 'Please enter $label';
        }
        if (value != null && value.isNotEmpty) {
          if (allowDecimal) {
            if (double.tryParse(value) == null) {
              return 'Please enter a valid number';
            }
          } else {
            if (int.tryParse(value) == null) {
              return 'Please enter a valid number';
            }
          }
        }
        return null;
      },
      onChanged: onChanged,
      enabled: enabled,
    );
  }

  /// Factory constructor for creating a price text field
  factory CustomTextField.price({
    required TextEditingController controller,
    required String label,
    String? hint,
    bool isRequired = false,
    String currency = 'NPR',
    Function(String)? onChanged,
    bool enabled = true,
  }) {
    return CustomTextField(
      controller: controller,
      label: label,
      hint: hint ?? 'e.g., 100.00',
      isRequired: isRequired,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      validator: (value) {
        if (isRequired && (value == null || value.isEmpty)) {
          return 'Please enter $label';
        }
        if (value != null && value.isNotEmpty) {
          if (double.tryParse(value) == null) {
            return 'Please enter a valid price';
          }
        }
        return null;
      },
      suffix: Text(currency, style: const TextStyle(color: Colors.grey)),
      onChanged: onChanged,
      enabled: enabled,
    );
  }
}
