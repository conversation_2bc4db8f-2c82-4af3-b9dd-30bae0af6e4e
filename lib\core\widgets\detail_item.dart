import 'package:flutter/material.dart';

/// A reusable widget for displaying a detail item in a details screen
class DetailItem extends StatelessWidget {
  final String label;
  final String value;
  final bool showDivider;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;

  const DetailItem({
    super.key,
    required this.label,
    required this.value,
    this.showDivider = true,
    this.labelStyle,
    this.valueStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: labelStyle ?? 
              const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: valueStyle ?? const TextStyle(fontSize: 16),
          ),
          if (showDivider) const Divider(),
        ],
      ),
    );
  }
}
