import 'package:flutter/material.dart';

/// A reusable widget for displaying a status badge
class StatusBadge extends StatelessWidget {
  final String status;
  final Map<String, Color>? statusColors;

  const StatusBadge({
    super.key,
    required this.status,
    this.statusColors,
  });

  @override
  Widget build(BuildContext context) {
    final Color statusColor = _getStatusColor(status);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withAlpha(51), // 0.2 * 255 = 51
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: statusColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    if (statusColors != null && statusColors!.containsKey(status)) {
      return statusColors![status]!;
    }

    // Default status colors
    switch (status.toLowerCase()) {
      case 'active':
      case 'open':
        return Colors.green;
      case 'inactive':
      case 'closed':
        return Colors.red;
      case 'maintenance':
      case 'under maintenance':
        return Colors.orange;
      case 'planned':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// Factory constructor for valve status badge
  factory StatusBadge.valve(String status) {
    return StatusBadge(
      status: status,
      statusColors: {
        'Open': Colors.green,
        'Closed': Colors.red,
        'Maintenance': Colors.orange,
        'Planned': Colors.blue,
      },
    );
  }

  /// Factory constructor for smart meter status badge
  factory StatusBadge.smartMeter(String status) {
    return StatusBadge(
      status: status,
      statusColors: {
        'Active': Colors.green,
        'Inactive': Colors.grey,
        'Maintenance': Colors.orange,
      },
    );
  }

  /// Factory constructor for pipeline status badge
  factory StatusBadge.pipeline(String status) {
    return StatusBadge(
      status: status,
      statusColors: {
        'Active': Colors.green,
        'Inactive': Colors.grey,
        'Under Maintenance': Colors.orange,
        'Planned': Colors.blue,
      },
    );
  }
}
