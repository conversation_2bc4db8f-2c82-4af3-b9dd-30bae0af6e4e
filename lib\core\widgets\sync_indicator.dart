import 'package:flutter/material.dart';

/// A reusable widget for displaying a sync status indicator
class SyncIndicator extends StatelessWidget {
  final bool isSynced;
  final bool isSyncing;
  final VoidCallback? onPressed;

  const SyncIndicator({
    super.key,
    required this.isSynced,
    this.isSyncing = false,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    if (isSyncing) {
      return const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 2,
        ),
      );
    }

    return IconButton(
      icon: Icon(
        isSynced ? Icons.cloud_done : Icons.cloud_upload,
        color: isSynced ? Colors.green : Colors.orange,
      ),
      tooltip: isSynced ? 'Synced with server' : 'Sync with server',
      onPressed: onPressed,
    );
  }
}
