import 'base_entity.dart';

/// Connector entity
class Connector extends BaseEntity {
  final String title;
  final String connectorType;
  final Map<String, dynamic> specifications;
  final double? pricePerUnit;

  const Connector({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    required bool isSync,
    required bool isDeleted,
    required this.title,
    required this.connectorType,
    required this.specifications,
    this.pricePerUnit,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          isSync: isSync,
          isDeleted: isDeleted,
        );

  /// Create a copy of this entity with the given fields replaced with the new values
  Connector copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSync,
    bool? isDeleted,
    String? title,
    String? connectorType,
    Map<String, dynamic>? specifications,
    double? pricePerUnit,
    bool clearPricePerUnit = false,
  }) {
    return Connector(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSync: isSync ?? this.isSync,
      isDeleted: isDeleted ?? this.isDeleted,
      title: title ?? this.title,
      connectorType: connectorType ?? this.connectorType,
      specifications: specifications ?? this.specifications,
      pricePerUnit: clearPricePerUnit ? null : (pricePerUnit ?? this.pricePerUnit),
    );
  }
}
