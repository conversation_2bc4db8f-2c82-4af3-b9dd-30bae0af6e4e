import 'base_entity.dart';

/// Pipeline entity
class Pipeline extends BaseEntity {
  final String title;
  final String pipeType;
  final String pipeDiameter;
  final String state;
  final Map<String, dynamic> properties;
  final List<PipelineSegment> segments;
  final double? pricePerUnit;

  const Pipeline({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    required bool isSync,
    required bool isDeleted,
    required this.title,
    required this.pipeType,
    required this.pipeDiameter,
    required this.state,
    required this.properties,
    required this.segments,
    this.pricePerUnit,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          isSync: isSync,
          isDeleted: isDeleted,
        );

  /// Create a copy of this entity with the given fields replaced with the new values
  Pipeline copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSync,
    bool? isDeleted,
    String? title,
    String? pipeType,
    String? pipeDiameter,
    String? state,
    Map<String, dynamic>? properties,
    List<PipelineSegment>? segments,
    double? pricePerUnit,
    bool clearPricePerUnit = false,
  }) {
    return Pipeline(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSync: isSync ?? this.isSync,
      isDeleted: isDeleted ?? this.isDeleted,
      title: title ?? this.title,
      pipeType: pipeType ?? this.pipeType,
      pipeDiameter: pipeDiameter ?? this.pipeDiameter,
      state: state ?? this.state,
      properties: properties ?? this.properties,
      segments: segments ?? this.segments,
      pricePerUnit: clearPricePerUnit ? null : (pricePerUnit ?? this.pricePerUnit),
    );
  }
}

/// Pipeline segment entity
class PipelineSegment {
  final String id;
  final double startLatitude;
  final double startLongitude;
  final double endLatitude;
  final double endLongitude;
  final String state;

  const PipelineSegment({
    required this.id,
    required this.startLatitude,
    required this.startLongitude,
    required this.endLatitude,
    required this.endLongitude,
    required this.state,
  });

  /// Create a copy of this entity with the given fields replaced with the new values
  PipelineSegment copyWith({
    String? id,
    double? startLatitude,
    double? startLongitude,
    double? endLatitude,
    double? endLongitude,
    String? state,
  }) {
    return PipelineSegment(
      id: id ?? this.id,
      startLatitude: startLatitude ?? this.startLatitude,
      startLongitude: startLongitude ?? this.startLongitude,
      endLatitude: endLatitude ?? this.endLatitude,
      endLongitude: endLongitude ?? this.endLongitude,
      state: state ?? this.state,
    );
  }
}


