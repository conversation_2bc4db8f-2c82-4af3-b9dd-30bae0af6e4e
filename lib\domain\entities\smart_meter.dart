import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'base_entity.dart';

/// Smart meter entity
class SmartMeter extends BaseEntity {
  final double latitude;
  final double longitude;
  final String title;
  final Map<String, dynamic> specifications;
  final String status;
  final DateTime? lastReading;
  final String areaId;
  final double? pricePerUnit;

  const SmartMeter({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    required bool isSync,
    required bool isDeleted,
    required this.latitude,
    required this.longitude,
    required this.title,
    required this.specifications,
    required this.status,
    this.lastReading,
    required this.areaId,
    this.pricePerUnit,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          isSync: isSync,
          isDeleted: isDeleted,
        );

  /// Get the position as a LatLng object
  LatLng get position => LatLng(latitude, longitude);

  /// Create a copy of this entity with the given fields replaced with the new values
  SmartMeter copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSync,
    bool? isDeleted,
    double? latitude,
    double? longitude,
    String? title,
    Map<String, dynamic>? specifications,
    String? status,
    DateTime? lastReading,
    bool clearLastReading = false,
    String? areaId,
    double? pricePerUnit,
    bool clearPricePerUnit = false,
  }) {
    return SmartMeter(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSync: isSync ?? this.isSync,
      isDeleted: isDeleted ?? this.isDeleted,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      title: title ?? this.title,
      specifications: specifications ?? this.specifications,
      status: status ?? this.status,
      lastReading: clearLastReading ? null : (lastReading ?? this.lastReading),
      areaId: areaId ?? this.areaId,
      pricePerUnit: clearPricePerUnit ? null : (pricePerUnit ?? this.pricePerUnit),
    );
  }
}
