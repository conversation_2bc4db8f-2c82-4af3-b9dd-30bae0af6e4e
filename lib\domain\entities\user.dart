import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'base_entity.dart';

/// User entity
class User extends BaseEntity {
  final String name;
  final String? address;
  final String? contact;
  final double? latitude;
  final double? longitude;
  final String? userId;

  const User({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    required bool isSync,
    required bool isDeleted,
    required this.name,
    this.address,
    this.contact,
    this.latitude,
    this.longitude,
    this.userId,
  }) : super(
          id: id,
          createdAt: createdAt,
          updatedAt: updatedAt,
          isSync: isSync,
          isDeleted: isDeleted,
        );

  /// Get the position as a LatLng object if latitude and longitude are available
  LatLng? get position => 
      latitude != null && longitude != null 
          ? LatLng(latitude!, longitude!) 
          : null;

  /// Check if the user has a location
  bool get hasLocation => latitude != null && longitude != null;

  /// Create a copy of this entity with the given fields replaced with the new values
  User copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSync,
    bool? isDeleted,
    String? name,
    String? address,
    bool clearAddress = false,
    String? contact,
    bool clearContact = false,
    double? latitude,
    bool clearLatitude = false,
    double? longitude,
    bool clearLongitude = false,
    String? userId,
  }) {
    return User(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSync: isSync ?? this.isSync,
      isDeleted: isDeleted ?? this.isDeleted,
      name: name ?? this.name,
      userId: userId ?? this.userId,
      address: clearAddress ? null : (address ?? this.address),
      contact: clearContact ? null : (contact ?? this.contact),
      latitude: clearLatitude ? null : (latitude ?? this.latitude),
      longitude: clearLongitude ? null : (longitude ?? this.longitude),
    );
  }
}
