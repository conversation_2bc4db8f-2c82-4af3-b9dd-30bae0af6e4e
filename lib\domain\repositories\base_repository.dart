/// Base repository interface that all repositories should implement
abstract class BaseRepository<T> {
  /// Get all entities
  Future<List<T>> getAll();
  
  /// Get an entity by ID
  Future<T?> getById(String id);
  
  /// Save an entity
  Future<void> save(T entity);
  
  /// Delete an entity
  Future<void> delete(String id, {bool permanent = false});
  
  /// Sync an entity with the remote data source
  Future<void> sync(T entity);
  
  /// Sync all entities with the remote data source
  Future<void> syncAll();
  
  /// Clear all local data and fetch from remote
  Future<void> clearAndFetchAll();
}
