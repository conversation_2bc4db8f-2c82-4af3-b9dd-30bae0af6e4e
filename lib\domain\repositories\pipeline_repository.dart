import '../entities/pipeline.dart';
import 'base_repository.dart';

/// Repository interface for pipelines
abstract class PipelineRepository extends BaseRepository<Pipeline> {
  /// Update the state of a pipeline
  Future<void> updateState(String id, String state);
  
  /// Update the state of a pipeline segment
  Future<void> updateSegmentState(String pipelineId, String segmentId, String state);
  
  /// Add a segment to a pipeline
  Future<void> addSegment(String pipelineId, PipelineSegment segment);
  
  /// Remove a segment from a pipeline
  Future<void> removeSegment(String pipelineId, String segmentId);
  
  /// Get pipelines by type
  Future<List<Pipeline>> getByType(String type);
}
