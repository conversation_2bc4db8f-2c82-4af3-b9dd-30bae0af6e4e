import '../entities/smart_meter.dart';
import 'base_repository.dart';

/// Repository interface for smart meters
abstract class SmartMeterRepository extends BaseRepository<SmartMeter> {
  /// Update the reading of a smart meter
  Future<void> updateReading(String id, double reading);
  
  /// Get all smart meters in a specific area
  Future<List<SmartMeter>> getByArea(String areaId);
  
  /// Update the position of a smart meter
  Future<void> updatePosition(String id, double latitude, double longitude);
}
