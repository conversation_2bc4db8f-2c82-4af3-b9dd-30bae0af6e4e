import '../entities/user.dart';
import 'base_repository.dart';

/// Repository interface for users
abstract class UserRepository extends BaseRepository<User> {
  /// Generate fake users
  Future<List<User>> generateFakeUsers(int count);
  
  /// Get users with location
  Future<List<User>> getUsersWithLocation();
  
  /// Update the position of a user
  Future<void> updatePosition(String id, double latitude, double longitude);
}
