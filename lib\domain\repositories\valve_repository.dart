import '../entities/valve.dart';
import 'base_repository.dart';

/// Repository interface for valves
abstract class ValveRepository extends BaseRepository<Valve> {
  /// Update the status of a valve
  Future<void> updateStatus(String id, String status);
  
  /// Record maintenance for a valve
  Future<void> recordMaintenance(String id);
  
  /// Get all valves in a specific area
  Future<List<Valve>> getByArea(String areaId);
  
  /// Update the position of a valve
  Future<void> updatePosition(String id, double latitude, double longitude);
}
