import '../entities/smart_meter.dart';
import '../repositories/smart_meter_repository.dart';

/// Get all smart meters use case
class GetAllSmartMeters {
  final SmartMeterRepository repository;

  GetAllSmartMeters(this.repository);

  Future<List<SmartMeter>> call() async {
    return await repository.getAll();
  }
}

/// Get smart meter by ID use case
class GetSmartMeterById {
  final SmartMeterRepository repository;

  GetSmartMeterById(this.repository);

  Future<SmartMeter?> call(String id) async {
    return await repository.getById(id);
  }
}

/// Save smart meter use case
class SaveSmartMeter {
  final SmartMeterRepository repository;

  SaveSmartMeter(this.repository);

  Future<void> call(SmartMeter smartMeter) async {
    await repository.save(smartMeter);
  }
}

/// Delete smart meter use case
class DeleteSmartMeter {
  final SmartMeterRepository repository;

  DeleteSmartMeter(this.repository);

  Future<void> call(String id, {bool permanent = false}) async {
    await repository.delete(id, permanent: permanent);
  }
}

/// Update smart meter reading use case
class UpdateSmartMeterReading {
  final SmartMeterRepository repository;

  UpdateSmartMeterReading(this.repository);

  Future<void> call(String id, double reading) async {
    await repository.updateReading(id, reading);
  }
}

/// Update smart meter position use case
class UpdateSmartMeterPosition {
  final SmartMeterRepository repository;

  UpdateSmartMeterPosition(this.repository);

  Future<void> call(String id, double latitude, double longitude) async {
    await repository.updatePosition(id, latitude, longitude);
  }
}

/// Sync smart meter use case
class SyncSmartMeter {
  final SmartMeterRepository repository;

  SyncSmartMeter(this.repository);

  Future<void> call(SmartMeter smartMeter) async {
    await repository.sync(smartMeter);
  }
}

/// Sync all smart meters use case
class SyncAllSmartMeters {
  final SmartMeterRepository repository;

  SyncAllSmartMeters(this.repository);

  Future<void> call() async {
    await repository.syncAll();
  }
}

/// Clear and fetch all smart meters use case
class ClearAndFetchAllSmartMeters {
  final SmartMeterRepository repository;

  ClearAndFetchAllSmartMeters(this.repository);

  Future<void> call() async {
    await repository.clearAndFetchAll();
  }
}
