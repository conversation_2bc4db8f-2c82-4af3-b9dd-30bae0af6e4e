import '../entities/user.dart';
import '../repositories/user_repository.dart';

/// Get all users use case
class GetAllUsers {
  final UserRepository repository;

  GetAllUsers(this.repository);

  Future<List<User>> call() async {
    return await repository.getAll();
  }
}

/// Get user by ID use case
class GetUserById {
  final UserRepository repository;

  GetUserById(this.repository);

  Future<User?> call(String id) async {
    return await repository.getById(id);
  }
}

/// Save user use case
class SaveUser {
  final UserRepository repository;

  SaveUser(this.repository);

  Future<void> call(User user) async {
    await repository.save(user);
  }
}

/// Delete user use case
class DeleteUser {
  final UserRepository repository;

  DeleteUser(this.repository);

  Future<void> call(String id, {bool permanent = false}) async {
    await repository.delete(id, permanent: permanent);
  }
}

/// Generate fake users use case
class GenerateFakeUsers {
  final UserRepository repository;

  GenerateFakeUsers(this.repository);

  Future<List<User>> call(int count) async {
    return await repository.generateFakeUsers(count);
  }
}

/// Get users with location use case
class GetUsersWithLocation {
  final UserRepository repository;

  GetUsersWithLocation(this.repository);

  Future<List<User>> call() async {
    return await repository.getUsersWithLocation();
  }
}

/// Update user position use case
class UpdateUserPosition {
  final UserRepository repository;

  UpdateUserPosition(this.repository);

  Future<void> call(String id, double latitude, double longitude) async {
    await repository.updatePosition(id, latitude, longitude);
  }
}

/// Sync user use case
class SyncUser {
  final UserRepository repository;

  SyncUser(this.repository);

  Future<void> call(User user) async {
    await repository.sync(user);
  }
}

/// Sync all users use case
class SyncAllUsers {
  final UserRepository repository;

  SyncAllUsers(this.repository);

  Future<void> call() async {
    await repository.syncAll();
  }
}

/// Clear and fetch all users use case
class ClearAndFetchAllUsers {
  final UserRepository repository;

  ClearAndFetchAllUsers(this.repository);

  Future<void> call() async {
    await repository.clearAndFetchAll();
  }
}
