import '../entities/valve.dart';
import '../repositories/valve_repository.dart';

/// Get all valves use case
class GetAllValves {
  final ValveRepository repository;

  GetAllValves(this.repository);

  Future<List<Valve>> call() async {
    return await repository.getAll();
  }
}

/// Get valve by ID use case
class GetValveById {
  final ValveRepository repository;

  GetValveById(this.repository);

  Future<Valve?> call(String id) async {
    return await repository.getById(id);
  }
}

/// Save valve use case
class SaveValve {
  final ValveRepository repository;

  SaveValve(this.repository);

  Future<void> call(Valve valve) async {
    await repository.save(valve);
  }
}

/// Delete valve use case
class DeleteValve {
  final ValveRepository repository;

  DeleteValve(this.repository);

  Future<void> call(String id, {bool permanent = false}) async {
    await repository.delete(id, permanent: permanent);
  }
}

/// Update valve status use case
class UpdateValveStatus {
  final ValveRepository repository;

  UpdateValveStatus(this.repository);

  Future<void> call(String id, String status) async {
    await repository.updateStatus(id, status);
  }
}

/// Record valve maintenance use case
class RecordValveMaintenance {
  final ValveRepository repository;

  RecordValveMaintenance(this.repository);

  Future<void> call(String id) async {
    await repository.recordMaintenance(id);
  }
}

/// Update valve position use case
class UpdateValvePosition {
  final ValveRepository repository;

  UpdateValvePosition(this.repository);

  Future<void> call(String id, double latitude, double longitude) async {
    await repository.updatePosition(id, latitude, longitude);
  }
}

/// Sync valve use case
class SyncValve {
  final ValveRepository repository;

  SyncValve(this.repository);

  Future<void> call(Valve valve) async {
    await repository.sync(valve);
  }
}

/// Sync all valves use case
class SyncAllValves {
  final ValveRepository repository;

  SyncAllValves(this.repository);

  Future<void> call() async {
    await repository.syncAll();
  }
}

/// Clear and fetch all valves use case
class ClearAndFetchAllValves {
  final ValveRepository repository;

  ClearAndFetchAllValves(this.repository);

  Future<void> call() async {
    await repository.clearAndFetchAll();
  }
}
