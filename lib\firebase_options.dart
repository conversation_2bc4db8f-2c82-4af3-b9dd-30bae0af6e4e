// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can create a web app in the Firebase Console',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can create an iOS app in the Firebase Console',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can create a macOS app in the Firebase Console',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can create a Windows app in the Firebase Console',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can create a Linux app in the Firebase Console',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // NOTE: You need to replace these values with your actual Firebase configuration
  // from the google-services.json file
  static const FirebaseOptions android = FirebaseOptions(
      apiKey: "AIzaSyB0Ajri9f83Mqr1LL359wS5iPDtPeAjJDw",
      authDomain: "imagetotext-ee218.firebaseapp.com",
      databaseURL: "https://imagetotext-ee218-default-rtdb.asia-southeast1.firebasedatabase.app",
      projectId: "imagetotext-ee218",
      storageBucket: "imagetotext-ee218.appspot.com",
      messagingSenderId: "84357006559",
      appId: "1:84357006559:web:670ae1e7dd55787314956a",
      measurementId: "G-XR5LJVW6VD"
  );
}
