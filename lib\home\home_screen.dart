import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'mobile_map_widget.dart';
import '../users/user_list_screen.dart';
import '../screens/valve_list_screen.dart';
import '../screens/smart_meter_list_screen.dart';
import '../screens/connector_list_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/fake_users_screen.dart';
import '../screens/category_list_screen.dart';
import 'map_state_provider.dart';
import '../services/sync_service.dart'; // For SyncStatus enum
import '../services/interfaces/hive_service_interface.dart';
import '../providers/sync_service_provider.dart';
import '../providers/hive_service_provider.dart';
import '../utils/nepali_names.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  bool _isSyncing = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late final HiveServiceInterface _hiveService;

  @override
  void initState() {
    super.initState();
    // We can't use ref.read in initState, so we'll do it in didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _hiveService = ref.read(hiveServiceProvider);
  }

  @override
  Widget build(BuildContext context) {
    // Get the sync service to check status
    final syncService = ref.read(syncServiceProvider);
    syncService.forceStatusUpdate();
    final directStatus = syncService.status;

    // Determine sync button color based on status
    Color syncButtonColor = Colors.grey;

    // Check for unsynced items
    bool hasUnsynced = false;
    syncService.hasUnsyncedItems().then((value) {
      hasUnsynced = value;
    });

    if (directStatus == SyncStatus.idle && hasUnsynced) {
      syncButtonColor = Colors.orange;
    } else if (directStatus == SyncStatus.partialSync) {
      syncButtonColor = Colors.orange;
    } else if (directStatus == SyncStatus.failed ||
        directStatus == SyncStatus.noConnection) {
      syncButtonColor = Colors.red;
    } else if (directStatus == SyncStatus.upToDate) {
      syncButtonColor = Colors.green;
    } else if (directStatus == SyncStatus.syncing) {
      syncButtonColor = Colors.blue;
    }

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: const Text(NepaliFriendlyNames.appTitle),
        leading: IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () {
            _scaffoldKey.currentState?.openDrawer();
          },
        ),
        actions: [
          // Sync button
          IconButton(
            icon: _isSyncing
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Icon(Icons.sync, color: syncButtonColor),
            tooltip: 'Sync data with server',
            onPressed: _isSyncing ? null : _syncData,
          ),
          // Clear button
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.red),
            tooltip: 'Clear local data and reload from server',
            onPressed: _clearAndReload,
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            const DrawerHeader(
              decoration: BoxDecoration(
                color: Colors.blue,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    NepaliFriendlyNames.appTitle,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'नेभिगेसन मेनु',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.map),
              title: const Text(NepaliFriendlyNames.mapTitle),
              onTap: () {
                Navigator.pop(context); // Close the drawer
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.people),
              title: const Text(NepaliFriendlyNames.userListTitle),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                _navigateToUserList();
              },
            ),
            ListTile(
              leading: const Icon(Icons.group),
              title: const Text('नेपाली प्रयोगकर्ताहरू'),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                _navigateToFakeUsers();
              },
            ),
            ListTile(
              leading: const Icon(Icons.plumbing),
              title: const Text(NepaliFriendlyNames.valveListTitle),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                _navigateToValveList();
              },
            ),
            ListTile(
              leading: const Icon(Icons.speed),
              title: const Text(NepaliFriendlyNames.smartMeterListTitle),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                _navigateToSmartMeterList();
              },
            ),
            ListTile(
              leading: const Icon(Icons.device_hub),
              title: const Text(NepaliFriendlyNames.connectorListTitle),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                _navigateToConnectorList();
              },
            ),
            ListTile(
              leading: const Icon(Icons.category),
              title: const Text(NepaliFriendlyNames.categoryListTitle),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                _navigateToCategoryList();
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text(NepaliFriendlyNames.settingsTitle),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                _navigateToSettings();
              },
            ),
          ],
        ),
      ),
      body: const MobileMapWidget(),
    );
  }

  Future<void> _syncData() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = ref.read(syncServiceProvider);

      // Force a status update before syncing
      syncService.forceStatusUpdate();

      // Check for unsynced items before syncing
      final hasUnsyncedItems = await syncService.hasUnsyncedItems();
      debugPrint('🔄 Has unsynced items before sync: $hasUnsyncedItems');

      // Get unsynced pipelines for debugging
      final unsyncedPipelines = _hiveService.getUnsyncedPipelines();
      debugPrint(
          '🚿 Found ${unsyncedPipelines.length} unsynced pipelines before sync');
      for (final pipeline in unsyncedPipelines) {
        debugPrint(
            '🚿 Unsynced pipeline: ${pipeline.id}, isSync: ${pipeline.isSync}');
      }

      final success = await syncService.syncAllData();

      // Force another status update after syncing
      syncService.forceStatusUpdate();

      // Check for unsynced items after syncing
      final hasUnsyncedItemsAfter = await syncService.hasUnsyncedItems();
      debugPrint('🔄 Has unsynced items after sync: $hasUnsyncedItemsAfter');

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('All data synced successfully')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                  'Sync completed with issues. Some items may not have been synced.'),
              action: SnackBarAction(
                label: 'RETRY',
                onPressed: _syncData,
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Sync error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  Future<void> _clearAndReload() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Local Data'),
        content: const Text(
            'This will clear all local data and reload everything from the server. This operation cannot be undone. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('CLEAR & RELOAD'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      // Use the sync service to clear and fetch all data
      final syncService = ref.read(syncServiceProvider);
      final success = await syncService.clearAndFetchAllData();

      if (!success) {
        debugPrint('Failed to clear and fetch all data from Firestore');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to reload data from server')),
          );
          return;
        }
      }

      debugPrint('Successfully cleared and fetched all data from Firestore');

      // Reload data from local database
      final mapNotifier = ref.read(mapStateProvider.notifier);
      await mapNotifier.loadInitialData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Data cleared and reloaded from server')),
        );
      }
    } catch (e) {
      debugPrint('Error clearing and reloading data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  void _navigateToUserList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const UserListScreen(),
      ),
    );
  }

  void _navigateToValveList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ValveListScreen(),
      ),
    );
  }

  void _navigateToSmartMeterList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SmartMeterListScreen(),
      ),
    );
  }

  void _navigateToConnectorList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ConnectorListScreen(),
      ),
    );
  }

  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  void _navigateToFakeUsers() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FakeUsersScreen(),
      ),
    );
  }

  void _navigateToCategoryList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CategoryListScreen(),
      ),
    );
  }

  // Removed unused _saveAllUsersToUserCollection method
}
