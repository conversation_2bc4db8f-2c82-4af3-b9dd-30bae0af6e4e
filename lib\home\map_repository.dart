import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/pipeline.dart';

class MapRepository {
  final List<Pipeline> pipelines = [];
  final List<UserValvePosition> userValvePositions = [];
  int pipelineCounter = 0;

  void addPipeline(Pipeline pipeline) {
    pipelines.add(pipeline);
  }

  void updatePipeline(String id, Pipeline pipeline) {
    final index = pipelines.indexWhere((p) => p.id == id);
    if (index != -1) {
      pipelines[index] = pipeline;
    }
  }

  void removePipeline(String id) {
    pipelines.removeWhere((p) => p.id == id);
  }

  void addUserValvePosition(UserValvePosition position) {
    userValvePositions.add(position);
  }
}

final mapRepositoryProvider = Provider((ref) => MapRepository());
