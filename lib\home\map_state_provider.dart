import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/pipeline.dart';
import 'package:geolocator/geolocator.dart';
import '../repositories/firestore_repository.dart';
import '../providers/firestore_provider.dart';
import '../widgets/detail_editors/user_details_editor.dart';
import '../widgets/detail_editors/valve_details_editor.dart';
import '../widgets/detail_editors/connector_details_editor.dart';
import '../widgets/detail_editors/pipeline_details_editor.dart';
import '../widgets/detail_editors/smart_meter_details_editor.dart';
import '../models/hive_models/hive_user_model.dart';
import '../models/hive_models/hive_valve_model.dart';
import '../models/hive_models/hive_connector_model.dart';
import '../models/hive_models/hive_pipeline_model.dart';
import '../models/hive_models/hive_smart_meter_model.dart';
import '../services/interfaces/hive_service_interface.dart';
import '../services/interfaces/sync_service_interface.dart';
import '../providers/hive_service_provider.dart';
import '../providers/sync_service_provider.dart';
import '../widgets/user_selection_dialog.dart';

// Class to hold information about a pipeline segment
class _SegmentInfo {
  final String segmentId;
  final String pipelineId;
  final LatLng start;
  final LatLng end;
  final double distance;

  _SegmentInfo({
    required this.segmentId,
    required this.pipelineId,
    required this.start,
    required this.end,
    required this.distance,
  });
}

class MapState {
  final List<Pipeline> pipelines;
  final List<UserValvePosition> userValvePositions;
  final List<LatLng> currentPoints;
  final bool isDrawing;
  final bool addingUser;
  final bool addingValve;
  final bool addingConnector;
  final bool addingThreeConnector;
  final bool addingFourConnector;
  final bool addingReducer;
  final bool addingMeter;
  final BitmapDescriptor userIcon;
  final BitmapDescriptor valveIcon;
  final BitmapDescriptor threeConnectorIcon;
  final BitmapDescriptor fourConnectorIcon;
  final BitmapDescriptor meter;
  final BitmapDescriptor reducer;
  final BitmapDescriptor connectorIcon;
  final bool showMyLocation;
  final LatLng? currentLocation; // Changed from Position? to LatLng?
  final double currentZoom;
  final String? editingPipelineId;
  final GoogleMapController? mapController; // Controller for the Google Map
  final LatLngBounds? currentMapBounds; // Current visible map bounds
  final bool isLoading; // Loading state for database operations
  final String currentAreaId; // Current geographic area ID

  MapState({
    required this.pipelines,
    required this.userValvePositions,
    required this.currentPoints,
    required this.isDrawing,
    required this.addingUser,
    required this.addingValve,
    required this.addingThreeConnector,
    required this.addingFourConnector,
    required this.addingReducer,
    required this.addingMeter,
    required this.addingConnector,
    required this.userIcon,
    required this.valveIcon,
    required this.connectorIcon,
    required this.threeConnectorIcon,
    required this.fourConnectorIcon,
    required this.meter,
    required this.reducer,
    required this.showMyLocation,
    this.currentLocation,
    this.currentZoom = 14.0,
    this.editingPipelineId,
    this.mapController,
    this.currentMapBounds,
    this.isLoading = false,
    this.currentAreaId = 'default',
  });

  MapState copyWith({
    List<Pipeline>? pipelines,
    List<UserValvePosition>? userValvePositions,
    List<LatLng>? currentPoints,
    bool? isDrawing,
    bool? addingUser,
    bool? addingValve,
    bool? addingConnector,
    bool? addingThreeConnector,
    bool? addingFourConnector,
    bool? addingReducer,
    bool? addingMeter,
    BitmapDescriptor? userIcon,
    BitmapDescriptor? valveIcon,
    BitmapDescriptor? connectorIcon,
    BitmapDescriptor? threeConnectorIcon,
    BitmapDescriptor? fourConnectorIcon,
    BitmapDescriptor? reducer,
    BitmapDescriptor? meter,
    bool? showMyLocation,
    LatLng? currentLocation,
    double? currentZoom,
    String? editingPipelineId,
    GoogleMapController? mapController,
    LatLngBounds? currentMapBounds,
    bool? isLoading,
    String? currentAreaId,
  }) {
    return MapState(
      pipelines: pipelines ?? this.pipelines,
      userValvePositions: userValvePositions ?? this.userValvePositions,
      currentPoints: currentPoints ?? this.currentPoints,
      isDrawing: isDrawing ?? this.isDrawing,
      addingUser: addingUser ?? this.addingUser,
      addingValve: addingValve ?? this.addingValve,
      addingThreeConnector: addingThreeConnector ?? this.addingThreeConnector,
      addingFourConnector: addingFourConnector ?? this.addingFourConnector,
      addingReducer: addingReducer ?? this.addingReducer,
      addingMeter: addingMeter ?? this.addingMeter,
      addingConnector: addingConnector ?? this.addingConnector,
      userIcon: userIcon ?? this.userIcon,
      valveIcon: valveIcon ?? this.valveIcon,
      connectorIcon: connectorIcon ?? this.connectorIcon,
      threeConnectorIcon: threeConnectorIcon ?? this.threeConnectorIcon,
      fourConnectorIcon: fourConnectorIcon ?? this.fourConnectorIcon,
      reducer: reducer ?? this.reducer,
      meter: meter ?? this.meter,
      showMyLocation: showMyLocation ?? this.showMyLocation,
      currentLocation: currentLocation ?? this.currentLocation,
      currentZoom: currentZoom ?? this.currentZoom,
      editingPipelineId: editingPipelineId ?? this.editingPipelineId,
      mapController: mapController ?? this.mapController,
      currentMapBounds: currentMapBounds ?? this.currentMapBounds,
      isLoading: isLoading ?? this.isLoading,
      currentAreaId: currentAreaId ?? this.currentAreaId,
    );
  }
}

class MapStateNotifier extends StateNotifier<MapState> {
  final FirestoreRepository _firestoreRepository;
  final HiveServiceInterface _hiveService;
  final Ref? ref;

  // Removed unused _pipelineCounter field
  // Add a constant for snap distance threshold (in meters)
  static const double snapDistanceThreshold =
      10.0; // Adjust this value as needed
  StreamSubscription<Position>? _positionStreamSubscription;
  static const double baseZoom = 14.0; // Base zoom level for default icon size
  static const double minIconSize = 20.0; // Minimum icon size in pixels
  static const double maxIconSize = 100.0; // Maximum icon size in pixels
  static const double defaultIconSize = 60.0; // Default icon size at BASE_ZOOM

  MapStateNotifier(this._firestoreRepository, this._hiveService, {this.ref})
      : super(MapState(
          pipelines: [],
          userValvePositions: [],
          currentPoints: [],
          isDrawing: false,
          addingUser: false,
          addingValve: false,
          addingConnector: false,
          addingReducer: false,
          addingThreeConnector: false,
          addingFourConnector: false,
          addingMeter: false,
          userIcon: BitmapDescriptor.defaultMarker,
          valveIcon: BitmapDescriptor.defaultMarker,
          connectorIcon: BitmapDescriptor.defaultMarker,
          threeConnectorIcon: BitmapDescriptor.defaultMarker,
          fourConnectorIcon: BitmapDescriptor.defaultMarker,
          reducer: BitmapDescriptor.defaultMarker,
          meter: BitmapDescriptor.defaultMarker,
          showMyLocation: true, // Enable location tracking by default
          currentLocation: null,
        )) {
    _loadCustomMarkers();
    // Start location tracking immediately
    _initLocationTracking();
    // Initialize Firestore database
    _initializeFirestore();
  }

  // Initialize Firestore database and load initial data
  Future<void> _initializeFirestore() async {
    try {
      // Initialize database with default area if needed
      await _firestoreRepository.initializeDatabase();

      // Load initial data when the app starts
      await loadInitialData();
    } catch (e) {
      debugPrint('❌ Error initializing Firestore: $e');
    }
  }

  // Force a refresh from Firestore - useful when the app is reinstalled
  Future<bool> forceRefreshFromFirestore() async {
    try {
      debugPrint('🔄 Forcing refresh from Firestore in MapStateNotifier...');
      state = state.copyWith(isLoading: true);

      // Get the SyncService instance
      final syncService = ref?.read(syncServiceProvider);

      if (syncService == null) {
        debugPrint('❌ SyncService is null, cannot force refresh');
        state = state.copyWith(isLoading: false);
        return false;
      }

      // Force a refresh from Firestore by clearing and fetching all data
      final success = await syncService.clearAndFetchAllData();

      if (success) {
        debugPrint('✅ Successfully refreshed data from Firestore');

        // Reload data from local database
        await loadInitialData();
        return true;
      } else {
        debugPrint('❌ Failed to refresh data from Firestore');
        state = state.copyWith(isLoading: false);
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error forcing refresh from Firestore: $e');
      state = state.copyWith(isLoading: false);
      return false;
    }
  }

  // Load initial data from local database and only fetch from Firestore if database is empty (fresh installation)
  Future<void> loadInitialData() async {
    try {
      debugPrint('🔄 Starting to load initial data...');
      state = state.copyWith(isLoading: true);

      // Get the SyncService instance from the provider
      final syncService = ref?.read(syncServiceProvider);

      bool fetchedFromFirestore = false;
      bool isEmptyDatabase = _hiveService.isDatabaseEmpty();

      debugPrint(
          '📊 Database status: ${isEmptyDatabase ? "Empty" : "Has data"}');

      // Only fetch from Firestore if the database is empty (fresh installation)
      if (isEmptyDatabase && syncService != null) {
        debugPrint(
            '🔄 Database is empty (fresh installation), fetching data from Firestore...');

        // Check if we should fetch data from Firestore (will only return true if database is empty)
        final shouldFetch = syncService.shouldFetchFromFirestore();

        if (shouldFetch) {
          debugPrint('🔄 Should fetch data from Firestore, starting fetch...');

          // Fetch all data from Firestore
          final success = await syncService.fetchAllData();
          if (!success) {
            debugPrint(
                '❌ Failed to fetch data from Firestore, will use local data if available');
            // Continue with loading from local database
          } else {
            debugPrint('✅ Successfully fetched data from Firestore');
            fetchedFromFirestore = true;
          }
        } else {
          debugPrint('ℹ️ Using local data only (no connectivity)');
        }
      } else {
        debugPrint('🔄 Database is not empty, using local data only');
        debugPrint(
            '🔄 Data will only be synced when the sync button is pressed');
      }

      // Check if database is still empty after fetch attempts
      if (_hiveService.isDatabaseEmpty() && fetchedFromFirestore) {
        debugPrint('⚠️ Database is still empty after fetching from Firestore!');
        debugPrint(
            '🔍 This might indicate an issue with the Firestore data or connectivity');
      }

      // Load data from local Hive database
      debugPrint('🔄 Loading data from local Hive database...');
      final List<UserValvePosition> userValvePositions = [];
      final List<Pipeline> pipelines = [];

      // Load users from Hive
      final users = _hiveService.getAllUsers();
      debugPrint('👤 Found ${users.length} users in Hive');
      for (final user in users) {
        // Create position directly from latitude and longitude
        final position = LatLng(user.latitude, user.longitude);
        debugPrint(
            '🔍 Loading user ${user.id} with position (${user.latitude}, ${user.longitude})');

        userValvePositions.add(UserValvePosition(
          id: user.id,
          position: position,
          type: PositionType.user,
          title: user.title,
        ));
      }

      // Load valves from Hive
      final valves = _hiveService.getAllValves();
      debugPrint('🔧 Found ${valves.length} valves in Hive');
      for (final valve in valves) {
        // Create position directly from latitude and longitude
        final position = LatLng(valve.latitude, valve.longitude);
        debugPrint(
            '🔍 Loading valve ${valve.id} with position (${valve.latitude}, ${valve.longitude})');

        userValvePositions.add(UserValvePosition(
          id: valve.id,
          position: position,
          type: PositionType.valve,
          title: valve.title,
        ));
      }

      // Load connectors from Hive
      final connectors = _hiveService.getAllConnectors();
      debugPrint('🔌 Found ${connectors.length} connectors in Hive');
      for (final connector in connectors) {
        // Create position directly from latitude and longitude
        final position = LatLng(connector.latitude, connector.longitude);
        debugPrint(
            '🔍 Loading connector ${connector.id} with position (${connector.latitude}, ${connector.longitude})');

        userValvePositions.add(UserValvePosition(
          id: connector.id,
          position: position,
          type: PositionType.connector,
          title: connector.title,
          connectorType: connector.connectorType,
        ));
      }

      // Load smart meters from Hive
      final smartMeters = _hiveService.getAllSmartMeters();
      debugPrint('📊 Found ${smartMeters.length} smart meters in Hive');
      for (final smartMeter in smartMeters) {
        // Create position directly from latitude and longitude
        final position = LatLng(smartMeter.latitude, smartMeter.longitude);
        debugPrint(
            '🔍 Loading smart meter ${smartMeter.id} with position (${smartMeter.latitude}, ${smartMeter.longitude})');

        userValvePositions.add(UserValvePosition(
          id: smartMeter.id,
          position: position,
          type: PositionType.meter,
          title: smartMeter.title,
        ));
      }

      // Load pipelines from Hive
      final hivePipelines = _hiveService.getAllPipelines();
      debugPrint('🚿 Found ${hivePipelines.length} pipelines in Hive');

      for (final pipeline in hivePipelines) {
        try {
          // Get the pipeline state
          final state = pipeline.properties['state'] ?? 'Active';

          // Get segment states from properties
          final segmentStates = pipeline.getSegmentStates();

          // Create segments map with states
          final Map<String, PipelineSegment> segments = {};

          // Create segments for each segment in the pipeline
          for (int i = 0; i < pipeline.points.length - 1; i++) {
            final start = pipeline.latLngPoints[i];
            final end = pipeline.latLngPoints[i + 1];
            final segmentId = generateSegmentId(start, end);

            // Get state for this segment (default to pipeline state)
            final segmentState = segmentStates[segmentId] ?? state;

            segments[segmentId] = PipelineSegment(
              id: segmentId,
              start: start,
              end: end,
              state: segmentState,
            );
          }

          pipelines.add(Pipeline(
            id: pipeline.id,
            points: pipeline.latLngPoints,
            segmentId: pipeline.segmentId,
            segments: segments,
            state: state,
          ));
        } catch (e) {
          debugPrint('❌ Error processing pipeline ${pipeline.id}: $e');
        }
      }

      // Update state with loaded data
      state = state.copyWith(
        pipelines: pipelines,
        userValvePositions: userValvePositions,
        isLoading: false,
      );

      // If we still have no data after all attempts, show a warning
      if (userValvePositions.isEmpty && pipelines.isEmpty) {
        debugPrint(
            '⚠️ WARNING: No data loaded from either Firestore or local database!');
        debugPrint(
            '🔍 This might indicate an issue with the Firestore data or connectivity');
      } else {
        debugPrint(
            '✅ Successfully loaded ${pipelines.length} pipelines and ${userValvePositions.length} markers from local database');
      }
    } catch (e) {
      debugPrint('❌ Error loading initial data: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  // Initialize location tracking
  Future<void> _initLocationTracking() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return;
    }

    // Start location updates
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    ).listen((Position position) {
      // Convert Position to LatLng
      state = state.copyWith(
        currentLocation: LatLng(position.latitude, position.longitude),
      );

      // Update the current area ID based on the new location
      _updateCurrentAreaId();
    });
  }

  void updateZoom(double newZoom) {
    state = state.copyWith(currentZoom: newZoom);
    _updateIconsForZoom(newZoom);
  }

  void _updateIconsForZoom(double zoom) {
    // Calculate size factor based on zoom difference
    double zoomDiff = zoom - baseZoom;
    // Change to make icons grow with zoom (use 2.0 for more dramatic scaling)
    double scaleFactor = pow(2.0, zoomDiff).toDouble();

    // Start with BASE size and scale up/down from there
    double newSize = defaultIconSize * scaleFactor;
    // Clamp size between min and max
    newSize = newSize.clamp(minIconSize, maxIconSize);

    // Update user icon
    BitmapDescriptor.asset(
      //ImageConfiguration(size: Size(newSize, newSize)),
      const ImageConfiguration(),
      "assets/icons/ic_person.webp",
    ).then((icon) {
      state = state.copyWith(userIcon: icon);
    });

    // Update valve icon
    BitmapDescriptor.asset(
      //ImageConfiguration(size: Size(newSize, newSize)),
      const ImageConfiguration(size: Size(24, 24)),
      "assets/icons/valve.webp",
    ).then((icon) {
      state = state.copyWith(valveIcon: icon);
    });

    BitmapDescriptor.asset(
      //ImageConfiguration(size: Size(newSize, newSize)),
      const ImageConfiguration(),
      "assets/icons/two_connector.webp",
    ).then((icon) {
      state = state.copyWith(connectorIcon: icon);
    });

    BitmapDescriptor.asset(
      //ImageConfiguration(size: Size(newSize, newSize)),
      const ImageConfiguration(),
      "assets/icons/three_connector.webp",
    ).then((icon) {
      state = state.copyWith(threeConnectorIcon: icon);
    });

    BitmapDescriptor.asset(
      //ImageConfiguration(size: Size(newSize, newSize)),
      const ImageConfiguration(),
      "assets/icons/two_connector.webp",
    ).then((icon) {
      state = state.copyWith(connectorIcon: icon);
    });

    BitmapDescriptor.asset(
      //ImageConfiguration(size: Size(newSize, newSize)),
      const ImageConfiguration(),
      "assets/icons/reducer.webp",
    ).then((icon) {
      state = state.copyWith(reducer: icon);
    });

    BitmapDescriptor.asset(
      //ImageConfiguration(size: Size(newSize, newSize)),
      const ImageConfiguration(),
      "assets/icons/meter.webp",
    ).then((icon) {
      state = state.copyWith(meter: icon);
    });
  }

  void _loadCustomMarkers() {
    _updateIconsForZoom(state.currentZoom);
  }

  Future<void> addUserValvePosition(LatLng point,
      {BuildContext? context}) async {
    if (state.addingUser ||
        state.addingValve ||
        state.addingConnector ||
        state.addingMeter) {
      String id;
      int type;
      String title;
      int? connectorType;

      if (state.addingUser) {
        // When adding a user directly from the map, we don't have a user ID from the user collection
        // So we generate a new one
        id = 'user_${DateTime.now().millisecondsSinceEpoch}';
        type = PositionType.user;
        title = 'User Location';

        // Check if this user ID already exists on the map
        final userExists = state.userValvePositions.any((position) =>
            position.id == id && position.type == PositionType.user);

        if (userExists) {
          // User is already on the map, show error message and return
          if (context != null && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'This user is already on the map. Delete it first if you want to reposition.'),
                duration: Duration(seconds: 3),
              ),
            );
          }
          debugPrint('User $id is already on the map. Skipping addition.');
          return;
        }
      } else if (state.addingValve) {
        id = 'valve_${DateTime.now().millisecondsSinceEpoch}';
        type = PositionType.valve;
        title = 'Valve Location';
      } else if (state.addingMeter) {
        id = 'meter_${DateTime.now().millisecondsSinceEpoch}';
        type = PositionType.meter;
        title = 'Smart Meter';
      } else {
        // Adding connector
        id = 'connector_${DateTime.now().millisecondsSinceEpoch}';
        type = PositionType.connector;
        connectorType = _selectedConnectorType;
        title = connectorType != null
            ? ConnectorType.getName(connectorType)
            : 'Connector';
      }

      final position = UserValvePosition(
        id: id,
        position: point,
        type: type,
        title: title,
        connectorType: connectorType,
      );

      try {
        // First update UI immediately (optimistic update)
        state = state.copyWith(
          userValvePositions: [...state.userValvePositions, position],
          isLoading: true,
        );

        // Save to local storage first
        switch (position.type) {
          case PositionType.user:
            // Ensure we have only the minimal required fields for elements collection
            final enhancedDetails = {
              'name': position.title,
              'userId':
                  id, // Include the user ID for reference when clicking on the map
              // No metadata as per requirement
            };

            final userModel = HiveUserModel.fromPosition(
              position: position.position,
              title: position.title,
              areaId: state.currentAreaId,
            );
            userModel.id = id;
            userModel.isSync = false; // Explicitly mark as not synced
            await _hiveService.saveUser(userModel);
            break;
          case PositionType.valve:
            final valveModel = HiveValveModel.fromPosition(
              position: position.position,
              title: position.title,
              areaId: '1', // Use a default area or determine dynamically
            );
            valveModel.id = id;
            await _hiveService.saveValve(valveModel);
            break;
          case PositionType.meter:
            final smartMeterModel = HiveSmartMeterModel.fromPosition(
              position: position.position,
              title: position.title,
              areaId: 'default', // Use a default area or determine dynamically
            );
            smartMeterModel.id = id;
            await _hiveService.saveSmartMeter(smartMeterModel);
            break;
          case PositionType.connector:
            final connectorModel = HiveConnectorModel.fromPosition(
              position: position.position,
              title: position.title,
              connectorType: position.connectorType ?? ConnectorType.tee,
              areaId: 'default', // Use a default area or determine dynamically
            );
            connectorModel.id = id;
            await _hiveService.saveConnector(connectorModel);
            break;
        }

        // Update loading state
        state = state.copyWith(isLoading: false);

        // Don't sync automatically - only save locally
        // Items will be synced when the user presses the sync button
        debugPrint('Item saved locally and waiting for manual sync');
      } catch (e) {
        // On error, revert the change
        debugPrint('Error adding position: $e');
        state = state.copyWith(
          userValvePositions: state.userValvePositions
              .where((p) => p.id != position.id)
              .toList(),
          isLoading: false,
        );
      }
    }
  }

  void startNewPipeline() async {
    await Future.delayed(const Duration(microseconds: 500));
    state = state.copyWith(
      isDrawing: true,
      currentPoints: [],
      editingPipelineId: null,
    );
  }

  // Helper method to calculate distance between two points
  double calculateDistance(LatLng point1, LatLng point2) {
    const double radius = 6371e3; // Earth's radius in meters
    double lat1 = point1.latitude * pi / 180;
    double lat2 = point2.latitude * pi / 180;
    double dLat = (point2.latitude - point1.latitude) * pi / 180;
    double dLon = (point2.longitude - point1.longitude) * pi / 180;

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return radius * c;
  }

  // Removed unused helper methods

  void addPoint(LatLng point) {
    if (state.isDrawing) {
      state = state.copyWith(
        currentPoints: [...state.currentPoints, point],
      );
    }
  }

  Future<void> onMapTap(LatLng point, {BuildContext? context}) async {
    if (state.isDrawing) {
      addPoint(point);
    } else if (state.addingMeter) {
      // For smart meters, find the closest pipeline segment
      final closestSegment = _findClosestPipelineSegment(point);
      if (closestSegment != null) {
        // If we found a close pipeline segment, add the meter at the exact tap location
        await addUserValvePosition(point, context: context);
      } else {
        // If no pipeline segment is close enough, just add at the tap location
        await addUserValvePosition(point, context: context);
      }
    } else if (state.addingUser) {
      // If context is provided, show the user selection dialog
      if (context != null) {
        // Show the user selection dialog
        await showDialog(
          context: context,
          builder: (context) => UserSelectionDialog(position: point),
        );
      } else {
        // If no context is provided, fall back to the old behavior
        await addUserValvePosition(point, context: context);
      }
    } else if (state.addingValve || state.addingConnector) {
      await addUserValvePosition(point, context: context);
    }
  }

  // Add a user with specific details
  Future<void> addUserWithDetails(
      LatLng point, String title, Map<String, dynamic> details,
      {BuildContext? context}) async {
    try {
      // Use the provided user ID from the user collection if available, otherwise generate a new one
      final id =
          details['userId'] ?? 'user_${DateTime.now().millisecondsSinceEpoch}';

      // Check if this user is already on the map
      final userExists = state.userValvePositions.any((position) =>
          position.id == id && position.type == PositionType.user);

      if (userExists) {
        // User is already on the map, show error message and return
        if (context != null && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'This user is already on the map. Delete it first if you want to reposition.'),
              duration: Duration(seconds: 3),
            ),
          );
        }
        debugPrint('User $id is already on the map. Skipping addition.');
        return;
      }

      // Create the user position
      final position = UserValvePosition(
        id: id,
        position: point,
        type: PositionType.user,
        title: title,
      );

      // First update UI immediately (optimistic update)
      state = state.copyWith(
        userValvePositions: [...state.userValvePositions, position],
        isLoading: true,
      );

      // Ensure details contains only the minimal required fields for elements collection
      final enhancedDetails = {
        ...details,
        'name': title,
        'userId':
            id, // Include the user ID for reference when clicking on the map
        // No metadata as per requirement
      };

      // Save to local storage only with minimal data
      // The user will be synced to Firestore when the sync button is pressed
      final userModel = HiveUserModel(
        id: id,
        latitude: point.latitude,
        longitude: point.longitude,
        title: title,
        areaId: state.currentAreaId,
        isSync: false, // Explicitly mark as not synced
      );

      await _hiveService.saveUser(userModel);

      // Update state to reflect successful save
      state = state.copyWith(isLoading: false);
      debugPrint('User saved locally with details: $enhancedDetails');
      debugPrint(
          'User will be synced to Firestore when sync button is pressed');
    } catch (e) {
      debugPrint('Error adding user with details: $e');
      // On error, revert the UI change
      state = state.copyWith(
        userValvePositions: state.userValvePositions
            .where(
                (p) => p.id != 'user_${DateTime.now().millisecondsSinceEpoch}')
            .toList(),
        isLoading: false,
      );
    }
  }

  // Helper method to find the closest pipeline segment to a point
  _SegmentInfo? _findClosestPipelineSegment(LatLng point) {
    double minDistance = snapDistanceThreshold;
    _SegmentInfo? closestSegment;

    for (final pipeline in state.pipelines) {
      for (int i = 0; i < pipeline.points.length - 1; i++) {
        final start = pipeline.points[i];
        final end = pipeline.points[i + 1];

        // Calculate distance from point to line segment
        final distance = _distanceToSegment(point, start, end);

        if (distance < minDistance) {
          minDistance = distance;
          final segmentId = generateSegmentId(start, end);
          closestSegment = _SegmentInfo(
              segmentId: segmentId,
              start: start,
              end: end,
              distance: distance,
              pipelineId: pipeline.id);
        }
      }
    }

    return closestSegment;
  }

  // Calculate distance from a point to a line segment
  double _distanceToSegment(LatLng point, LatLng start, LatLng end) {
    // Convert to cartesian coordinates for simplicity
    // This is an approximation that works for small distances
    const earthRadius = 6371000.0; // meters

    // Convert to radians
    final lat1 = start.latitude * pi / 180;
    final lon1 = start.longitude * pi / 180;
    final lat2 = end.latitude * pi / 180;
    final lon2 = end.longitude * pi / 180;
    final latP = point.latitude * pi / 180;
    final lonP = point.longitude * pi / 180;

    // Convert to cartesian coordinates
    final x1 = earthRadius * cos(lat1) * cos(lon1);
    final y1 = earthRadius * cos(lat1) * sin(lon1);
    final z1 = earthRadius * sin(lat1);

    final x2 = earthRadius * cos(lat2) * cos(lon2);
    final y2 = earthRadius * cos(lat2) * sin(lon2);
    final z2 = earthRadius * sin(lat2);

    final xP = earthRadius * cos(latP) * cos(lonP);
    final yP = earthRadius * cos(latP) * sin(lonP);
    final zP = earthRadius * sin(latP);

    // Calculate vectors
    final dx = x2 - x1;
    final dy = y2 - y1;
    final dz = z2 - z1;

    // Calculate squared length of segment
    final segmentLengthSquared = dx * dx + dy * dy + dz * dz;

    if (segmentLengthSquared == 0) {
      // Start and end points are the same
      return sqrt(pow(xP - x1, 2) + pow(yP - y1, 2) + pow(zP - z1, 2));
    }

    // Calculate projection of point onto segment
    final t = ((xP - x1) * dx + (yP - y1) * dy + (zP - z1) * dz) /
        segmentLengthSquared;

    if (t < 0) {
      // Point is closest to start point
      return sqrt(pow(xP - x1, 2) + pow(yP - y1, 2) + pow(zP - z1, 2));
    } else if (t > 1) {
      // Point is closest to end point
      return sqrt(pow(xP - x2, 2) + pow(yP - y2, 2) + pow(zP - z2, 2));
    } else {
      // Point is closest to segment
      final projX = x1 + t * dx;
      final projY = y1 + t * dy;
      final projZ = z1 + t * dz;

      return sqrt(pow(xP - projX, 2) + pow(yP - projY, 2) + pow(zP - projZ, 2));
    }
  }

  // Renamed from addValveConnection to addConnection to be more generic
  void addConnection(LatLng position) {
    if (state.isDrawing) {
      state = state.copyWith(
        currentPoints: [...state.currentPoints, position],
      );
    }
  }

  Future<void> completePipeline() async {
    if (state.currentPoints.length >= 2) {
      // Set loading state
      state = state.copyWith(isLoading: true);

      final points = List<LatLng>.from(state.currentPoints);
      final segmentId = generateSegmentId(points[0], points[1]);

      final pipelineId = state.editingPipelineId ??
          'pipeline_${DateTime.now().millisecondsSinceEpoch}';

      final newPipeline = Pipeline(
        id: pipelineId,
        points: points,
        segmentId: segmentId,
      );

      try {
        // Save to local database first
        if (state.editingPipelineId != null) {
          // Update existing pipeline in local database
          final pipelineModel = HivePipelineModel.fromPipeline(
            pipeline: newPipeline,
            areaId: 'default', // Use a default area or determine dynamically
            isSync: false, // Explicitly mark as not synced in the constructor
            properties: {
              'state': 'Active',
              'material': 'Standard',
              'pipeType': 'Standard',
              'diameter': 100,
              'pressure': 'Medium',
            },
          );
          await _hiveService.savePipeline(pipelineModel);
          debugPrint(
              'Updated pipeline $pipelineId in local database (marked as not synced)');
        } else {
          // Add new pipeline to local database
          final pipelineModel = HivePipelineModel.fromPipeline(
            pipeline: newPipeline,
            areaId: 'default', // Use a default area or determine dynamically
            isSync: false, // Explicitly mark as not synced in the constructor
            properties: {
              'state': 'Active',
              'material': 'Standard',
              'pipeType': 'Standard',
              'diameter': 100,
              'pressure': 'Medium',
            },
          );
          await _hiveService.savePipeline(pipelineModel);
          debugPrint(
              'Saved new pipeline $pipelineId to local database (marked as not synced)');
        }

        // Update local state
        final updatedPipelines = List<Pipeline>.from(state.pipelines);
        if (state.editingPipelineId != null) {
          final index = updatedPipelines
              .indexWhere((p) => p.id == state.editingPipelineId);
          if (index != -1) {
            updatedPipelines[index] = newPipeline;
          }
        } else {
          updatedPipelines.add(newPipeline);
        }

        state = state.copyWith(
          pipelines: updatedPipelines,
          currentPoints: [],
          isDrawing: false,
          editingPipelineId: null,
          isLoading: false,
        );

        // Don't sync to Firestore - wait for sync button press
        debugPrint('Pipeline saved locally and waiting for manual sync');

        // Verify the pipeline is marked as not synced
        final savedPipeline = _hiveService.getPipeline(pipelineId);
        if (savedPipeline != null) {
          debugPrint(
              'Saved pipeline sync status: ${savedPipeline.isSync ? "Synced" : "Not synced"}');
          debugPrint('Saved pipeline properties: ${savedPipeline.properties}');
        } else {
          debugPrint('Warning: Could not retrieve saved pipeline from Hive');
        }
      } catch (e) {
        debugPrint('Error saving pipeline: $e');
        state = state.copyWith(isLoading: false);
      }
    }
  }

  void toggleUserMode() async {
    if (!state.isDrawing) {
      await Future.delayed(const Duration(microseconds: 500));
      state = state.copyWith(
        addingUser: !state.addingUser,
        addingValve: false,
        addingMeter: false,
        addingConnector: false,
      );
    }
  }

  void toggleValveMode() async {
    if (!state.isDrawing) {
      await Future.delayed(const Duration(microseconds: 500));
      state = state.copyWith(
        addingValve: !state.addingValve,
        addingUser: false,
        addingMeter: false,
        addingConnector: false,
      );
    }
  }

  void toggleMeterMode() async {
    if (!state.isDrawing) {
      await Future.delayed(const Duration(microseconds: 500));
      state = state.copyWith(
        addingMeter: !state.addingMeter,
        addingUser: false,
        addingValve: false,
        addingConnector: false,
      );
    }
  }

  Future<void> deleteMarker(UserValvePosition position,
      {BuildContext? context}) async {
    // Set loading state
    state = state.copyWith(isLoading: true);

    try {
      // Delete from local storage - mark as deleted but don't sync to Firestore
      switch (position.type) {
        case PositionType.user:
          await _hiveService.deleteUser(position.id);
          break;
        case PositionType.valve:
          await _hiveService.deleteValve(position.id);
          break;
        case PositionType.connector:
          await _hiveService.deleteConnector(position.id);
          break;
        case PositionType.meter:
          await _hiveService.deleteSmartMeter(position.id);
          break;
      }

      // Update local state immediately
      state = state.copyWith(
        userValvePositions: state.userValvePositions
            .where((marker) => marker.id != position.id)
            .toList(),
        isLoading: false,
      );

      // Don't delete from Firestore immediately - wait for sync button press
      debugPrint(
          'Marker marked as deleted locally and will be synced when sync button is pressed');

      // Show success message if context is provided
      if (context != null && context.mounted) {
        final messenger = ScaffoldMessenger.of(context);
        messenger.showSnackBar(
          SnackBar(content: Text('${position.title} deleted successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error deleting marker: $e');
      state = state.copyWith(isLoading: false);

      // Show error message if context is provided
      if (context != null && context.mounted) {
        final messenger = ScaffoldMessenger.of(context);
        messenger.showSnackBar(
          SnackBar(content: Text('Error deleting ${position.title}: $e')),
        );
      }
    }
  }

  void showMarkerOptions(BuildContext context, UserValvePosition position) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Wrap(
        children: [
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('View Info'),
            onTap: () {
              Navigator.pop(context);
              showInfoDialog(context, position);
            },
          ),
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Edit Details'),
            onTap: () {
              Navigator.pop(context);
              showDetailsEditor(context, position);
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Delete Marker',
                style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              confirmDeleteMarker(context, position);
            },
          ),
        ],
      ),
    );
  }

  void showInfoDialog(BuildContext context, UserValvePosition position) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Marker Info'),
        content: Text(
          'Location: ${position.position.latitude}, ${position.position.longitude}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void showDetailsEditor(BuildContext context, UserValvePosition position) {
    // Import the appropriate editor based on position type
    switch (position.type) {
      case PositionType.user:
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => UserDetailsEditor(
              userId: position.id,
              title: position.title,
              onSaved: () {
                // Refresh data after saving
                loadInitialData();
              },
            ),
          ),
        );
        break;
      case PositionType.valve:
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ValveDetailsEditor(
              valveId: position.id,
              title: position.title,
              onSaved: () {
                // Refresh data after saving
                loadInitialData();
              },
            ),
          ),
        );
        break;
      case PositionType.connector:
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ConnectorDetailsEditor(
              connectorId: position.id,
              title: position.title,
              onSaved: () {
                // Refresh data after saving
                loadInitialData();
              },
            ),
          ),
        );
        break;
      case PositionType.meter:
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => SmartMeterDetailsEditor(
              position: position,
            ),
          ),
        );
        break;
      default:
        // Show a simple info dialog if type is unknown
        showInfoDialog(context, position);
    }
  }

  void confirmDeleteMarker(BuildContext context, UserValvePosition position) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Marker'),
        content: Text('Are you sure you want to delete ${position.title}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(dialogContext);
              // Pass the context to show feedback
              deleteMarker(position, context: context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  void cancelPipeline() {
    state = state.copyWith(
      currentPoints: [],
      isDrawing: false,
      editingPipelineId: null,
    );
  }

  Future<void> deletePipeline(String segmentId) async {
    // Set loading state
    state = state.copyWith(isLoading: true);

    final updatedPipelines = <Pipeline>[];
    Pipeline? pipelineToDelete;

    for (final pipeline in state.pipelines) {
      final points = List<LatLng>.from(pipeline.points);
      bool segmentFoundInPipeline = false;

      // Check each segment in the pipeline
      for (int i = 0; i < points.length - 1; i++) {
        final currentSegmentId = generateSegmentId(points[i], points[i + 1]);

        if (currentSegmentId == segmentId) {
          segmentFoundInPipeline = true;

          // If pipeline has only two points (one segment), delete the entire pipeline
          if (points.length <= 2) {
            pipelineToDelete = pipeline;
            break;
          }

          // Create new points list without the deleted segment
          final newPoints = <LatLng>[];
          for (int j = 0; j < points.length; j++) {
            if (j != i && j != (i + 1)) {
              newPoints.add(points[j]);
            }
          }

          // Only add pipeline if it has at least 2 points remaining
          if (newPoints.length >= 2) {
            final updatedPipeline = Pipeline(
                id: pipeline.id,
                points: newPoints,
                segmentId: generateSegmentId(newPoints[0], newPoints[1]));

            updatedPipelines.add(updatedPipeline);

            // Update in local database first
            try {
              final pipelineModel = HivePipelineModel.fromPipeline(
                pipeline: updatedPipeline,
                areaId:
                    'default', // Use a default area or determine dynamically
              );
              await _hiveService.savePipeline(pipelineModel);
              debugPrint('Updated pipeline ${pipeline.id} in local database');
            } catch (e) {
              debugPrint('Error updating pipeline in local database: $e');
            }
          } else {
            // If less than 2 points remaining, delete the pipeline
            pipelineToDelete = pipeline;
          }
          break;
        }
      }
      // If no segment was found in this pipeline, keep it unchanged
      if (!segmentFoundInPipeline) {
        updatedPipelines.add(pipeline);
      }
    }

    // Delete pipeline from local database if needed
    if (pipelineToDelete != null) {
      try {
        await _hiveService.deletePipeline(pipelineToDelete.id);
        debugPrint(
            'Deleted pipeline ${pipelineToDelete.id} from local database');
      } catch (e) {
        debugPrint('Error deleting pipeline from local database: $e');
      }
    }

    state = state.copyWith(
      pipelines: updatedPipelines,
      currentPoints: [],
      isDrawing: false,
      editingPipelineId: null,
      isLoading: false,
    );
  }

  void editPipeline(String id) {
    final pipeline = state.pipelines.firstWhere((p) => p.id == id);
    state = state.copyWith(
      editingPipelineId: id,
      currentPoints: List.from(pipeline.points),
      isDrawing: true,
      addingUser: false,
      addingValve: false,
    );
  }

  void cancelEditing() {
    state = state.copyWith(
      editingPipelineId: null,
      currentPoints: [],
      isDrawing: false,
    );
  }

  Future<void> undoLastPoint() async {
    if (state.currentPoints.isNotEmpty) {
      // If we're drawing a pipeline, remove the last point
      final updatedPoints = List<LatLng>.from(state.currentPoints)
        ..removeLast();
      state = state.copyWith(
        currentPoints: updatedPoints,
        isDrawing: updatedPoints.isNotEmpty ? state.isDrawing : false,
        addingUser: false,
        addingValve: false,
        addingConnector: false,
      );
    } else if (state.addingUser ||
        state.addingValve ||
        state.addingConnector ||
        state.addingMeter) {
      // Set loading state
      state = state.copyWith(isLoading: true);

      // If we're in user/valve/connector/meter mode, find the last added marker of that type
      final markers = List<UserValvePosition>.from(state.userValvePositions);

      // Determine the marker type based on the current mode
      int markerType;
      if (state.addingUser) {
        markerType = PositionType.user;
      } else if (state.addingValve) {
        markerType = PositionType.valve;
      } else if (state.addingMeter) {
        markerType = PositionType.meter;
      } else {
        // addingConnector
        markerType = PositionType.connector;
      }

      // Find the last marker of the current type
      UserValvePosition? markerToRemove;
      for (int i = markers.length - 1; i >= 0; i--) {
        if (markers[i].type == markerType) {
          // For connectors, also check the connector type if we have a selected type
          if (markerType == PositionType.connector &&
              _selectedConnectorType != null) {
            // Only remove if it matches the selected connector type
            if (markers[i].connectorType == _selectedConnectorType) {
              markerToRemove = markers[i];
              markers.removeAt(i);
              break;
            }
          } else {
            // For users and valves, or if no specific connector type is selected
            markerToRemove = markers[i];
            markers.removeAt(i);
            break;
          }
        }
      }

      // If we found a marker to remove, delete it from local database
      if (markerToRemove != null) {
        try {
          // Delete from local database based on marker type
          switch (markerToRemove.type) {
            case PositionType.user:
              await _hiveService.deleteUser(markerToRemove.id);
              break;
            case PositionType.valve:
              await _hiveService.deleteValve(markerToRemove.id);
              break;
            case PositionType.connector:
              await _hiveService.deleteConnector(markerToRemove.id);
              break;
            case PositionType.meter:
              await _hiveService.deleteSmartMeter(markerToRemove.id);
              break;
          }
          debugPrint('Deleted marker ${markerToRemove.id} from local database');
        } catch (e) {
          debugPrint('Error deleting marker: $e');
        }
      }

      state = state.copyWith(userValvePositions: markers, isLoading: false);
    }
  }

  void showPipelineOptions(BuildContext context, String segmentId,
      {String? segmentState}) {
    // If adding meter mode is active, we don't show pipeline options
    if (state.addingMeter) {
      return;
    }

    // Only show options if no adding mode is active
    if (!state.addingUser &&
        !state.addingValve &&
        !state.addingConnector &&
        !state.isDrawing) {
      // Find the pipeline that contains this segment
      Pipeline? pipeline;
      try {
        pipeline = state.pipelines.firstWhere((p) => p.points.any((point) {
              final index = p.points.indexOf(point);
              return index < p.points.length - 1 &&
                  generateSegmentId(point, p.points[index + 1]) == segmentId;
            }));
      } catch (e) {
        debugPrint('Pipeline for segment $segmentId not found: $e');
      }

      // Get the segment state if not provided
      final currentState = segmentState ?? pipeline?.state ?? 'Active';

      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        builder: (context) => ListView(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          children: [
            // Show current segment state with color
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Pipeline.getColorForState(currentState),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Current State: $currentState',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.edit, color: Colors.blue, size: 28),
              title: const Text('Edit Pipeline Path'),
              subtitle: const Text('Modify this pipeline\'s path'),
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              onTap: () {
                Navigator.pop(context);
                if (pipeline != null) {
                  editPipeline(pipeline.id);
                }
              },
            ),
            ListTile(
              leading:
                  const Icon(Icons.edit_note, color: Colors.orange, size: 28),
              title: const Text('Edit Pipeline Details'),
              subtitle: const Text('Edit properties and metadata'),
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              onTap: () {
                Navigator.pop(context);
                if (pipeline != null) {
                  showPipelineDetailsEditor(context, pipeline.id);
                }
              },
            ),
            // Add segment state options
            ListTile(
              leading:
                  const Icon(Icons.color_lens, color: Colors.purple, size: 28),
              title: const Text('Change Segment State'),
              subtitle: const Text('Update this segment\'s state'),
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              onTap: () {
                Navigator.pop(context);
                if (pipeline != null) {
                  showSegmentStateOptions(
                      context, pipeline.id, segmentId, currentState);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.format_paint,
                  color: Colors.deepPurple, size: 28),
              title: const Text('Change Pipeline State'),
              subtitle: const Text('Update the entire pipeline\'s state'),
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              onTap: () {
                Navigator.pop(context);
                if (pipeline != null) {
                  showPipelineStateOptions(
                      context, pipeline.id, pipeline.state);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.info, color: Colors.green, size: 28),
              title: const Text('View Info'),
              subtitle: const Text('See pipeline details'),
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              onTap: () {
                Navigator.pop(context);
                if (pipeline != null) {
                  showPipelineInfo(context, pipeline.id);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red, size: 28),
              title: const Text('Delete Pipeline'),
              subtitle: const Text('Remove this pipeline segment'),
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              onTap: () {
                Navigator.pop(context);
                confirmDeletePipeline(context, segmentId);
              },
            ),
          ],
        ),
      );
    }
  }

  // Show options to change segment state
  void showSegmentStateOptions(BuildContext context, String pipelineId,
      String segmentId, String currentState) {
    final states = [
      'Active',
      'Inactive',
      'Under Maintenance',
      'Planned',
      'Decommissioned',
      'Warning',
      'Critical',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Segment State'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: states.length,
            itemBuilder: (context, index) {
              final state = states[index];
              final isSelected = state == currentState;

              return ListTile(
                leading: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Pipeline.getColorForState(state),
                    shape: BoxShape.circle,
                  ),
                ),
                title: Text(state),
                trailing: isSelected
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  Navigator.pop(context);
                  updateSegmentState(pipelineId, segmentId, state);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }

  // Show options to change the entire pipeline state
  void showPipelineStateOptions(
      BuildContext context, String pipelineId, String currentState) {
    final states = [
      'Active',
      'Inactive',
      'Under Maintenance',
      'Planned',
      'Decommissioned',
      'Warning',
      'Critical',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Pipeline State'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: states.length,
            itemBuilder: (context, index) {
              final state = states[index];
              final isSelected = state == currentState;

              return ListTile(
                leading: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Pipeline.getColorForState(state),
                    shape: BoxShape.circle,
                  ),
                ),
                title: Text(state),
                trailing: isSelected
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  Navigator.pop(context);
                  updatePipelineState(pipelineId, state);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }

  // Update segment state in Firestore and local database
  Future<void> updateSegmentState(
      String pipelineId, String segmentId, String newState) async {
    try {
      state = state.copyWith(isLoading: true);

      // Update in Firestore
      final firestoreRepository = ref?.read(firestoreRepositoryProvider);
      if (firestoreRepository != null) {
        await firestoreRepository.updatePipelineSegmentState(
            pipelineId, segmentId, newState);
      }

      // Update in local database
      final hivePipeline = _hiveService.getPipeline(pipelineId);
      if (hivePipeline != null) {
        // Update the properties to include the segment state
        final properties = Map<String, dynamic>.from(hivePipeline.properties);
        properties['segmentStates'] =
            properties['segmentStates'] ?? <String, String>{};
        final segmentStates =
            Map<String, String>.from(properties['segmentStates']);
        segmentStates[segmentId] = newState;
        properties['segmentStates'] = segmentStates;

        // Save the updated pipeline
        hivePipeline.properties = properties;
        hivePipeline.markAsUpdated();
        // Save the changes to the box
        await _hiveService.savePipeline(hivePipeline);
      }

      // Update the UI
      final updatedPipelines = state.pipelines.map((pipeline) {
        if (pipeline.id == pipelineId) {
          // Create a copy of the segments map
          final updatedSegments =
              Map<String, PipelineSegment>.from(pipeline.segments);

          // Find the segment and update its state
          for (int i = 0; i < pipeline.points.length - 1; i++) {
            final start = pipeline.points[i];
            final end = pipeline.points[i + 1];
            final currentSegmentId = generateSegmentId(start, end);

            if (currentSegmentId == segmentId) {
              updatedSegments[segmentId] = PipelineSegment(
                id: segmentId,
                start: start,
                end: end,
                state: newState,
              );
              break;
            }
          }

          // Return updated pipeline
          return Pipeline(
            id: pipeline.id,
            points: pipeline.points,
            segmentId: pipeline.segmentId,
            segments: updatedSegments,
            state: pipeline.state,
          );
        }
        return pipeline;
      }).toList();

      // Force UI refresh by setting loading to true briefly
      state = state.copyWith(isLoading: true);

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 50));

      // Update the state with the new pipelines list
      state = state.copyWith(
        pipelines: updatedPipelines,
        isLoading: false,
      );

      debugPrint('Updated segment $segmentId state to $newState');
    } catch (e) {
      debugPrint('Error updating segment state: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  // Update entire pipeline state in Firestore and local database
  Future<void> updatePipelineState(String pipelineId, String newState) async {
    try {
      state = state.copyWith(isLoading: true);

      debugPrint('🔄 Updating pipeline $pipelineId state to $newState');

      // Update in local database first
      final hivePipeline = _hiveService.getPipeline(pipelineId);
      if (hivePipeline != null) {
        // Update the properties to include the pipeline state
        final properties = Map<String, dynamic>.from(hivePipeline.properties);
        properties['state'] = newState;

        debugPrint('🔄 Setting pipeline state in properties to: $newState');
        debugPrint('🔄 Properties before update: ${hivePipeline.properties}');

        // Save the updated pipeline
        hivePipeline.properties = properties;
        hivePipeline.markAsUpdated(); // Mark as not synced

        debugPrint('🔄 Properties after update: ${hivePipeline.properties}');

        // Save the changes to the box
        await _hiveService.savePipeline(hivePipeline);

        // Verify the update
        final verifiedPipeline = _hiveService.getPipeline(pipelineId);
        if (verifiedPipeline != null) {
          debugPrint(
              '✅ Verified pipeline state in local database: ${verifiedPipeline.properties['state']}');
          debugPrint(
              '✅ Sync status: ${verifiedPipeline.isSync ? "Synced" : "Not synced"}');
        }
      } else {
        debugPrint('❌ Pipeline not found in Hive: $pipelineId');
      }

      // Update in Firestore directly to ensure immediate update
      final firestoreRepository = ref?.read(firestoreRepositoryProvider);
      if (firestoreRepository != null) {
        // Update the pipeline state in Firestore
        debugPrint('🔄 Updating pipeline state in Firestore to: $newState');
        await firestoreRepository.updatePipelineState(pipelineId, newState);
        debugPrint('✅ Pipeline state updated in Firestore');
      }

      // Update the UI
      final updatedPipelines = state.pipelines.map((pipeline) {
        if (pipeline.id == pipelineId) {
          // Create a new Pipeline instance with the updated state
          // Also update all segments to use the new state if they don't have a specific state
          final updatedSegments =
              Map<String, PipelineSegment>.from(pipeline.segments);

          // Update segments that use the pipeline state
          for (int i = 0; i < pipeline.points.length - 1; i++) {
            final start = pipeline.points[i];
            final end = pipeline.points[i + 1];
            final currentSegmentId = generateSegmentId(start, end);

            // If segment exists and uses the pipeline state, update it
            if (updatedSegments.containsKey(currentSegmentId)) {
              final segment = updatedSegments[currentSegmentId]!;
              if (segment.state == pipeline.state) {
                // This segment was using the pipeline state, so update it
                updatedSegments[currentSegmentId] = PipelineSegment(
                  id: segment.id,
                  start: segment.start,
                  end: segment.end,
                  state: newState,
                );
              }
            } else {
              // If segment doesn't exist, create it with the new state
              updatedSegments[currentSegmentId] = PipelineSegment(
                id: currentSegmentId,
                start: start,
                end: end,
                state: newState,
              );
            }
          }

          final updatedPipeline = Pipeline(
            id: pipeline.id,
            points: pipeline.points,
            segmentId: pipeline.segmentId,
            segments: updatedSegments,
            state: newState,
          );

          debugPrint(
              '✅ Created updated pipeline with state: ${updatedPipeline.state}');
          return updatedPipeline;
        }
        return pipeline;
      }).toList();

      // Force UI refresh by setting loading to true briefly
      state = state.copyWith(isLoading: true);

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 50));

      // Update the state with the new pipelines list
      state = state.copyWith(
        pipelines: updatedPipelines,
        isLoading: false,
      );

      debugPrint('✅ Updated pipeline $pipelineId state to $newState');
    } catch (e) {
      debugPrint('❌ Error updating pipeline state: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  void confirmDeletePipeline(BuildContext context, String segmentId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Pipeline'),
        content: const Text('Are you sure you want to delete this pipeline?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              deletePipeline(segmentId);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  void showPipelineDetailsEditor(BuildContext context, String pipelineId) {
    // Get the pipeline title
    final title = 'Pipeline ${pipelineId.split('_').last}';

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PipelineDetailsEditor(
          pipelineId: pipelineId,
          title: title,
          onSaved: () {
            // Refresh data after saving
            loadInitialData();
          },
        ),
      ),
    );
  }

  void showPipelineInfo(BuildContext context, String pipelineId) {
    final pipeline = state.pipelines.firstWhere((p) => p.id == pipelineId);
    final startPoint = pipeline.points.first;
    final endPoint = pipeline.points.last;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pipeline Info'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Pipeline ID: $pipelineId'),
            const SizedBox(height: 8),
            Text(
                'Start: (${startPoint.latitude.toStringAsFixed(6)}, ${startPoint.longitude.toStringAsFixed(6)})'),
            const SizedBox(height: 4),
            Text(
                'End: (${endPoint.latitude.toStringAsFixed(6)}, ${endPoint.longitude.toStringAsFixed(6)})'),
            const SizedBox(height: 8),
            Text('Total points: ${pipeline.points.length}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // Track the currently selected connector type
  int? _selectedConnectorType;

  int? get selectedConnectorType => _selectedConnectorType;

  void toggleConnectorMode() async {
    if (!state.isDrawing) {
      await Future.delayed(const Duration(microseconds: 500));

      // If turning off connector mode, also reset the selected connector type
      if (state.addingConnector) {
        _selectedConnectorType = null;
      }

      state = state.copyWith(
        addingConnector: !state.addingConnector,
        addingUser: false,
        addingValve: false,
        addingMeter: false,
      );
    }
  }

  void showConnectorTypeOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      builder: (context) => ListView(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(vertical: 20),
        children: [
          const Padding(
            padding: EdgeInsets.only(bottom: 16.0),
            child: Text(
              'Select Connector Type',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          ListTile(
            leading: Icon(ConnectorType.getIcon(ConnectorType.tee),
                color: Colors.blue, size: 28),
            title: const Text('Connector'),
            subtitle: const Text('Two-Way connection'),
            contentPadding: const EdgeInsets.symmetric(horizontal: 24),
            onTap: () {
              _selectedConnectorType = ConnectorType.tee;
              state = state.copyWith(addingConnector: true);
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Icon(ConnectorType.getIcon(ConnectorType.tee),
                color: Colors.blue, size: 28),
            title: const Text('Tee Connector'),
            subtitle: const Text('Three-way connection'),
            contentPadding: const EdgeInsets.symmetric(horizontal: 24),
            onTap: () {
              _selectedConnectorType = ConnectorType.tee;
              state = state.copyWith(addingConnector: true);
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Icon(ConnectorType.getIcon(ConnectorType.elbow),
                color: Colors.blue, size: 28),
            title: const Text('Four-Way Connector'),
            subtitle: const Text('Four-way Connector'),
            contentPadding: const EdgeInsets.symmetric(horizontal: 24),
            onTap: () {
              _selectedConnectorType = ConnectorType.elbow;
              state = state.copyWith(addingFourConnector: true);
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Icon(ConnectorType.getIcon(ConnectorType.coupling),
                color: Colors.blue, size: 28),
            title: const Text('Coupling Connector'),
            subtitle: const Text('Straight connection'),
            contentPadding: const EdgeInsets.symmetric(horizontal: 24),
            onTap: () {
              _selectedConnectorType = ConnectorType.coupling;
              state = state.copyWith(addingConnector: true);
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Icon(ConnectorType.getIcon(ConnectorType.cross),
                color: Colors.blue, size: 28),
            title: const Text('Cross Connector'),
            subtitle: const Text('Four-way connection'),
            contentPadding: const EdgeInsets.symmetric(horizontal: 24),
            onTap: () {
              _selectedConnectorType = ConnectorType.cross;
              state = state.copyWith(addingConnector: true);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  // Helper function to generate consistent segment IDs
  String generateSegmentId(LatLng start, LatLng end) {
    // Ensure consistent segment ID regardless of point order
    final startStr = '${start.latitude},${start.longitude}';
    final endStr = '${end.latitude},${end.longitude}';

    if (startStr.compareTo(endStr) > 0) {
      return 'segment_${end.latitude}_${end.longitude}_${start.latitude}_${start.longitude}';
    }
    return 'segment_${start.latitude}_${start.longitude}_${end.latitude}_${end.longitude}';
  }

  // Get current location and return it as LatLng
  Future<LatLng?> getCurrentLocation() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return null;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return null;
    }

    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      return LatLng(position.latitude, position.longitude);
    } catch (e) {
      // Silently handle error and return null
      return null;
    }
  }

  // Set the Google Map controller
  void setMapController(GoogleMapController controller) {
    state = state.copyWith(mapController: controller);
  }

  // This method is already defined elsewhere in the file

  Future<LatLngBounds?> getBoundsFromVisibleRegion() async {
    if (state.mapController == null) return null;

    try {
      final visibleRegion = await state.mapController!.getVisibleRegion();
      return LatLngBounds(
        southwest: visibleRegion.southwest,
        northeast: visibleRegion.northeast,
      );
    } catch (e) {
      // Silently handle error
      return null;
    }
  }

  void updateMapBounds(LatLngBounds bounds) {
    state = state.copyWith(currentMapBounds: bounds);
    // Update the current area ID based on the map center
    _updateCurrentAreaId();
  }

  // Update the current area ID based on the map center
  Future<void> _updateCurrentAreaId() async {
    if (state.currentLocation != null) {
      try {
        // Get the area ID for the current location
        final areaId = await _firestoreRepository
            .getAreaForPosition(state.currentLocation!);
        // Update state with the new area ID
        state = state.copyWith(currentAreaId: areaId);
      } catch (e) {
        debugPrint('Error updating current area ID: $e');
      }
    }
  }

  // Update the title of a marker in the UI
  void updateMarkerTitle(String markerId, String newTitle) {
    final updatedPositions = state.userValvePositions.map((position) {
      if (position.id == markerId) {
        return UserValvePosition(
          id: position.id,
          position: position.position,
          type: position.type,
          title: newTitle,
          connectorType: position.connectorType,
        );
      }
      return position;
    }).toList();

    state = state.copyWith(userValvePositions: updatedPositions);
  }

  // Use current location based on active mode
  Future<void> useCurrentLocation({BuildContext? context}) async {
    LatLng? currentLocation = await getCurrentLocation();
    if (currentLocation == null) return;

    // Update state with current location
    state = state.copyWith(currentLocation: currentLocation);

    // Update the current area ID based on the new location
    await _updateCurrentAreaId();

    // Move camera to current location if controller is available
    if (state.mapController != null) {
      state.mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          currentLocation,
          state.currentZoom,
        ),
      );
    }

    // Use current location based on active mode
    if (state.isDrawing) {
      // Add point to pipeline
      addPoint(currentLocation);
      // Show visual feedback (optional)
      // You could add a temporary marker or animation here
    } else if (state.addingUser ||
        state.addingValve ||
        state.addingConnector ||
        state.addingMeter) {
      // Store the context locally to avoid using it across async gaps
      final BuildContext? localContext = context;

      // Add user, valve, connector, or smart meter at current location
      if (localContext != null && localContext.mounted) {
        addUserValvePosition(currentLocation, context: localContext);
      } else {
        addUserValvePosition(currentLocation);
      }
    }
  }

  // Toggle visibility of the location indicator on the map
  void toggleLocationVisibility() {
    state = state.copyWith(showMyLocation: !state.showMyLocation);
  }

  // Center the map on a specific position
  void centerOnPosition(LatLng position) {
    if (state.mapController != null) {
      state.mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          position,
          state.currentZoom,
        ),
      );
    }
  }

  // Update valve position after dragging
  Future<void> updateValvePosition(String valveId, LatLng newPosition,
      {BuildContext? context}) async {
    try {
      // Set loading state
      state = state.copyWith(isLoading: true);

      // Find the valve in local state
      final valveIndex = state.userValvePositions.indexWhere((position) =>
          position.id == valveId && position.type == PositionType.valve);

      if (valveIndex == -1) {
        debugPrint('❌ Valve with ID $valveId not found');
        state = state.copyWith(isLoading: false);
        return;
      }

      // Get the valve from Hive
      final valve = _hiveService.getValvesBox().get(valveId);
      if (valve == null) {
        debugPrint('❌ Valve with ID $valveId not found in Hive');
        state = state.copyWith(isLoading: false);
        return;
      }

      // Debug log before updating
      debugPrint(
          '🔍 BEFORE UPDATE: Valve ${valve.id} position in Hive: lat=${valve.latitude}, lng=${valve.longitude}');
      debugPrint(
          '🔍 NEW POSITION: lat=${newPosition.latitude}, lng=${newPosition.longitude}');

      // Create a new valve model with the updated position
      // This approach avoids any potential issues with the Hive object
      final updatedValveModel = HiveValveModel(
        id: valve.id,
        latitude: newPosition.latitude,
        longitude: newPosition.longitude,
        title: valve.title,
        size: valve.size,
        controlType: valve.controlType,
        type: valve.type,
        areaId: valve.areaId,
        createdAt: valve.createdAt,
        updatedAt: DateTime.now(),
        isSync: false, // Explicitly mark as not synced
        isDeleted: valve.isDeleted,
        status: valve.status,
        lastMaintenance: valve.lastMaintenance,
      );

      // Debug log after creating the new model
      debugPrint(
          '🔍 NEW MODEL: Valve ${updatedValveModel.id} position: lat=${updatedValveModel.latitude}, lng=${updatedValveModel.longitude}');

      // Explicitly mark as updated to ensure it's flagged for sync
      updatedValveModel.markAsUpdated();

      // Explicitly save the valve to ensure changes are persisted
      await _hiveService.saveValve(updatedValveModel);

      // Debug log after saving
      debugPrint(
          '🔍 AFTER SAVE CALL: Valve ${updatedValveModel.id} position should be saved now');

      // Verify the update by retrieving the valve again from Hive
      final verifiedValve = _hiveService.getValvesBox().get(valveId);
      if (verifiedValve != null) {
        debugPrint(
            '✅ VERIFIED: Valve ${verifiedValve.id} position in Hive: lat=${verifiedValve.latitude}, lng=${verifiedValve.longitude}');
      } else {
        debugPrint(
            '❌ ERROR: Could not retrieve valve $valveId from Hive after saving');
      }

      // Update valve position in local state
      final updatedPositions =
          List<UserValvePosition>.from(state.userValvePositions);
      updatedPositions[valveIndex] = UserValvePosition(
        id: valveId,
        position: newPosition,
        type: PositionType.valve,
        title: updatedPositions[valveIndex].title,
      );

      // Update state
      state = state.copyWith(
        userValvePositions: updatedPositions,
        isLoading: false,
      );

      // Show success message if context is provided
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Valve position updated successfully')),
        );
      }

      debugPrint('✅ Valve position updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating valve position: $e');
      state = state.copyWith(isLoading: false);

      // Show error message if context is provided
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating valve position: $e')),
        );
      }
    }
  }

  // Update user position after dragging
  Future<void> updateUserPosition(String userId, LatLng newPosition,
      {BuildContext? context}) async {
    try {
      // Set loading state
      state = state.copyWith(isLoading: true);

      // Find the user in local state
      final userIndex = state.userValvePositions.indexWhere((position) =>
          position.id == userId && position.type == PositionType.user);

      if (userIndex == -1) {
        debugPrint('❌ User with ID $userId not found');
        state = state.copyWith(isLoading: false);
        return;
      }

      // Get the user from Hive
      final user = _hiveService.getUsersBox().get(userId);
      if (user == null) {
        debugPrint('❌ User with ID $userId not found in Hive');
        state = state.copyWith(isLoading: false);
        return;
      }

      // Debug log before updating
      debugPrint(
          '🔍 BEFORE UPDATE: User ${user.id} position in Hive: lat=${user.latitude}, lng=${user.longitude}');
      debugPrint(
          '🔍 NEW POSITION: lat=${newPosition.latitude}, lng=${newPosition.longitude}');

      // Create a new user model with the updated position
      // This approach avoids any potential issues with the Hive object
      final updatedUserModel = HiveUserModel(
        id: user.id,
        latitude: newPosition.latitude,
        longitude: newPosition.longitude,
        title: user.title,
        areaId: user.areaId,
        createdAt: user.createdAt,
        updatedAt: DateTime.now(),
        isSync: false, // Explicitly mark as not synced
        isDeleted: user.isDeleted,
        connections: user.connections,
        address: user.address,
        contact: user.contact,
        userId: user.id,
        name: user.name
      );

      // Debug log after creating the new model
      debugPrint(
          '🔍 NEW MODEL: User ${updatedUserModel.id} position: lat=${updatedUserModel.latitude}, lng=${updatedUserModel.longitude}');

      // Explicitly mark as updated to ensure it's flagged for sync
      updatedUserModel.markAsUpdated();

      // Explicitly save the user to ensure changes are persisted
      await _hiveService.saveUser(updatedUserModel);

      // Debug log after saving
      debugPrint(
          '🔍 AFTER SAVE CALL: User ${updatedUserModel.id} position should be saved now');

      // Verify the update by retrieving the user again from Hive
      final verifiedUser = _hiveService.getUsersBox().get(userId);
      if (verifiedUser != null) {
        debugPrint(
            '✅ VERIFIED: User ${verifiedUser.id} position in Hive: lat=${verifiedUser.latitude}, lng=${verifiedUser.longitude}');
      } else {
        debugPrint(
            '❌ ERROR: Could not retrieve user $userId from Hive after saving');
      }

      // Update user position in local state
      final updatedPositions =
          List<UserValvePosition>.from(state.userValvePositions);
      updatedPositions[userIndex] = UserValvePosition(
        id: userId,
        position: newPosition,
        type: PositionType.user,
        title: updatedPositions[userIndex].title,
      );

      // Update state
      state = state.copyWith(
        userValvePositions: updatedPositions,
        isLoading: false,
      );

      // Show success message if context is provided
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('User position updated successfully')),
        );
      }

      debugPrint('✅ User position updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating user position: $e');
      state = state.copyWith(isLoading: false);

      // Show error message if context is provided
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating user position: $e')),
        );
      }
    }
  }

  // Update smart meter position after dragging
  Future<void> updateSmartMeterPosition(String meterId, LatLng newPosition,
      {BuildContext? context}) async {
    try {
      // Set loading state
      state = state.copyWith(isLoading: true);

      // Find the smart meter in local state
      final meterIndex = state.userValvePositions.indexWhere((position) =>
          position.id == meterId && position.type == PositionType.meter);

      if (meterIndex == -1) {
        debugPrint('❌ Smart meter with ID $meterId not found');
        state = state.copyWith(isLoading: false);
        return;
      }

      // Get the smart meter from Hive
      final smartMeter = _hiveService.getSmartMetersBox().get(meterId);
      if (smartMeter == null) {
        debugPrint('❌ Smart meter with ID $meterId not found in Hive');
        state = state.copyWith(isLoading: false);
        return;
      }

      // Debug log before updating
      debugPrint(
          '🔍 BEFORE UPDATE: Smart meter ${smartMeter.id} position in Hive: lat=${smartMeter.latitude}, lng=${smartMeter.longitude}');
      debugPrint(
          '🔍 NEW POSITION: lat=${newPosition.latitude}, lng=${newPosition.longitude}');

      // Create a new smart meter model with the updated position
      // This approach avoids any potential issues with the Hive object
      final updatedMeterModel = HiveSmartMeterModel(
        id: smartMeter.id,
        latitude: newPosition.latitude,
        longitude: newPosition.longitude,
        title: smartMeter.title,
        areaId: smartMeter.areaId,
        createdAt: smartMeter.createdAt,
        updatedAt: DateTime.now(),
        isSync: false, // Explicitly mark as not synced
        isDeleted: smartMeter.isDeleted,
        status: smartMeter.status,
        lastReading: smartMeter.lastReading,
      );

      // Debug log after creating the new model
      debugPrint(
          '🔍 NEW MODEL: Smart meter ${updatedMeterModel.id} position: lat=${updatedMeterModel.latitude}, lng=${updatedMeterModel.longitude}');

      // Explicitly mark as updated to ensure it's flagged for sync
      updatedMeterModel.markAsUpdated();

      // Explicitly save the smart meter to ensure changes are persisted
      await _hiveService.saveSmartMeter(updatedMeterModel);

      // Debug log after saving
      debugPrint(
          '🔍 AFTER SAVE CALL: Smart meter ${updatedMeterModel.id} position should be saved now');

      // Double-check that the position was actually updated in Hive
      final verifiedMeter = _hiveService.getSmartMetersBox().get(meterId);
      if (verifiedMeter != null) {
        debugPrint(
            '✅ VERIFIED: Smart meter ${verifiedMeter.id} position in Hive after flush: lat=${verifiedMeter.latitude}, lng=${verifiedMeter.longitude}');
      } else {
        debugPrint(
            '❌ ERROR: Could not retrieve smart meter $meterId from Hive after saving');
      }

      // Update smart meter position in local state
      final updatedPositions =
          List<UserValvePosition>.from(state.userValvePositions);
      updatedPositions[meterIndex] = UserValvePosition(
        id: meterId,
        position: newPosition,
        type: PositionType.meter,
        title: updatedPositions[meterIndex].title,
      );

      // Update state
      state = state.copyWith(
        userValvePositions: updatedPositions,
        isLoading: false,
      );

      // Show success message if context is provided
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Smart meter position updated successfully')),
        );
      }

      debugPrint('✅ Smart meter position updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating smart meter position: $e');
      state = state.copyWith(isLoading: false);

      // Show error message if context is provided
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating smart meter position: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _positionStreamSubscription?.cancel();
    super.dispose();
  }
}

final mapStateProvider =
    StateNotifierProvider<MapStateNotifier, MapState>((ref) {
  return MapStateNotifier(
    ref.read(firestoreRepositoryProvider),
    ref.read(hiveServiceProvider),
    ref: ref,
  );
});
