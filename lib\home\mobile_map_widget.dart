import 'dart:math';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'map_state_provider.dart';
import '../models/pipeline.dart';

class MobileMapWidget extends HookConsumerWidget {
  final CameraPosition initialPosition;

  const MobileMapWidget({
    Key? key,
    this.initialPosition = const CameraPosition(
      target: LatLng(27.7120, 85.2994),
      zoom: 14,
    ),
  }) : super(key: key);

  // Check if map bounds have changed significantly to reload data
  bool _isBoundsChangedSignificantly(
      LatLngBounds oldBounds, LatLngBounds newBounds) {
    // Calculate the center points of both bounds
    final oldCenter = LatLng(
      (oldBounds.northeast.latitude + oldBounds.southwest.latitude) / 2,
      (oldBounds.northeast.longitude + oldBounds.southwest.longitude) / 2,
    );

    final newCenter = LatLng(
      (newBounds.northeast.latitude + newBounds.southwest.latitude) / 2,
      (newBounds.northeast.longitude + newBounds.southwest.longitude) / 2,
    );

    // Calculate the distance between centers
    final distance = _calculateDistance(oldCenter, newCenter);

    // Calculate the diagonal distance of the old bounds (as a reference)
    final oldDiagonal = _calculateDistance(
      oldBounds.northeast,
      oldBounds.southwest,
    );
    // If the center has moved more than 50% of the diagonal, consider it significant
    return distance > (oldDiagonal * 0.5);
  }

  // Calculate distance between two points in meters
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // in meters
    final lat1 = point1.latitude * pi / 180;
    final lat2 = point2.latitude * pi / 180;
    final dLat = (point2.latitude - point1.latitude) * pi / 180;
    final dLon = (point2.longitude - point1.longitude) * pi / 180;

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mapState = ref.watch(mapStateProvider);
    final mapNotifier = ref.read(mapStateProvider.notifier);

    Set<Marker> getMarkers() {
      return mapState.userValvePositions.map((position) {
        // Determine the icon based on the position type
        BitmapDescriptor icon;
        switch (position.type) {
          case PositionType.user:
            icon = mapState.userIcon;
            break;
          case PositionType.valve:
            icon = mapState.valveIcon;
            break;
          case PositionType.meter:
            icon = mapState.meter;
            break;
          case PositionType.connector:
          default:
            // For connectors, we use the connector icon
            icon = mapState.connectorIcon;
            break;
        }

        // Make valve, user, and smart meter markers draggable
        final bool isDraggable = position.type == PositionType.valve ||
            position.type == PositionType.user ||
            position.type == PositionType.meter;

        return Marker(
          markerId: MarkerId(position.id),
          position: position.position,
          icon: icon,
          draggable: isDraggable,
          onDragEnd: isDraggable
              ? (newPosition) async {
                  if (position.type == PositionType.valve) {
                    await mapNotifier.updateValvePosition(
                      position.id,
                      newPosition,
                      context: context,
                    );
                  } else if (position.type == PositionType.user) {
                    await mapNotifier.updateUserPosition(
                      position.id,
                      newPosition,
                      context: context,
                    );
                  } else if (position.type == PositionType.meter) {
                    await mapNotifier.updateSmartMeterPosition(
                      position.id,
                      newPosition,
                      context: context,
                    );
                  }
                }
              : null,
          infoWindow: InfoWindow(
            title: position.title,
            snippet: mapState.isDrawing
                ? 'Click to connect pipeline'
                : isDraggable
                    ? 'Long press to drag'
                    : null,
          ),
          onTap: () {
            if (!mapState.isDrawing) {
              mapNotifier.showMarkerOptions(context, position);
            } else {
              // Allow connecting to both users and valves during drawing
              mapNotifier.addConnection(position.position);
            }
          },
        );
      }).toSet();
    }

    Set<Polyline> getPolylines() {
      final Set<Polyline> polylines = {};

      // Draw existing pipelines
      for (final pipeline in mapState.pipelines) {
        // Get the overall pipeline state
        final pipelineState = pipeline.state;

        for (int i = 0; i < pipeline.points.length - 1; i++) {
          final start = pipeline.points[i];
          final end = pipeline.points[i + 1];
          final segmentId = mapNotifier.generateSegmentId(start, end);

          // Get segment state if available, otherwise use pipeline state
          String segmentState = pipelineState;
          if (pipeline.segments.containsKey(segmentId)) {
            segmentState = pipeline.segments[segmentId]!.state;
          }

          // Get color based on state
          Color segmentColor = pipeline.id == mapState.editingPipelineId
              ? Colors.red
              : Pipeline.getColorForState(segmentState);

          polylines.add(Polyline(
            polylineId: PolylineId(segmentId),
            points: [start, end],
            color: segmentColor,
            width: 12,
            // Disable tap events when drawing or adding meter
            consumeTapEvents: !mapState.isDrawing && !mapState.addingMeter,
            onTap: (mapState.isDrawing || mapState.addingMeter)
                ? null
                : () {
                    mapNotifier.showPipelineOptions(context, segmentId,
                        segmentState: segmentState);
                  },
          ));
        }
      }

      // Show current drawing line
      if (mapState.isDrawing && mapState.currentPoints.isNotEmpty) {
        polylines.add(Polyline(
          polylineId: const PolylineId('current_line'),
          points: mapState.currentPoints,
          color: Colors.red,
          width: 12,
          consumeTapEvents: false,
        ));
      }

      return polylines;
    }

    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: initialPosition,
                  polylines: getPolylines(),
                  markers: getMarkers(), // User, valve, and connector markers
                  onTap: (point) async {
                    await mapNotifier.onMapTap(point, context: context);
                  },
                  onCameraMove: (CameraPosition position) {
                    mapNotifier.updateZoom(position.zoom);
                  },
                  onMapCreated: (GoogleMapController controller) {
                    mapNotifier.setMapController(controller);
                  },
                  onCameraIdle: () async {
                    if (mapState.mapController != null) {
                      final bounds =
                          await mapNotifier.getBoundsFromVisibleRegion();
                      if (bounds != null) {
                        mapNotifier.updateMapBounds(bounds);

                        // Reload data when map bounds change significantly
                        if (mapState.currentMapBounds != null &&
                            _isBoundsChangedSignificantly(
                                mapState.currentMapBounds!, bounds)) {
                          await mapNotifier.loadInitialData();
                        }
                      }
                    }
                  },
                  myLocationEnabled: mapState.showMyLocation,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: false,
                ),
                // Loading indicator
                if (mapState.isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
              ],
            ),
          ),
          Container(
            height: 70,
            color: Colors.white,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                child: Row(
                  children: [
                    if (!mapState.isDrawing)
                      GestureDetector(
                        onTap: () => mapNotifier.startNewPipeline(),
                        child: Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Tooltip(
                            message: 'Start New Pipeline',
                            child: Icon(Icons.line_axis, color: Colors.white),
                          ),
                        ),
                      ),
                    if (mapState.isDrawing)
                      GestureDetector(
                        onTap: () => mapNotifier.completePipeline(),
                        child: Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Tooltip(
                            message: 'Finish Pipeline',
                            child: Icon(Icons.check, color: Colors.white),
                          ),
                        ),
                      ),
                    const SizedBox(width: 10),
                    GestureDetector(
                      onTap: () => mapNotifier.toggleUserMode(),
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color:
                              mapState.addingUser ? Colors.green : Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Tooltip(
                          message: 'Add User',
                          child: Icon(Icons.person_2_outlined,
                              color: Colors.white),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    GestureDetector(
                      onTap: () => mapNotifier.toggleValveMode(),
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color:
                              mapState.addingValve ? Colors.green : Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Tooltip(
                          message: 'Add Valve',
                          child: Icon(Icons.plumbing_outlined,
                              color: Colors.white),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    GestureDetector(
                      onTap: () {
                        if (!mapState.addingConnector) {
                          // Show connector type options when not already in connector mode
                          mapNotifier.showConnectorTypeOptions(context);
                        } else {
                          // Toggle off if already in connector mode
                          mapNotifier.toggleConnectorMode();
                        }
                      },
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: mapState.addingConnector
                              ? Colors.green
                              : Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Tooltip(
                          message: 'Add Connector',
                          child: Icon(
                            mapNotifier.selectedConnectorType != null &&
                                    mapState.addingConnector
                                ? ConnectorType.getIcon(
                                    mapNotifier.selectedConnectorType!)
                                : Icons.connecting_airports_rounded,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    GestureDetector(
                      onTap: () => mapNotifier.toggleMeterMode(),
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color:
                              mapState.addingMeter ? Colors.green : Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Tooltip(
                          message: 'Add Smart Meter',
                          child:
                              Icon(Icons.speed_outlined, color: Colors.white),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    GestureDetector(
                      onTap: () => mapNotifier.undoLastPoint(),
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Tooltip(
                          message: 'Undo Last Point',
                          child: Icon(Icons.undo, color: Colors.white),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    GestureDetector(
                      onTap: () {
                        // Only add coordinates when in an active mode
                        if (mapState.isDrawing ||
                            mapState.addingUser ||
                            mapState.addingValve ||
                            mapState.addingConnector ||
                            mapState.addingMeter) {
                          mapNotifier.useCurrentLocation();
                        }
                      },
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: (mapState.isDrawing ||
                                  mapState.addingUser ||
                                  mapState.addingValve ||
                                  mapState.addingConnector ||
                                  mapState.addingMeter)
                              ? Colors.green
                              : Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Tooltip(
                          message: mapState.isDrawing
                              ? 'Add Current Location to Pipeline'
                              : mapState.addingUser
                                  ? 'Add User at Current Location'
                                  : mapState.addingValve
                                      ? 'Add Valve at Current Location'
                                      : mapState.addingConnector
                                          ? 'Add Connector at Current Location'
                                          : mapState.addingMeter
                                              ? 'Add Smart Meter at Current Location'
                                              : 'Current Location',
                          child: Icon(Icons.my_location, color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
