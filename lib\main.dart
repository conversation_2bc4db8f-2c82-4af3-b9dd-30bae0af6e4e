import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'initialization_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  bool firebaseInitialized = false;

  try {
    // First try with default options
    await Firebase.initializeApp();
    firebaseInitialized = true;
  } catch (e) {
    debugPrint('Failed to initialize Firebase with options: $e');

    // Fallback to auto initialization
    try {
      await Firebase.initializeApp();
      firebaseInitialized = true;
      debugPrint('Firebase initialized with auto-detection');
    } catch (e) {
      debugPrint('Failed to initialize Firebase: $e');
    }
  }

  runApp(
    ProviderScope(
      child: MyApp(firebaseInitialized: firebaseInitialized),
    ),
  );
}

class MyApp extends StatelessWidget {
  final bool firebaseInitialized;

  const MyApp({Key? key, this.firebaseInitialized = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'GIS Pipeline Mapping',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: InitializationScreen(firebaseInitialized: firebaseInitialized),
    );
  }
}
