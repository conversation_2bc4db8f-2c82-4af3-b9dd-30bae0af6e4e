import 'package:uuid/uuid.dart';
import 'hive_models/hive_connector_listing_model.dart';

/// Model class for a connector
class ConnectorModel {
  final String id;
  final String title;
  final String connectorType;
  final Map<String, dynamic> specifications;

  ConnectorModel({
    String? id,
    required this.title,
    required this.connectorType,
    required this.specifications,
  }) : id = id ?? const Uuid().v4();

  // Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'connectorType': connectorType,
      'specifications': specifications,
      'createdAt': DateTime.now().millisecondsSinceEpoch,
      'updatedAt': DateTime.now().millisecondsSinceEpoch,
    };
  }

  // Create from Firestore document
  factory ConnectorModel.fromJson(Map<String, dynamic> json) {
    return ConnectorModel(
      id: json['id'] as String,
      title: json['title'] as String,
      connectorType: json['connectorType'] as String,
      specifications: (json['specifications'] as Map<String, dynamic>?) ?? {},
    );
  }

  // Create a copy with updated fields
  ConnectorModel copyWith({
    String? id,
    String? title,
    String? connectorType,
    Map<String, dynamic>? specifications,
  }) {
    return ConnectorModel(
      id: id ?? this.id,
      title: title ?? this.title,
      connectorType: connectorType ?? this.connectorType,
      specifications: specifications ?? this.specifications,
    );
  }

  // Create from HiveConnectorListingModel
  factory ConnectorModel.fromHiveConnectorListing(
      HiveConnectorListingModel hiveModel) {
    return ConnectorModel(
      id: hiveModel.id,
      title: hiveModel.title,
      connectorType: hiveModel.connectorType,
      specifications: hiveModel.specifications,
    );
  }
}
