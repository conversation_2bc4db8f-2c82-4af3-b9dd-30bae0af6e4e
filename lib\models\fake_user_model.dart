import 'package:uuid/uuid.dart';

class FakeUser {
  final String id;
  final String name;
  final String contact;
  final String address;
  final String notes;
  bool isSelected;

  FakeUser({
    String? id,
    required this.name,
    this.contact = '',
    this.address = '',
    this.notes = '',
    this.isSelected = false,
  }) : id = id ?? const Uuid().v4();

  // Convert to a map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'contact': contact,
      'address': address,
      'notes': notes,
    };
  }

  // Create from a map from Firestore
  factory FakeUser.fromMap(Map<String, dynamic> map) {
    return FakeUser(
      id: map['id'] ?? const Uuid().v4(),
      name: map['name'] ?? '',
      contact: map['contact'] ?? '',
      address: map['address'] ?? '',
      notes: map['notes'] ?? '',
    );
  }

  // Create a copy with modified fields
  FakeUser copyWith({
    String? id,
    String? name,
    String? contact,
    String? address,
    String? notes,
    bool? isSelected,
  }) {
    return FakeUser(
      id: id ?? this.id,
      name: name ?? this.name,
      contact: contact ?? this.contact,
      address: address ?? this.address,
      notes: notes ?? this.notes,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
