import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'pipeline.dart';

/// Base class for all map elements
class FirestoreElement {
  final String id;
  final GeoPoint position;
  final String areaId;
  final String title;
  final String type; // "pipeline", "user", "valve", "connector"
  final Map<String, dynamic> metadata;

  FirestoreElement({
    required this.id,
    required this.position,
    required this.areaId,
    required this.title,
    required this.type,
    required this.metadata,
  });

  factory FirestoreElement.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return FirestoreElement(
      id: doc.id,
      position: data['position'] as GeoPoint,
      areaId: data['areaId'] as String,
      title: data['title'] as String,
      type: data['type'] as String,
      metadata: data['metadata'] as Map<String, dynamic>,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'position': position,
      'areaId': areaId,
      'title': title,
      'type': type,
      'metadata': metadata,
    };
  }
}

/// Pipeline element with complete path
class FirestorePipeline extends FirestoreElement {
  final List<Map<String, double>> path; // List of {lat, lng} points
  // Store the full document data for later use in toPipeline
  Map<String, dynamic>? data;

  FirestorePipeline({
    required String id,
    required GeoPoint position,
    required String areaId,
    required String title,
    required Map<String, dynamic> metadata,
    required this.path,
    this.data,
  }) : super(
          id: id,
          position: position,
          areaId: areaId,
          title: title,
          type: 'pipeline',
          metadata: metadata,
        );

  factory FirestorePipeline.fromFirestore(DocumentSnapshot doc) {
    try {
      debugPrint('Creating FirestorePipeline from doc ${doc.id}');

      // Check if document data is null
      if (doc.data() == null) {
        debugPrint('Document data is null for pipeline ${doc.id}');
        // Return a minimal valid pipeline with default values
        return FirestorePipeline(
          id: doc.id,
          position: const GeoPoint(0, 0),
          areaId: 'default',
          title: 'Pipeline ${doc.id}',
          metadata: {},
          path: [
            {'lat': 0.0, 'lng': 0.0},
            {'lat': 0.001, 'lng': 0.001}
          ],
          data: null,
        );
      }

      final data = doc.data() as Map<String, dynamic>;

      // Check for path data - first try 'path', then fallback to 'fullPath' for backward compatibility
      if (data.containsKey('path') || data.containsKey('fullPath')) {
        // Try to parse the path data
        try {
          // Use 'path' field if available, otherwise use 'fullPath'
          final List<dynamic> rawPath = data.containsKey('path')
              ? data['path'] as List<dynamic>
              : data['fullPath'] as List<dynamic>;

          debugPrint('Raw path length: ${rawPath.length}');

          final List<Map<String, double>> parsedPath = rawPath.map((point) {
            final Map<String, dynamic> pointMap = point as Map<String, dynamic>;
            return {
              'lat': (pointMap['lat'] as num).toDouble(),
              'lng': (pointMap['lng'] as num).toDouble(),
            };
          }).toList();

          debugPrint(
              'Successfully parsed path with ${parsedPath.length} points');

          // Create FirestorePipeline with the document data
          // Add null checks for all fields and provide default values
          final pipeline = FirestorePipeline(
            id: doc.id,
            position: data['position'] as GeoPoint? ??
                const GeoPoint(0, 0), // Default to origin if position is null
            areaId: data['areaId'] as String? ??
                'default', // Default to 'default' area if areaId is null
            title: data['title'] as String? ??
                'Pipeline ${doc.id}', // Default title if null
            metadata: data['metadata'] != null
                ? data['metadata'] as Map<String, dynamic>
                : {}, // Empty map if metadata is null
            path: parsedPath,
          );

          // Store the full document data for later use in toPipeline
          pipeline.data = data;

          return pipeline;
        } catch (e) {
          debugPrint('Error parsing path data: $e');
          rethrow;
        }
      } else {
        debugPrint(
            'No path or fullPath data found in pipeline document, creating default path');
        // Instead of throwing an exception, create a default path
        final List<Map<String, double>> defaultPath = [
          {'lat': 0.0, 'lng': 0.0},
          {'lat': 0.001, 'lng': 0.001}
        ];

        // Create FirestorePipeline with default path
        final pipeline = FirestorePipeline(
          id: doc.id,
          position: data['position'] as GeoPoint? ?? const GeoPoint(0, 0),
          areaId: data['areaId'] as String? ?? 'default',
          title: data['title'] as String? ?? 'Pipeline ${doc.id}',
          metadata: data['metadata'] != null
              ? data['metadata'] as Map<String, dynamic>
              : {},
          path: defaultPath,
        );

        // Store the full document data for later use in toPipeline
        pipeline.data = data;

        debugPrint('Created pipeline with default path');
        return pipeline;
      }
    } catch (e) {
      debugPrint('Error in FirestorePipeline.fromFirestore: $e');
      rethrow;
    }
  }

  @override
  Map<String, dynamic> toFirestore() {
    final baseMap = super.toFirestore();
    return {
      ...baseMap,
      'path': path,
    };
  }

  // Convert to app's Pipeline model
  Pipeline toPipeline() {
    try {
      // Get all points from the path
      List<LatLng> points = path.map((point) {
        // Add null safety to lat/lng access
        final lat = point['lat'] ?? 0.0;
        final lng = point['lng'] ?? 0.0;
        return LatLng(lat, lng);
      }).toList();

      debugPrint(
          'Converting pipeline $id with ${points.length} points to Pipeline model');

      // Add null safety to metadata access
      final segmentId =
          metadata.containsKey('segmentId') && metadata['segmentId'] != null
              ? metadata['segmentId'] as String?
              : null;

      // Get the overall pipeline state from the document data
      String pipelineState = 'Active';

      // Create segment map
      Map<String, PipelineSegment> segments = {};

      // Try to get segments data from Firestore document
      try {
        // Get the document data to check for segments
        final data = this.data;
        if (data != null && data.containsKey('segments')) {
          final List<dynamic> segmentsData = data['segments'] as List<dynamic>;

          // Process each segment
          for (final segmentData in segmentsData) {
            final Map<String, dynamic> segmentMap =
                segmentData as Map<String, dynamic>;
            final String segId = segmentMap['id'] as String;
            final Map<String, dynamic> startPoint =
                segmentMap['startPoint'] as Map<String, dynamic>;
            final Map<String, dynamic> endPoint =
                segmentMap['endPoint'] as Map<String, dynamic>;
            final String segmentState =
                segmentMap['state'] as String? ?? pipelineState;

            // Create segment
            segments[segId] = PipelineSegment(
              id: segId,
              start: LatLng(
                (startPoint['lat'] as num).toDouble(),
                (startPoint['lng'] as num).toDouble(),
              ),
              end: LatLng(
                (endPoint['lat'] as num).toDouble(),
                (endPoint['lng'] as num).toDouble(),
              ),
              state: segmentState,
            );
          }

          debugPrint('Loaded ${segments.length} segments from Firestore data');
        }
      } catch (e) {
        debugPrint('Error loading segments from Firestore data: $e');
      }

      // If no segments were loaded from Firestore, create them from points
      if (segments.isEmpty && points.length > 1) {
        debugPrint('Creating segments from points');
        for (int i = 0; i < points.length - 1; i++) {
          final start = points[i];
          final end = points[i + 1];
          final segId = _generateSegmentId(start, end);

          // Default to the pipeline state
          String segmentState = pipelineState;

          // Try to get segment state from path data
          try {
            if (i < path.length - 1) {
              final pointData = path[i + 1] as Map<String, dynamic>;
              if (pointData.containsKey('state')) {
                segmentState = pointData['state'] as String? ?? pipelineState;
              }
            }
          } catch (e) {
            debugPrint('Error getting segment state from path data: $e');
          }

          segments[segId] = PipelineSegment(
            id: segId,
            start: start,
            end: end,
            state: segmentState,
          );
        }
      }

      return Pipeline(
        id: id,
        points: points,
        segmentId: segmentId,
        segments: segments,
        state: pipelineState,
      );
    } catch (e) {
      debugPrint('Error in toPipeline: $e');
      // Return a basic pipeline without segments if there's an error
      // Create at least two points to ensure a valid pipeline
      if (path.isEmpty) {
        debugPrint(
            'Path is empty, creating a default pipeline with two points');
        return Pipeline(
          id: id,
          points: [const LatLng(0, 0), const LatLng(0.001, 0.001)],
          segmentId: null,
        );
      }

      return Pipeline(
        id: id,
        points: path.map((point) {
          // Add null safety to lat/lng access
          final lat = point['lat'] ?? 0.0;
          final lng = point['lng'] ?? 0.0;
          return LatLng(lat, lng);
        }).toList(),
        segmentId:
            metadata.containsKey('segmentId') && metadata['segmentId'] != null
                ? metadata['segmentId'] as String?
                : null,
      );
    }
  }

  // Helper function to generate consistent segment IDs
  static String _generateSegmentId(LatLng start, LatLng end) {
    // Ensure consistent segment ID regardless of point order
    final startStr = '${start.latitude},${start.longitude}';
    final endStr = '${end.latitude},${end.longitude}';

    if (startStr.compareTo(endStr) > 0) {
      return 'segment_${end.latitude}_${end.longitude}_${start.latitude}_${start.longitude}';
    }
    return 'segment_${start.latitude}_${start.longitude}_${end.latitude}_${end.longitude}';
  }

  // Create from app's Pipeline model
  factory FirestorePipeline.fromPipeline(Pipeline pipeline, String areaId) {
    // Calculate center point
    final centerLat =
        pipeline.points.map((p) => p.latitude).reduce((a, b) => a + b) /
            pipeline.points.length;
    final centerLng =
        pipeline.points.map((p) => p.longitude).reduce((a, b) => a + b) /
            pipeline.points.length;

    return FirestorePipeline(
      id: pipeline.id,
      position: GeoPoint(centerLat, centerLng),
      areaId: areaId,
      title: 'Pipeline ${pipeline.id}',
      metadata: {
        'segmentId': pipeline.segmentId,
        'length': _calculatePathLength(pipeline.points),
        'pointCount': pipeline.points.length,
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      },
      path: pipeline.points
          .map((point) => {
                'lat': point.latitude,
                'lng': point.longitude,
              })
          .toList(),
      data: null, // No need to store data when creating from Pipeline
    );
  }

  static double _calculatePathLength(List<LatLng> path) {
    double totalDistance = 0;
    for (int i = 0; i < path.length - 1; i++) {
      totalDistance += _calculateDistance(path[i], path[i + 1]);
    }
    return totalDistance;
  }

  static double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // in meters
    final lat1 = point1.latitude * 3.14159 / 180;
    final lat2 = point2.latitude * 3.14159 / 180;
    final dLat = (point2.latitude - point1.latitude) * 3.14159 / 180;
    final dLon = (point2.longitude - point1.longitude) * 3.14159 / 180;

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }
}

/// User element
class FirestoreUser extends FirestoreElement {
  FirestoreUser({
    required String id,
    required GeoPoint position,
    required String areaId,
    required String title,
    required Map<String, dynamic> metadata,
  }) : super(
          id: id,
          position: position,
          areaId: areaId,
          title: title,
          type: 'user',
          metadata: metadata,
        );

  factory FirestoreUser.fromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>;
      return FirestoreUser(
        id: doc.id,
        position: data['position'] as GeoPoint? ??
            const GeoPoint(0, 0), // Default to origin if position is null
        areaId: data['areaId'] as String? ??
            'default', // Default to 'default' area if areaId is null
        title: data['title'] as String? ??
            'User ${doc.id}', // Default title if null
        metadata: data['metadata'] != null
            ? data['metadata'] as Map<String, dynamic>
            : {}, // Empty map if metadata is null
      );
    } catch (e) {
      debugPrint('Error in FirestoreUser.fromFirestore: $e');
      // Return a basic user with default values
      return FirestoreUser(
        id: doc.id,
        position: const GeoPoint(0, 0),
        areaId: 'default',
        title: 'User ${doc.id}',
        metadata: {},
      );
    }
  }

  // Convert to app's UserValvePosition model
  UserValvePosition toUserValvePosition() {
    return UserValvePosition(
      id: id,
      position: LatLng(position.latitude, position.longitude),
      type: PositionType.user,
      title: title,
    );
  }

  // Create from app's UserValvePosition model
  factory FirestoreUser.fromUserValvePosition(
      UserValvePosition position, String areaId) {
    return FirestoreUser(
      id: position.id,
      position:
          GeoPoint(position.position.latitude, position.position.longitude),
      areaId: areaId,
      title: position.title,
      metadata: {
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      },
    );
  }
}

/// Valve element
class FirestoreValve extends FirestoreElement {
  FirestoreValve({
    required String id,
    required GeoPoint position,
    required String areaId,
    required String title,
    required Map<String, dynamic> metadata,
  }) : super(
          id: id,
          position: position,
          areaId: areaId,
          title: title,
          type: 'valve',
          metadata: metadata,
        );

  factory FirestoreValve.fromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>;
      return FirestoreValve(
        id: doc.id,
        position: data['position'] as GeoPoint? ??
            const GeoPoint(0, 0), // Default to origin if position is null
        areaId: data['areaId'] as String? ??
            'default', // Default to 'default' area if areaId is null
        title: data['title'] as String? ??
            'Valve ${doc.id}', // Default title if null
        metadata: data['metadata'] != null
            ? data['metadata'] as Map<String, dynamic>
            : {}, // Empty map if metadata is null
      );
    } catch (e) {
      debugPrint('Error in FirestoreValve.fromFirestore: $e');
      // Return a basic valve with default values
      return FirestoreValve(
        id: doc.id,
        position: const GeoPoint(0, 0),
        areaId: 'default',
        title: 'Valve ${doc.id}',
        metadata: {},
      );
    }
  }

  // Convert to app's UserValvePosition model
  UserValvePosition toUserValvePosition() {
    return UserValvePosition(
      id: id,
      position: LatLng(position.latitude, position.longitude),
      type: PositionType.valve,
      title: title,
    );
  }

  // Create from app's UserValvePosition model
  factory FirestoreValve.fromUserValvePosition(
      UserValvePosition position, String areaId) {
    return FirestoreValve(
      id: position.id,
      position:
          GeoPoint(position.position.latitude, position.position.longitude),
      areaId: areaId,
      title: position.title,
      metadata: {
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      },
    );
  }
}

/// Connector element
class FirestoreConnector extends FirestoreElement {
  final int connectorType;

  FirestoreConnector({
    required String id,
    required GeoPoint position,
    required String areaId,
    required String title,
    required Map<String, dynamic> metadata,
    required this.connectorType,
  }) : super(
          id: id,
          position: position,
          areaId: areaId,
          title: title,
          type: 'connector',
          metadata: metadata,
        );

  factory FirestoreConnector.fromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>;
      return FirestoreConnector(
        id: doc.id,
        position: data['position'] as GeoPoint? ??
            const GeoPoint(0, 0), // Default to origin if position is null
        areaId: data['areaId'] as String? ??
            'default', // Default to 'default' area if areaId is null
        title: data['title'] as String? ??
            'Connector ${doc.id}', // Default title if null
        metadata: data['metadata'] != null
            ? data['metadata'] as Map<String, dynamic>
            : {}, // Empty map if metadata is null
        connectorType: data['connectorType'] as int? ??
            ConnectorType.tee, // Default to tee if connectorType is null
      );
    } catch (e) {
      debugPrint('Error in FirestoreConnector.fromFirestore: $e');
      // Return a basic connector with default values
      return FirestoreConnector(
        id: doc.id,
        position: const GeoPoint(0, 0),
        areaId: 'default',
        title: 'Connector ${doc.id}',
        metadata: {},
        connectorType: ConnectorType.tee,
      );
    }
  }

  @override
  Map<String, dynamic> toFirestore() {
    final baseMap = super.toFirestore();
    return {
      ...baseMap,
      'connectorType': connectorType,
    };
  }

  // Convert to app's UserValvePosition model
  UserValvePosition toUserValvePosition() {
    return UserValvePosition(
      id: id,
      position: LatLng(position.latitude, position.longitude),
      type: PositionType.connector,
      title: title,
      connectorType: connectorType,
    );
  }

  // Create from app's UserValvePosition model
  factory FirestoreConnector.fromUserValvePosition(
      UserValvePosition position, String areaId) {
    return FirestoreConnector(
      id: position.id,
      position:
          GeoPoint(position.position.latitude, position.position.longitude),
      areaId: areaId,
      title: position.title,
      metadata: {
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      },
      connectorType: position.connectorType ?? ConnectorType.tee,
    );
  }
}

/// Geographic area for partitioning data
class FirestoreArea {
  final String id;
  final String name;
  final GeoPoint northeast;
  final GeoPoint southwest;
  final Map<String, int> summary;

  FirestoreArea({
    required this.id,
    required this.name,
    required this.northeast,
    required this.southwest,
    required this.summary,
  });

  factory FirestoreArea.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final bounds = data['bounds'] as Map<String, dynamic>;

    return FirestoreArea(
      id: doc.id,
      name: data['name'] as String,
      northeast: bounds['northeast'] as GeoPoint,
      southwest: bounds['southwest'] as GeoPoint,
      summary: Map<String, int>.from(data['summary'] as Map),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'bounds': {
        'northeast': northeast,
        'southwest': southwest,
      },
      'summary': summary,
    };
  }

  // Check if a point is within this area
  bool containsPoint(LatLng point) {
    return point.latitude >= southwest.latitude &&
        point.latitude <= northeast.latitude &&
        point.longitude >= southwest.longitude &&
        point.longitude <= northeast.longitude;
  }

  // Check if this area intersects with a bounds
  bool intersectsBounds(LatLngBounds bounds) {
    // Check if one rectangle is to the left of the other
    if (northeast.longitude < bounds.southwest.longitude ||
        southwest.longitude > bounds.northeast.longitude) {
      return false;
    }

    // Check if one rectangle is above the other
    if (northeast.latitude < bounds.southwest.latitude ||
        southwest.latitude > bounds.northeast.latitude) {
      return false;
    }

    return true;
  }
}

/// Detailed pipeline information
class FirestorePipelineDetails {
  final String id;
  final GeoPoint startPoint;
  final GeoPoint endPoint;
  final Map<String, dynamic> properties;
  final List<String> connections;

  FirestorePipelineDetails({
    required this.id,
    required this.startPoint,
    required this.endPoint,
    required this.properties,
    required this.connections,
  });

  factory FirestorePipelineDetails.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return FirestorePipelineDetails(
      id: doc.id,
      startPoint: data['startPoint'] as GeoPoint,
      endPoint: data['endPoint'] as GeoPoint,
      properties: data['properties'] as Map<String, dynamic>,
      connections: List<String>.from(data['connections'] ?? []),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'startPoint': startPoint,
      'endPoint': endPoint,
      'properties': properties,
      'connections': connections,
    };
  }
}

/// Detailed user information
class FirestoreUserDetails {
  final String id;
  final Map<String, dynamic> details;
  final List<String> connections;

  FirestoreUserDetails({
    required this.id,
    required this.details,
    required this.connections,
  });

  factory FirestoreUserDetails.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return FirestoreUserDetails(
      id: doc.id,
      details: data['details'] as Map<String, dynamic>,
      connections: List<String>.from(data['connections'] ?? []),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'details': details,
      'connections': connections,
    };
  }
}

/// Detailed valve information
class FirestoreValveDetails {
  final String id;
  final Map<String, dynamic> specifications;
  final String status;
  final DateTime? lastMaintenance;
  final bool isDeleted;
  final bool isSync;
  final double? pricePerUnit;

  FirestoreValveDetails({
    required this.id,
    required this.specifications,
    required this.status,
    this.lastMaintenance,
    this.isDeleted = false,
    this.isSync = true,
    this.pricePerUnit,
  });

  factory FirestoreValveDetails.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Handle lastMaintenance as milliseconds or Timestamp
    DateTime? lastMaintenance;
    if (data['lastMaintenance'] != null) {
      if (data['lastMaintenance'] is int) {
        lastMaintenance =
            DateTime.fromMillisecondsSinceEpoch(data['lastMaintenance'] as int);
      } else if (data['lastMaintenance'] is Timestamp) {
        lastMaintenance = (data['lastMaintenance'] as Timestamp).toDate();
      }
    }

    return FirestoreValveDetails(
      id: doc.id,
      specifications: data['specifications'] as Map<String, dynamic>? ?? {},
      status: data['status'] as String? ?? 'Operational',
      lastMaintenance: lastMaintenance,
      isDeleted: data['isDeleted'] as bool? ?? false,
      isSync: data['isSync'] as bool? ?? true,
      pricePerUnit: data['pricePerUnit'] as double?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'specifications': specifications,
      'status': status,
      'lastMaintenance': lastMaintenance?.millisecondsSinceEpoch,
      'isDeleted': isDeleted,
      'isSync': isSync,
      'pricePerUnit': pricePerUnit,
    };
  }
}

/// Detailed connector information
class FirestoreConnectorDetails {
  final String id;
  final int type;
  final Map<String, dynamic> specifications;
  final List<String> connections;

  FirestoreConnectorDetails({
    required this.id,
    required this.type,
    required this.specifications,
    required this.connections,
  });

  factory FirestoreConnectorDetails.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return FirestoreConnectorDetails(
      id: doc.id,
      type: data['type'] as int,
      specifications: data['specifications'] as Map<String, dynamic>,
      connections: List<String>.from(data['connections'] ?? []),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'type': type,
      'specifications': specifications,
      'connections': connections,
    };
  }
}

// Helper functions are already available from dart:math import
