import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'hive_category_model.g.dart';

@HiveType(typeId: 8)
class HiveCategoryModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String description;

  @HiveField(3)
  String assetType; // 'valve', 'connector', 'pipeline', 'smartMeter', 'all'

  @HiveField(4)
  Map<String, dynamic> properties;

  @HiveField(5)
  int updatedAt;

  @HiveField(6)
  bool isSync;

  @HiveField(7)
  bool isDeleted;

  HiveCategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.assetType,
    required this.properties,
    required this.updatedAt,
    this.isSync = false,
    this.isDeleted = false,
  });

  // Create a new category with a generated ID
  factory HiveCategoryModel.create({
    required String name,
    required String description,
    required String assetType,
    required Map<String, dynamic> properties,
  }) {
    return HiveCategoryModel(
      id: const Uuid().v4(),
      name: name,
      description: description,
      assetType: assetType,
      properties: properties,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
      isSync: false,
    );
  }

  // Create from JSON (for Firestore)
  factory HiveCategoryModel.fromJson(Map<String, dynamic> json) {
    return HiveCategoryModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      assetType: json['assetType'] as String? ?? 'all',
      properties: (json['properties'] as Map<String, dynamic>?) ?? {},
      updatedAt:
          json['updatedAt'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      isSync: json['isSync'] as bool? ?? true,
      isDeleted: json['isDeleted'] as bool? ?? false,
    );
  }

  // Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'assetType': assetType,
      'properties': properties,
      'updatedAt': updatedAt,
      'isSync': isSync,
      'isDeleted': isDeleted,
    };
  }

  // Mark as updated (not synced)
  void markAsUpdated() {
    isSync = false;
    updatedAt = DateTime.now().millisecondsSinceEpoch;
  }

  // Mark as deleted
  void markAsDeleted() {
    isDeleted = true;
    isSync = false;
    updatedAt = DateTime.now().millisecondsSinceEpoch;
  }
}
