// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_category_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveCategoryModelAdapter extends TypeAdapter<HiveCategoryModel> {
  @override
  final int typeId = 8;

  @override
  HiveCategoryModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveCategoryModel(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      assetType: fields[3] as String,
      properties: (fields[4] as Map).cast<String, dynamic>(),
      updatedAt: fields[5] as int,
      isSync: fields[6] as bool,
      isDeleted: fields[7] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, HiveCategoryModel obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.assetType)
      ..writeByte(4)
      ..write(obj.properties)
      ..writeByte(5)
      ..write(obj.updatedAt)
      ..writeByte(6)
      ..write(obj.isSync)
      ..writeByte(7)
      ..write(obj.isDeleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveCategoryModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
