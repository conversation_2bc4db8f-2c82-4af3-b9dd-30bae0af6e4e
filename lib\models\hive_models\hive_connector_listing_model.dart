import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'hive_connector_listing_model.g.dart';

@HiveType(typeId: 6)
class HiveConnectorListingModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  String connectorType;

  @HiveField(3)
  Map<String, dynamic> specifications;

  @HiveField(4)
  int updatedAt;

  @HiveField(5)
  bool isSync;

  @HiveField(6)
  bool isDeleted;

  @HiveField(7)
  double? pricePerUnit;

  HiveConnectorListingModel({
    required this.id,
    required this.title,
    required this.connectorType,
    required this.specifications,
    required this.updatedAt,
    this.isSync = false,
    this.isDeleted = false,
    this.pricePerUnit,
  });

  // Create a new connector listing with a generated ID
  factory HiveConnectorListingModel.create({
    required String title,
    required String connectorType,
    required Map<String, dynamic> specifications,
    double? pricePerUnit,
  }) {
    return HiveConnectorListingModel(
      id: const Uuid().v4(),
      title: title,
      connectorType: connectorType,
      specifications: specifications,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
      isSync: false,
      pricePerUnit: pricePerUnit,
    );
  }

  // Create from JSON (for Firestore)
  factory HiveConnectorListingModel.fromJson(Map<String, dynamic> json) {
    return HiveConnectorListingModel(
      id: json['id'] as String,
      title: json['title'] as String,
      connectorType: json['connectorType'] as String,
      specifications: (json['specifications'] as Map<String, dynamic>?) ?? {},
      updatedAt:
          json['updatedAt'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      isSync: json['isSync'] as bool? ?? true,
      isDeleted: json['isDeleted'] as bool? ?? false,
      pricePerUnit: json['pricePerUnit'] != null
          ? (json['pricePerUnit'] as num).toDouble()
          : null,
    );
  }

  // Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'connectorType': connectorType,
      'specifications': specifications,
      'updatedAt': updatedAt,
      'isSync': isSync,
      'isDeleted': isDeleted,
      'pricePerUnit': pricePerUnit,
    };
  }

  // Mark as updated (not synced)
  void markAsUpdated() {
    isSync = false;
    updatedAt = DateTime.now().millisecondsSinceEpoch;
  }

  // Mark as deleted
  void markAsDeleted() {
    isDeleted = true;
    isSync = false;
    updatedAt = DateTime.now().millisecondsSinceEpoch;
  }
}
