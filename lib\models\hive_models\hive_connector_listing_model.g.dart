// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_connector_listing_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveConnectorListingModelAdapter
    extends TypeAdapter<HiveConnectorListingModel> {
  @override
  final int typeId = 11;

  @override
  HiveConnectorListingModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveConnectorListingModel(
      id: fields[0] as String,
      title: fields[1] as String,
      connectorType: fields[2] as String,
      specifications: (fields[3] as Map).cast<String, dynamic>(),
      updatedAt: fields[4] as int,
      isSync: fields[5] as bool,
      isDeleted: fields[6] as bool,
      pricePerUnit: fields[7] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, HiveConnectorListingModel obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.connectorType)
      ..writeByte(3)
      ..write(obj.specifications)
      ..writeByte(4)
      ..write(obj.updatedAt)
      ..writeByte(5)
      ..write(obj.isSync)
      ..writeByte(6)
      ..write(obj.isDeleted)
      ..writeByte(7)
      ..write(obj.pricePerUnit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveConnectorListingModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
