import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import '../pipeline.dart';
import 'package:uuid/uuid.dart';

part 'hive_connector_model.g.dart';

@HiveType(typeId: 3)
class HiveConnectorModel extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime createdAt;

  @HiveField(2)
  late DateTime updatedAt;

  @HiveField(3)
  late bool isSync;

  @HiveField(4)
  late bool isDeleted;
  @HiveField(5)
  late double latitude;

  @HiveField(6)
  late double longitude;

  @HiveField(7)
  late String title;

  @HiveField(8)
  late int connectorType;

  @HiveField(9)
  late Map<String, dynamic> specifications;

  @HiveField(10)
  late List<String> connections;

  @HiveField(11)
  late String areaId;

  HiveConnectorModel({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isSync = false,
    bool isDeleted = false,
    required this.latitude,
    required this.longitude,
    required this.title,
    required this.connectorType,
    Map<String, dynamic>? specifications,
    List<String>? connections,
    required this.areaId,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    this.isSync = isSync;
    this.isDeleted = isDeleted;
    this.specifications = specifications ?? {'material': 'Standard'};
    this.connections = connections ?? [];
  }

  factory HiveConnectorModel.fromPosition({
    required LatLng position,
    required String title,
    required int connectorType,
    required String areaId,
    Map<String, dynamic>? specifications,
    List<String>? connections,
  }) {
    return HiveConnectorModel(
      latitude: position.latitude,
      longitude: position.longitude,
      title: title,
      connectorType: connectorType,
      specifications: specifications ?? {'material': 'Standard'},
      connections: connections ?? [],
      areaId: areaId,
    );
  }

  LatLng get position => LatLng(latitude, longitude);

  /// Mark the model as updated
  void markAsUpdated() {
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as synced with Firestore
  void markAsSynced() {
    isSync = true;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as deleted
  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Check if the model needs to be synced
  bool get needsSync => !isSync;

  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'type': 'connector',
      'position': {'latitude': latitude, 'longitude': longitude},
      'title': title,
      'areaId': areaId,
      'metadata': {
        'createdAt': createdAt.millisecondsSinceEpoch,
        'lastUpdated': updatedAt.millisecondsSinceEpoch,
        'connectorType': connectorType,
      },
    };
  }

  Map<String, dynamic> toFirestoreDetails() {
    return {
      'type': connectorType,
      'specifications': specifications,
      'connections': connections,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': updatedAt.millisecondsSinceEpoch,
    };
  }
}
