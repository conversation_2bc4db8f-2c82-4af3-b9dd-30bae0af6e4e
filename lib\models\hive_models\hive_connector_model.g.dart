// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_connector_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveConnectorModelAdapter extends TypeAdapter<HiveConnectorModel> {
  @override
  final int typeId = 3;

  @override
  HiveConnectorModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveConnectorModel(
      id: fields[0] as String?,
      createdAt: fields[1] as DateTime?,
      updatedAt: fields[2] as DateTime?,
      isSync: fields[3] as bool,
      isDeleted: fields[4] as bool,
      latitude: fields[5] as double,
      longitude: fields[6] as double,
      title: fields[7] as String,
      connectorType: fields[8] as int,
      specifications: (fields[9] as Map?)?.cast<String, dynamic>(),
      connections: (fields[10] as List?)?.cast<String>(),
      areaId: fields[11] as String,
    );
  }

  @override
  void write(BinaryWriter writer, HiveConnectorModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.updatedAt)
      ..writeByte(3)
      ..write(obj.isSync)
      ..writeByte(4)
      ..write(obj.isDeleted)
      ..writeByte(5)
      ..write(obj.latitude)
      ..writeByte(6)
      ..write(obj.longitude)
      ..writeByte(7)
      ..write(obj.title)
      ..writeByte(8)
      ..write(obj.connectorType)
      ..writeByte(9)
      ..write(obj.specifications)
      ..writeByte(10)
      ..write(obj.connections)
      ..writeByte(11)
      ..write(obj.areaId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveConnectorModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
