import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';

part 'hive_latlng.g.dart';

@HiveType(typeId: 5)
class HiveLatLng {
  @HiveField(0)
  final double latitude;

  @HiveField(1)
  final double longitude;

  HiveLatLng(this.latitude, this.longitude);

  factory HiveLatLng.fromLatLng(LatLng latLng) {
    return HiveLatLng(latLng.latitude, latLng.longitude);
  }

  LatLng toLatLng() {
    return LatLng(latitude, longitude);
  }
}

