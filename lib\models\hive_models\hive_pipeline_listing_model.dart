import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'hive_pipeline_listing_model.g.dart';

@HiveType(typeId: 7)
class HivePipelineListingModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  String pipeType;

  @HiveField(3)
  String pipeDiameter;

  @HiveField(4)
  String state;

  @HiveField(5)
  Map<String, dynamic> properties;

  @HiveField(6)
  int updatedAt;

  @HiveField(7)
  bool isSync;

  @HiveField(8)
  bool isDeleted;

  @HiveField(9)
  double? pricePerUnit;

  HivePipelineListingModel({
    required this.id,
    required this.title,
    required this.pipeType,
    required this.pipeDiameter,
    required this.state,
    required this.properties,
    required this.updatedAt,
    this.isSync = false,
    this.isDeleted = false,
    this.pricePerUnit,
  });

  // Create a new pipeline listing with a generated ID
  factory HivePipelineListingModel.create({
    required String title,
    required String pipeType,
    required String pipeDiameter,
    required String state,
    required Map<String, dynamic> properties,
    double? pricePerUnit,
  }) {
    return HivePipelineListingModel(
      id: const Uuid().v4(),
      title: title,
      pipeType: pipeType,
      pipeDiameter: pipeDiameter,
      state: state,
      properties: properties,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
      isSync: false,
      pricePerUnit: pricePerUnit,
    );
  }

  // Create from JSON (for Firestore)
  factory HivePipelineListingModel.fromJson(Map<String, dynamic> json) {
    return HivePipelineListingModel(
      id: json['id'] as String,
      title: json['title'] as String,
      pipeType: json['pipeType'] as String? ?? 'Standard',
      pipeDiameter: json['pipeDiameter'] as String? ?? '100mm',
      state: json['state'] as String? ?? 'normal',
      properties: (json['properties'] as Map<String, dynamic>?) ?? {},
      updatedAt:
          json['updatedAt'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      isSync: json['isSync'] as bool? ?? true,
      isDeleted: json['isDeleted'] as bool? ?? false,
      pricePerUnit: json['pricePerUnit'] != null
          ? (json['pricePerUnit'] as num).toDouble()
          : null,
    );
  }

  // Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'pipeType': pipeType,
      'pipeDiameter': pipeDiameter,
      'state': state,
      'properties': properties,
      'updatedAt': updatedAt,
      'isSync': isSync,
      'isDeleted': isDeleted,
      'pricePerUnit': pricePerUnit,
    };
  }

  // Mark as updated (not synced)
  void markAsUpdated() {
    isSync = false;
    updatedAt = DateTime.now().millisecondsSinceEpoch;
  }

  // Mark as deleted
  void markAsDeleted() {
    isDeleted = true;
    isSync = false;
    updatedAt = DateTime.now().millisecondsSinceEpoch;
  }
}
