// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_pipeline_listing_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HivePipelineListingModelAdapter
    extends TypeAdapter<HivePipelineListingModel> {
  @override
  final int typeId = 7;

  @override
  HivePipelineListingModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HivePipelineListingModel(
      id: fields[0] as String,
      title: fields[1] as String,
      pipeType: fields[2] as String,
      pipeDiameter: fields[3] as String,
      state: fields[4] as String,
      properties: (fields[5] as Map).cast<String, dynamic>(),
      updatedAt: fields[6] as int,
      isSync: fields[7] as bool,
      isDeleted: fields[8] as bool,
      pricePerUnit: fields[9] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, HivePipelineListingModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.pipeType)
      ..writeByte(3)
      ..write(obj.pipeDiameter)
      ..writeByte(4)
      ..write(obj.state)
      ..writeByte(5)
      ..write(obj.properties)
      ..writeByte(6)
      ..write(obj.updatedAt)
      ..writeByte(7)
      ..write(obj.isSync)
      ..writeByte(8)
      ..write(obj.isDeleted)
      ..writeByte(9)
      ..write(obj.pricePerUnit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HivePipelineListingModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
