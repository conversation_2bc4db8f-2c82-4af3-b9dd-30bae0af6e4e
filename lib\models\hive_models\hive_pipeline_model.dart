import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'manual_adapters.dart' as adapters;
import '../../models/pipeline.dart';

part 'hive_pipeline_model.g.dart';

@HiveType(typeId: 9)
class HivePipelineModel extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime createdAt;

  @HiveField(2)
  late DateTime updatedAt;

  @HiveField(3)
  late bool isSync;

  @HiveField(4)
  late bool isDeleted;
  @HiveField(5)
  late List<adapters.HiveLatLng> points;

  @HiveField(6)
  late String title;

  @HiveField(7)
  late Map<String, dynamic> properties;

  @HiveField(8)
  late String areaId;

  @HiveField(9)
  late String segmentId;

  // Factory method to create a HivePipelineModel from a Pipeline
  factory HivePipelineModel.fromPipeline({
    required Pipeline pipeline,
    required String areaId,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isSync = false,
    bool isDeleted = false,
    Map<String, dynamic>? properties,
  }) {
    return HivePipelineModel(
      id: pipeline.id,
      points: pipeline.points,
      title: 'Pipeline ${pipeline.id.split('_').last}',
      areaId: areaId,
      segmentId: pipeline.segmentId ?? '',
      createdAt: createdAt,
      updatedAt: updatedAt,
      isSync: isSync,
      isDeleted: isDeleted,
      properties: properties,
    );
  }

  HivePipelineModel({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isSync = false,
    bool isDeleted = false,
    required List<LatLng> points,
    required this.title,
    Map<String, dynamic>? properties,
    required this.areaId,
    required this.segmentId,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    this.isSync = isSync;
    this.isDeleted = isDeleted;
    this.points = points
        .map((p) => adapters.HiveLatLng(p.latitude, p.longitude))
        .toList();
    this.properties = properties ?? {'material': 'Standard', 'diameter': 100};
  }

  List<LatLng> get latLngPoints =>
      points.map((p) => LatLng(p.latitude, p.longitude)).toList();

  /// Mark the model as updated
  void markAsUpdated() {
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
    debugPrint(
        'HivePipelineModel: markAsUpdated called for pipeline $id - isSync=$isSync');
  }

  /// Mark the model as synced with Firestore
  void markAsSynced() {
    isSync = true;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as deleted
  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Check if the model needs to be synced
  bool get needsSync => !isSync;

  Map<String, dynamic> toFirestore() {
    // Calculate center point for the pipeline
    final centerLat = points.isNotEmpty
        ? points.map((p) => p.latitude).reduce((a, b) => a + b) / points.length
        : 0.0;
    final centerLng = points.isNotEmpty
        ? points.map((p) => p.longitude).reduce((a, b) => a + b) / points.length
        : 0.0;

    // Get state from properties or default to 'Active'
    final state = properties['state'] ?? 'Active';

    // Convert all points to the format needed for Firestore
    final List<Map<String, double>> fullPath = points
        .map((point) => {'lat': point.latitude, 'lng': point.longitude})
        .toList();

    return {
      'id': id,
      'type': 'pipeline',
      'position': {'latitude': centerLat, 'longitude': centerLng},
      'title': title,
      'areaId': areaId,
      'state': state, // Add state to elements collection
      'path': fullPath, // Include all points in the path
      // No metadata as per requirement
    };
  }

  // Get segment states from properties
  Map<String, String> getSegmentStates() {
    if (properties.containsKey('segmentStates') &&
        properties['segmentStates'] is Map) {
      return Map<String, String>.from(properties['segmentStates']);
    }
    return {};
  }

  // Set segment state for a specific segment
  void setSegmentState(String segmentId, String state) {
    final segmentStates = getSegmentStates();
    segmentStates[segmentId] = state;
    properties['segmentStates'] = segmentStates;
    markAsUpdated();
  }

  // Generate segment ID for two points
  String generateSegmentId(LatLng start, LatLng end) {
    // Ensure consistent segment ID regardless of point order
    final startStr = '${start.latitude},${start.longitude}';
    final endStr = '${end.latitude},${end.longitude}';

    if (startStr.compareTo(endStr) > 0) {
      return 'segment_${end.latitude}_${end.longitude}_${start.latitude}_${start.longitude}';
    }
    return 'segment_${start.latitude}_${start.longitude}_${end.latitude}_${end.longitude}';
  }

  // Get all segment IDs for this pipeline
  List<String> getAllSegmentIds() {
    final List<String> segmentIds = [];
    for (int i = 0; i < points.length - 1; i++) {
      final start = points[i];
      final end = points[i + 1];
      segmentIds.add(generateSegmentId(LatLng(start.latitude, start.longitude),
          LatLng(end.latitude, end.longitude)));
    }
    return segmentIds;
  }

  Map<String, dynamic> toFirestoreDetails() {
    // Calculate path length
    double totalDistance = 0;
    for (int i = 0; i < points.length - 1; i++) {
      totalDistance += _calculateDistance(
        LatLng(points[i].latitude, points[i].longitude),
        LatLng(points[i + 1].latitude, points[i + 1].longitude),
      );
    }

    // Get state from properties or default to 'Active'
    final state = properties['state'] ?? 'Active';

    // Debug log to verify the state is being included
    debugPrint('🔄 Pipeline $id state in toFirestoreDetails: $state');
    debugPrint('🔄 Pipeline $id properties: $properties');
    debugPrint('🔄 Pipeline $id isSync: $isSync, updatedAt: $updatedAt');

    // Get segment states from properties
    final segmentStates = getSegmentStates();

    // Ensure properties has all required fields
    final enhancedProperties = {
      ...properties,
      'pipeType': properties['pipeType'] ?? 'Standard',
      'diameter': properties['diameter'] ?? 100,
      'material': properties['material'] ?? 'PVC',
      'pressure': properties['pressure'] ?? 'Medium',
      'state': state,
    };

    // Create segments array with IDs and states
    final List<Map<String, dynamic>> segments = [];
    final List<Map<String, dynamic>> path = [];

    // Process each segment
    for (int i = 0; i < points.length - 1; i++) {
      final start = points[i];
      final end = points[i + 1];

      // Generate a unique segment ID
      final currentSegmentId = generateSegmentId(
        LatLng(start.latitude, start.longitude),
        LatLng(end.latitude, end.longitude),
      );

      // Get state for this segment (default to pipeline state)
      final segmentState = segmentStates[currentSegmentId] ?? state;

      // Calculate segment length
      final segmentLength = _calculateDistance(
        LatLng(start.latitude, start.longitude),
        LatLng(end.latitude, end.longitude),
      );

      // Add segment info with start and end points
      segments.add({
        'id': currentSegmentId,
        'startPoint': {'lat': start.latitude, 'lng': start.longitude},
        'endPoint': {'lat': end.latitude, 'lng': end.longitude},
        'state': segmentState,
        'length': segmentLength,
      });

      // Add points to the path array with segment information
      // For the first segment, add the start point
      if (i == 0) {
        path.add({
          'lat': start.latitude,
          'lng': start.longitude,
          'segmentId': currentSegmentId,
          'state': segmentState,
          'isJunction': false,
          'isStart': true,
          'isEnd': false,
        });
      }

      // Add the end point with segment info
      path.add({
        'lat': end.latitude,
        'lng': end.longitude,
        'segmentId': currentSegmentId,
        'state': segmentState,
        'isJunction':
            i < points.length - 2, // It's a junction if it's not the last point
        'isStart': false,
        'isEnd':
            i == points.length - 2, // It's the end if it's the last segment
      });
    }

    return {
      'startPoint': {
        'latitude': points.first.latitude,
        'longitude': points.first.longitude
      },
      'endPoint': {
        'latitude': points.last.latitude,
        'longitude': points.last.longitude
      },
      'path': path, // Renamed from enhancedFullPath to path for clarity
      'segments': segments,
      'properties': enhancedProperties,
      'connections': [],
      'state': state, // Add state at the top level too
      'metadata': {
        'length': totalDistance,
        'pointCount': points.length,
        'segmentCount': segments.length,
        'segmentId': segmentId, // Keep for backward compatibility
        'createdAt': createdAt.millisecondsSinceEpoch,
        'lastUpdated': updatedAt.millisecondsSinceEpoch,
      },
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': updatedAt.millisecondsSinceEpoch,
    };
  }

  // This method was removed as it's no longer used

  // Calculate distance between two points in meters
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // in meters
    final lat1 = point1.latitude * (3.141592653589793 / 180);
    final lat2 = point2.latitude * (3.141592653589793 / 180);
    final dLat =
        (point2.latitude - point1.latitude) * (3.141592653589793 / 180);
    final dLon =
        (point2.longitude - point1.longitude) * (3.141592653589793 / 180);

    final a = _sin(dLat / 2) * _sin(dLat / 2) +
        _cos(lat1) * _cos(lat2) * _sin(dLon / 2) * _sin(dLon / 2);
    final c = 2 * _atan2(_sqrt(a), _sqrt(1 - a));

    return earthRadius * c;
  }

  // Simple math functions to avoid importing dart:math
  double _sin(double x) => _taylor_series_sin(x);
  double _cos(double x) => _taylor_series_cos(x);
  double _sqrt(double x) => x <= 0 ? 0 : _newton_raphson_sqrt(x);
  double _atan2(double y, double x) {
    if (x > 0) return _atan(y / x);
    if (x < 0 && y >= 0) return _atan(y / x) + 3.141592653589793;
    if (x < 0 && y < 0) return _atan(y / x) - 3.141592653589793;
    if (x == 0 && y > 0) return 1.5707963267948966;
    if (x == 0 && y < 0) return -1.5707963267948966;
    return 0; // x == 0 && y == 0
  }

  double _atan(double x) {
    if (x.abs() > 1)
      return x > 0
          ? 1.5707963267948966 - _atan(1 / x)
          : -1.5707963267948966 - _atan(1 / x);
    double result = 0;
    double term = x;
    double x_squared = x * x;
    double denominator = 1;
    for (int i = 1; i <= 10; i++) {
      result += term / denominator;
      term = -term * x_squared;
      denominator += 2;
    }
    return result;
  }

  double _taylor_series_sin(double x) {
    // Normalize x to be between -2π and 2π
    x = x % (2 * 3.141592653589793);
    double result = 0;
    double term = x;
    double x_squared = x * x;
    double factorial = 1;
    for (int i = 1; i <= 5; i++) {
      result += term / factorial;
      term = -term * x_squared;
      factorial *= (2 * i) * (2 * i + 1);
    }
    return result;
  }

  double _taylor_series_cos(double x) {
    // Normalize x to be between -2π and 2π
    x = x % (2 * 3.141592653589793);
    double result = 1;
    double term = 1;
    double x_squared = x * x;
    double factorial = 1;
    for (int i = 1; i <= 5; i++) {
      term = -term * x_squared / ((2 * i - 1) * (2 * i));
      result += term;
    }
    return result;
  }

  double _newton_raphson_sqrt(double x) {
    double guess = x / 2;
    for (int i = 0; i < 10; i++) {
      guess = 0.5 * (guess + x / guess);
    }
    return guess;
  }
}

// HiveLatLng is now defined in manual_adapters.dart
