// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_pipeline_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HivePipelineModelAdapter extends TypeAdapter<HivePipelineModel> {
  @override
  final int typeId = 4;

  @override
  HivePipelineModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HivePipelineModel(
      id: fields[0] as String?,
      createdAt: fields[1] as DateTime?,
      updatedAt: fields[2] as DateTime?,
      isSync: fields[3] as bool,
      isDeleted: fields[4] as bool,
      points: (fields[5] as List).cast<LatLng>(),
      title: fields[6] as String,
      properties: (fields[7] as Map?)?.cast<String, dynamic>(),
      areaId: fields[8] as String,
      segmentId: fields[9] as String,
    );
  }

  @override
  void write(BinaryWriter writer, HivePipelineModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.updatedAt)
      ..writeByte(3)
      ..write(obj.isSync)
      ..writeByte(4)
      ..write(obj.isDeleted)
      ..writeByte(5)
      ..write(obj.points)
      ..writeByte(6)
      ..write(obj.title)
      ..writeByte(7)
      ..write(obj.properties)
      ..writeByte(8)
      ..write(obj.areaId)
      ..writeByte(9)
      ..write(obj.segmentId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HivePipelineModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
