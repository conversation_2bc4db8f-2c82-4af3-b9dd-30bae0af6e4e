import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'hive_smart_meter_model.g.dart';

@HiveType(typeId: 4)
class HiveSmartMeterModel extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime createdAt;

  @HiveField(2)
  late DateTime updatedAt;

  @HiveField(3)
  late bool isSync;

  @HiveField(4)
  late bool isDeleted;

  @HiveField(5)
  late double latitude;

  @HiveField(6)
  late double longitude;

  @HiveField(7)
  late String title;

  @HiveField(8)
  late String type;

  @HiveField(9)
  late String status;

  @HiveField(10)
  DateTime? lastReading;

  @HiveField(11)
  late String areaId;

  @HiveField(12)
  double? pricePerUnit;

  @HiveField(13)
  late String reading;

  HiveSmartMeterModel({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isSync = false,
    this.isDeleted = false,
    required this.latitude,
    required this.longitude,
    required this.title,
    String? type,
    String? status,
    String? reading,
    this.lastReading,
    required this.areaId,
    this.pricePerUnit,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    this.type = type ?? 'Standard';
    this.status = status ?? 'Active';
    this.reading = reading ?? '0';
  }

  factory HiveSmartMeterModel.fromPosition({
    required LatLng position,
    required String title,
    required String areaId,
    String? type,
    String? status,
    String? reading,
    DateTime? lastReading,
    double? pricePerUnit,
  }) {
    return HiveSmartMeterModel(
      latitude: position.latitude,
      longitude: position.longitude,
      title: title,
      type: type ?? 'Standard',
      status: status ?? 'Active',
      reading: reading ?? '0',
      lastReading: lastReading,
      areaId: areaId,
      pricePerUnit: pricePerUnit,
    );
  }

  LatLng get position => LatLng(latitude, longitude);

  /// Mark the model as updated
  void markAsUpdated() {
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as synced with Firestore
  void markAsSynced() {
    isSync = true;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as deleted
  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Check if the model needs to be synced
  bool get needsSync => !isSync;

  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'type': 'smartMeter',
      'position': {'latitude': latitude, 'longitude': longitude},
      'title': title,
      'areaId': areaId,
      'metadata': {
        'createdAt': createdAt.millisecondsSinceEpoch,
        'lastUpdated': updatedAt.millisecondsSinceEpoch,
      },
    };
  }

  Map<String, dynamic> toFirestoreDetails() {
    return {
      'type': type,
      'reading': reading,
      'status': status,
      'lastReading': lastReading?.millisecondsSinceEpoch,
      'pricePerUnit': pricePerUnit,
      'isDeleted': isDeleted,
      'isSync': isSync,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': updatedAt.millisecondsSinceEpoch,
    };
  }
}
