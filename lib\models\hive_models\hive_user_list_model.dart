import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../../models/user_model.dart';

part 'hive_user_list_model.g.dart';

@HiveType(typeId: 8) // Using a new type ID for the user list model
class HiveUserListModel extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime createdAt;

  @HiveField(2)
  late DateTime updatedAt;

  @HiveField(3)
  late bool isSync;

  @HiveField(4)
  late bool isDeleted;

  @HiveField(5)
  late String userId;

  @HiveField(6)
  late String address;

  @HiveField(7)
  late String contact;

  @HiveField(8)
  late String name;

  @HiveField(9)
  late String areaId;

  @HiveField(10)
  late List<String> connections;

  // Optional position fields - only used if the user is also on the map
  @HiveField(11)
  double? latitude;

  @HiveField(12)
  double? longitude;

  @HiveField(13)
  String? customerNumber;

  @HiveField(14)
  String? connectionNumber;

  HiveUserListModel({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isSync = false,
    this.isDeleted = false,
    this.areaId = '', // Default areaId is empty
    this.address = '',
    this.contact = '',
    this.name = '',
    this.userId = '',
    this.connectionNumber = '',
    this.customerNumber = '',
    List<String>? connections,
    this.latitude,
    this.longitude,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    this.address = address ?? '';
    this.contact = contact ?? '';
    this.areaId = areaId ?? '';
    this.name = name ?? '';
    this.connectionNumber =
        connectionNumber ?? ''; // Default connection number is empty
    this.customerNumber =
        customerNumber ?? ''; // Default customer number is empty
    this.userId = id ?? ''; // Generate a new userId if not provided
    this.connections = connections ?? [];
  }

  // Create from UserModel
  factory HiveUserListModel.fromUserModel(UserModel user) {
    // Handle timestamps safely
    DateTime? createdAt;
    DateTime? updatedAt;

    try {
      if (user.createdAt != null) {
        createdAt = DateTime.fromMillisecondsSinceEpoch(user.createdAt!);
      }
    } catch (e) {
      // If there's an error parsing the timestamp, use current time
      createdAt = DateTime.now();
    }

    try {
      if (user.updatedAt != null) {
        updatedAt = DateTime.fromMillisecondsSinceEpoch(user.updatedAt!);
      }
    } catch (e) {
      // If there's an error parsing the timestamp, use current time
      updatedAt = DateTime.now();
    }

    return HiveUserListModel(
      id: user.id,
      name: user.name,
      address: user.address,
      contact: user.contact,
      userId: user.userId,
      connections: user.connections,
      latitude: user.position?.latitude,
      longitude: user.position?.longitude,
      createdAt: createdAt,
      customerNumber: user.customerNumber,
      connectionNumber: user.connectionNumber,
      areaId: user.areaId,
      isDeleted: false,
      isSync: false,
      updatedAt: updatedAt,
    );
  }

  // Convert to UserModel
  UserModel toUserModel() {
    return UserModel(
      id: id,
      position: (latitude != null && longitude != null)
          ? LatLng(latitude!, longitude!)
          : null,
      userId: userId,
      address: address,
      areaId: areaId,
      contact: contact,
      isDeleted: false,
      name: name,
      customerNumber: customerNumber ?? '',
      connectionNumber: connectionNumber ?? '',
      connections: connections,
      createdAt: createdAt.millisecondsSinceEpoch,
      updatedAt: updatedAt.millisecondsSinceEpoch,
    );
  }

  // For Firestore - detailed data
  Map<String, dynamic> toFirestore() {
    return {
      'address': address,
      'contact': contact,
      'name': name,
      'userId': userId,
      'areaId': areaId,
      'isDeleted': isDeleted,
      'isSync': isSync,
      'customer_number': customerNumber ?? '',
      'connection_number': connectionNumber ?? '',
      'connections': connections,
      'position': (latitude != null && longitude != null)
          ? {'latitude': latitude, 'longitude': longitude}
          : null,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// Mark the model as updated
  void markAsUpdated() {
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as synced with Firestore
  void markAsSynced() {
    isSync = true;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as deleted
  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Check if the model needs to be synced
  bool get needsSync => !isSync;
}
