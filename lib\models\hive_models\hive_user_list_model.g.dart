// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_user_list_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveUserListModelAdapter extends TypeAdapter<HiveUserListModel> {
  @override
  final int typeId = 8;

  @override
  HiveUserListModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveUserListModel(
      id: fields[0] as String?,
      createdAt: fields[1] as DateTime?,
      updatedAt: fields[2] as DateTime?,
      isSync: fields[3] as bool,
      isDeleted: fields[4] as bool,
      areaId: fields[9] as String,
      address: fields[6] as String,
      contact: fields[7] as String,
      name: fields[8] as String,
      userId: fields[5] as String,
      connectionNumber: fields[14] as String?,
      customerNumber: fields[13] as String?,
      connections: (fields[10] as List?)?.cast<String>(),
      latitude: fields[11] as double?,
      longitude: fields[12] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, HiveUserListModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.updatedAt)
      ..writeByte(3)
      ..write(obj.isSync)
      ..writeByte(4)
      ..write(obj.isDeleted)
      ..writeByte(5)
      ..write(obj.userId)
      ..writeByte(6)
      ..write(obj.address)
      ..writeByte(7)
      ..write(obj.contact)
      ..writeByte(8)
      ..write(obj.name)
      ..writeByte(9)
      ..write(obj.areaId)
      ..writeByte(10)
      ..write(obj.connections)
      ..writeByte(11)
      ..write(obj.latitude)
      ..writeByte(12)
      ..write(obj.longitude)
      ..writeByte(13)
      ..write(obj.customerNumber)
      ..writeByte(14)
      ..write(obj.connectionNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveUserListModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
