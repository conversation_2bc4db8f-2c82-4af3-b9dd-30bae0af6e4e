import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../../models/user_model.dart';

part 'hive_user_model.g.dart';

@HiveType(typeId: 1)
class HiveUserModel extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime createdAt;

  @HiveField(2)
  late DateTime updatedAt;

  @HiveField(3)
  late bool isSync;

  @HiveField(4)
  late bool isDeleted;

  @HiveField(5)
  late double latitude;

  @HiveField(6)
  late double longitude;

  @HiveField(7)
  late String title; // This is the display name on the map

  @HiveField(8)
  late String userId;

  @HiveField(9)
  late String address;

  @HiveField(10)
  late String contact;

  @HiveField(11)
  late String name;

  @HiveField(12)
  late String areaId;

  @HiveField(13)
  late List<String> connections;


  HiveUserModel({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isSync = false,
    this.isDeleted = false,
    required this.latitude,
    required this.longitude,
    required this.title,
    this.userId = '',
    this.address = '',
    this.contact = '',
    this.name = '',

    required this.areaId,
    List<String>? connections,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    this.contact = contact ?? '';
    this.address = address ?? '';

    this.name = name ?? title; // Ensure name is set to title if not provided
    this.userId = id ?? ''; // Generate a new userId if not provided
    this.connections = connections ?? [];
  }

  factory HiveUserModel.fromPosition({
    required LatLng position,
    required String title,
    required String areaId,
    String name = '',
    String contact = '',
    String address = '',
    String userId = '',
    List<String>? connections,
  }) {


    return HiveUserModel(
      latitude: position.latitude,
      longitude: position.longitude,
      title: title,
      name: name,
      contact: contact,
      address: address,
      userId: userId,
      areaId: areaId,
      connections: connections ?? [],
    );
  }

  factory HiveUserModel.withoutLocation({
    String? id,
    required String title,
    required String name,
    required String contact,
    required String address,
    required String userId,
    required String areaId,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isSync = false,
    bool isDeleted = false,
    List<String>? connections,
  }) {


    return HiveUserModel(
      id: id,
      latitude: 0.0, // Default value, indicating no location
      longitude: 0.0, // Default value, indicating no location
      title: title,
      name: name,
      contact: contact,
      address: address,
      userId: userId,
      areaId: areaId,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isSync: isSync,
      isDeleted: isDeleted,
      connections: connections ?? [],
    );
  }

  // Create from UserModel
  factory HiveUserModel.fromUserModel(UserModel user,
      {required String areaId}) {
    return HiveUserModel(
      id: user.id,
      latitude: user.position?.latitude ?? 0.0,
      longitude: user.position?.longitude ?? 0.0,
      title: user.name,
      name: user.name,
      contact: user.contact ?? '',
      address: user.address ?? '',
      userId: user.userId ?? user.id, // Use userId if available, otherwise use id
      areaId: areaId,
      connections: user.connections,
    );
  }

  LatLng get position => LatLng(latitude, longitude);

  /// Check if the user has a valid location
  bool get hasValidLocation => latitude != 0.0 || longitude != 0.0;

  /// Mark the model as updated
  void markAsUpdated() {
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
    debugPrint(
        'HiveUserModel: markAsUpdated called for user $id - lat=$latitude, lng=$longitude');
  }

  /// Mark the model as synced with Firestore
  void markAsSynced() {
    isSync = true;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as deleted
  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Check if the model needs to be synced
  bool get needsSync => !isSync;


  // Convert to UserModel
  UserModel toUserModel() {
    return UserModel(
      id: id,
      position: hasValidLocation ? position : null,
      userId: userId,
      address: address,
      contact: contact,
      name: name,
      connections: connections,
      createdAt: createdAt.millisecondsSinceEpoch,
      updatedAt: updatedAt.millisecondsSinceEpoch,
    );
  }

  // For elements collection - minimal data
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'type': 'user',
      'position': {'latitude': latitude, 'longitude': longitude},
      'title': title,
      'name': name,
      'userId': userId,
      'contact': contact,
      'address': address,
      'connections': connections,
      'areaId': areaId,
      // No metadata as per requirement
    };
  }

  // For user collection - detailed data
  Map<String, dynamic> toFirestoreDetails() {
    return {
      'connections': connections,
      'userId': userId,
      'address': address,
      'contact': contact,
      'name': name,
      'position': hasValidLocation
          ? {'latitude': latitude, 'longitude': longitude}
          : null,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': updatedAt.millisecondsSinceEpoch,
    };
  }
}
