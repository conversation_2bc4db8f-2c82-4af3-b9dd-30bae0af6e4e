// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveUserModelAdapter extends TypeAdapter<HiveUserModel> {
  @override
  final int typeId = 1;

  @override
  HiveUserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveUserModel(
      id: fields[0] as String?,
      createdAt: fields[1] as DateTime?,
      updatedAt: fields[2] as DateTime?,
      isSync: fields[3] as bool,
      isDeleted: fields[4] as bool,
      latitude: fields[5] as double,
      longitude: fields[6] as double,
      title: fields[7] as String,
      userId: fields[8] as String,
      address: fields[9] as String,
      contact: fields[10] as String,
      name: fields[11] as String,
      areaId: fields[12] as String,
      connections: (fields[13] as List?)?.cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, HiveUserModel obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.updatedAt)
      ..writeByte(3)
      ..write(obj.isSync)
      ..writeByte(4)
      ..write(obj.isDeleted)
      ..writeByte(5)
      ..write(obj.latitude)
      ..writeByte(6)
      ..write(obj.longitude)
      ..writeByte(7)
      ..write(obj.title)
      ..writeByte(8)
      ..write(obj.userId)
      ..writeByte(9)
      ..write(obj.address)
      ..writeByte(10)
      ..write(obj.contact)
      ..writeByte(11)
      ..write(obj.name)
      ..writeByte(12)
      ..write(obj.areaId)
      ..writeByte(13)
      ..write(obj.connections);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveUserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
