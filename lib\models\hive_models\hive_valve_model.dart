import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'hive_valve_model.g.dart';

@HiveType(typeId: 2)
class HiveValveModel extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime createdAt;

  @HiveField(2)
  late DateTime updatedAt;

  @HiveField(3)
  late bool isSync;

  @HiveField(4)
  late bool isDeleted;
  @HiveField(5)
  late double latitude;

  @HiveField(6)
  late double longitude;

  @HiveField(7)
  late String title;

  @HiveField(8)
  late String controlType;

  @HiveField(9)
  late String type;

  @HiveField(10)
  late String status;

  @HiveField(11)
  late double size;

  @HiveField(12)
  DateTime? lastMaintenance;

  @HiveField(13)
  late String areaId;

  @HiveField(14)
  double? pricePerUnit;

  HiveValveModel({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool isSync = false,
    bool isDeleted = false,
    required this.latitude,
    required this.longitude,
    required this.title,
    required this.type,
    required this.size,
    required this.controlType,
    String? status,
    this.lastMaintenance,
    required this.areaId,
    this.pricePerUnit,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    this.isSync = isSync;
    this.isDeleted = isDeleted;
    this.type = type;
    this.size = size;
    this.controlType = controlType;
    this.status = status ?? 'Operational';
  }

  factory HiveValveModel.fromPosition({
    required LatLng position,
    required String title,
    required String areaId,
    String? type,
    double? size,
    String? controlType,
    Map<String, dynamic>? specifications,
    String? status,
    DateTime? lastMaintenance,
    double? pricePerUnit,
  }) {
    return HiveValveModel(
      latitude: position.latitude,
      longitude: position.longitude,
      title: title,
      type: type ?? 'Gate',
      size: size ?? 20,
      controlType: controlType ?? 'Manual',
      status: status ?? 'Operational',
      lastMaintenance: lastMaintenance,
      areaId: areaId,
      pricePerUnit: pricePerUnit,
    );
  }

  LatLng get position => LatLng(latitude, longitude);

  /// Mark the model as updated
  void markAsUpdated() {
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as synced with Firestore
  void markAsSynced() {
    isSync = true;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Mark the model as deleted
  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();
    isSync = false;
    // Don't call save() directly as it might not be in a box yet
  }

  /// Check if the model needs to be synced
  bool get needsSync => !isSync;

  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'type': type,
      'position': {'latitude': latitude, 'longitude': longitude},
      'title': title,
      'areaId': areaId,
      'status': status,
      'size': size,
      'pricePerunit': pricePerUnit,
      'controlType': controlType,
      'isDeleted': isDeleted,
      'isSync': isSync,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': updatedAt.millisecondsSinceEpoch,
    };
  }

  Map<String, dynamic> toFirestoreDetails() {
    return {
      'status': status,
      'type': type,
      'controlType': controlType,
      'size': size,
      'lastMaintenance': lastMaintenance?.millisecondsSinceEpoch,
      'pricePerUnit': pricePerUnit,
      'isDeleted': isDeleted,
      'isSync': isSync,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUpdated': updatedAt.millisecondsSinceEpoch,
    };
  }
}
