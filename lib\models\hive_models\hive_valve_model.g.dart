// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_valve_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveValveModelAdapter extends TypeAdapter<HiveValveModel> {
  @override
  final int typeId = 2;

  @override
  HiveValveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveValveModel(
      id: fields[0] as String?,
      createdAt: fields[1] as DateTime?,
      updatedAt: fields[2] as DateTime?,
      isSync: fields[3] as bool,
      isDeleted: fields[4] as bool,
      latitude: fields[5] as double,
      longitude: fields[6] as double,
      title: fields[7] as String,
      type: fields[9] as String,
      size: fields[11] as double,
      controlType: fields[8] as String,
      status: fields[10] as String?,
      lastMaintenance: fields[12] as DateTime?,
      areaId: fields[13] as String,
      pricePerUnit: fields[14] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, HiveValveModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.updatedAt)
      ..writeByte(3)
      ..write(obj.isSync)
      ..writeByte(4)
      ..write(obj.isDeleted)
      ..writeByte(5)
      ..write(obj.latitude)
      ..writeByte(6)
      ..write(obj.longitude)
      ..writeByte(7)
      ..write(obj.title)
      ..writeByte(8)
      ..write(obj.controlType)
      ..writeByte(9)
      ..write(obj.type)
      ..writeByte(10)
      ..write(obj.status)
      ..writeByte(11)
      ..write(obj.size)
      ..writeByte(12)
      ..write(obj.lastMaintenance)
      ..writeByte(13)
      ..write(obj.areaId)
      ..writeByte(14)
      ..write(obj.pricePerUnit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveValveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
