
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';

class HiveLatLng {
  final double latitude;
  final double longitude;

  HiveLatLng(this.latitude, this.longitude);

  LatLng toLatLng() {
    return LatLng(latitude, longitude);
  }

  static HiveLatLng fromLatLng(LatLng latLng) {
    return HiveLatLng(latLng.latitude, latLng.longitude);
  }
}

class HiveLatLngAdapter extends TypeAdapter<HiveLatLng> {
  @override
  final int typeId = 7;

  @override
  HiveLatLng read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveLatLng(
      fields[0] as double,
      fields[1] as double,
    );
  }

  @override
  void write(BinaryWriter writer, HiveLatLng obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.latitude)
      ..writeByte(1)
      ..write(obj.longitude);
  }
}