import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/material.dart';
import 'hive_models/hive_pipeline_listing_model.dart';

class PipelineSegment {
  final String id;
  final LatLng start;
  final LatLng end;
  final String state;

  PipelineSegment({
    required this.id,
    required this.start,
    required this.end,
    this.state = 'Active',
  });
}

class Pipeline {
  final String id;
  final List<LatLng> points;
  final String? segmentId;
  final Map<String, PipelineSegment> segments;
  final String state;
  final String? title;
  final String? pipeType;
  final String? pipeDiameter;
  final Map<String, dynamic> properties;

  Pipeline({
    required this.id,
    required this.points,
    this.segmentId,
    Map<String, PipelineSegment>? segments,
    this.state = 'Active',
    this.title,
    this.pipeType,
    this.pipeDiameter,
    Map<String, dynamic>? properties,
  })  : segments = segments ?? {},
        properties = properties ?? {};

  // Create a Pipeline from a HivePipelineListingModel
  // Note: This requires points to be provided separately since they're not stored in the listing
  factory Pipeline.fromHivePipelineListing(
      HivePipelineListingModel hiveModel, List<LatLng> points,
      {Map<String, PipelineSegment>? segments}) {
    return Pipeline(
      id: hiveModel.id,
      points: points,
      state: hiveModel.state,
      title: hiveModel.title,
      pipeType: hiveModel.pipeType,
      pipeDiameter: hiveModel.pipeDiameter,
      properties: hiveModel.properties,
      segments: segments,
    );
  }

  // Helper method to get color based on segment state
  static Color getColorForState(String state) {
    switch (state.toLowerCase()) {
      case 'active':
        return Colors.blue;
      case 'inactive':
        return Colors.grey;
      case 'under maintenance':
        return Colors.orange;
      case 'planned':
        return Colors.purple;
      case 'decommissioned':
        return Colors.brown;
      case 'warning':
        return Colors.amber;
      case 'critical':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}

// Constants for position types
class PositionType {
  static const int user = 1;
  static const int valve = 2;
  static const int connector = 3;
  static const int meter = 4;
}

// Constants for connector types
class ConnectorType {
  static const int tee = 1;
  static const int elbow = 2;
  static const int coupling = 3;
  static const int cross = 4;

  // Get icon for connector type
  static IconData getIcon(int type) {
    switch (type) {
      case tee:
        return Icons.add_road;
      case elbow:
        return Icons.turn_right;
      case coupling:
        return Icons.linear_scale;
      case cross:
        return Icons.add;
      default:
        return Icons.connecting_airports_rounded;
    }
  }

  // Get name for connector type
  static String getName(int type) {
    switch (type) {
      case tee:
        return 'Tee Connector';
      case elbow:
        return 'Elbow Connector';
      case coupling:
        return 'Coupling Connector';
      case cross:
        return 'Cross Connector';
      default:
        return 'Unknown Connector';
    }
  }
}

class UserValvePosition {
  final String id;
  final LatLng position;
  final int type; // 1 for user, 2 for valve, 3 for connector
  final String title;
  final int?
      connectorType; // Null for non-connectors, otherwise refers to ConnectorType

  UserValvePosition({
    required this.id,
    required this.position,
    required this.type,
    required this.title,
    this.connectorType,
  });
}
