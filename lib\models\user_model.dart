import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gis/utils/latlng_converter.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    //! -------------------------------------------------------------------------------------------| BIO
    @Default('') String id,
    @NullableLatLngConverter() LatLng? position,
    int? createdAt,
    int? updatedAt,
    @Default('') address,
    @Default('') contact,
    @Default('') customerNumber,
    @Default('') connectionNumber,
    @Default('') name,
    @Default('') userId,
    @Default('') areaId,
    @Default(false) bool isDeleted,
    @Default([]) List<String> connections,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  // Create from Firestore document
  static UserModel fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Extract position from Firestore if available
    LatLng? position;
    if (data.containsKey('position') && data['position'] != null) {
      final pos = data['position'] as Map<String, dynamic>;
      if (pos.containsKey('latitude') && pos.containsKey('longitude')) {
        position = LatLng(
          pos['latitude'] as double,
          pos['longitude'] as double,
        );
      }
    }

    // Extract timestamps
    int? createdAt;
    int? updatedAt;
    if (data.containsKey('createdAt') && data['createdAt'] != null) {
      if (data['createdAt'] is Timestamp) {
        createdAt = (data['createdAt'] as Timestamp).millisecondsSinceEpoch;
      } else if (data['createdAt'] is int) {
        createdAt = data['createdAt'] as int;
      }
    }

    if (data.containsKey('lastUpdated') && data['lastUpdated'] != null) {
      if (data['lastUpdated'] is Timestamp) {
        updatedAt = (data['lastUpdated'] as Timestamp).millisecondsSinceEpoch;
      } else if (data['lastUpdated'] is int) {
        updatedAt = data['lastUpdated'] as int;
      }
    }

    return UserModel(
      id: doc.id,
      position: position,
      createdAt: createdAt,
      updatedAt: updatedAt,
      areaId: data['areaId'] ?? '',
      address: data['address'] ?? '',
      contact: data['contact'] ?? '',
      customerNumber: data['customerNumber'] ?? '',
      connectionNumber: data['connectionNumber'] ?? '',
      name: data['name'] ?? '',
      connections: data.containsKey('connections')
          ? List<String>.from(data['connections'] ?? [])
          : [],
    );
  }
}


// 0- inactive, 1-active, 2- admin, 3 - super_admin
