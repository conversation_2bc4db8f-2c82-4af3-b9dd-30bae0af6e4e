// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserModel _$UserModelFromJson(Map<String, dynamic> json) {
  return _UserModel.fromJson(json);
}

/// @nodoc
mixin _$UserModel {
//! -------------------------------------------------------------------------------------------| BIO
  String get id => throw _privateConstructorUsedError;
  @NullableLatLngConverter()
  LatLng? get position => throw _privateConstructorUsedError;
  int? get createdAt => throw _privateConstructorUsedError;
  int? get updatedAt => throw _privateConstructorUsedError;
  dynamic get address => throw _privateConstructorUsedError;
  dynamic get contact => throw _privateConstructorUsedError;
  dynamic get customerNumber => throw _privateConstructorUsedError;
  dynamic get connectionNumber => throw _privateConstructorUsedError;
  dynamic get name => throw _privateConstructorUsedError;
  dynamic get userId => throw _privateConstructorUsedError;
  dynamic get areaId => throw _privateConstructorUsedError;
  bool get isDeleted => throw _privateConstructorUsedError;
  List<String> get connections => throw _privateConstructorUsedError;

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call(
      {String id,
      @NullableLatLngConverter() LatLng? position,
      int? createdAt,
      int? updatedAt,
      dynamic address,
      dynamic contact,
      dynamic customerNumber,
      dynamic connectionNumber,
      dynamic name,
      dynamic userId,
      dynamic areaId,
      bool isDeleted,
      List<String> connections});
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? position = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? address = freezed,
    Object? contact = freezed,
    Object? customerNumber = freezed,
    Object? connectionNumber = freezed,
    Object? name = freezed,
    Object? userId = freezed,
    Object? areaId = freezed,
    Object? isDeleted = null,
    Object? connections = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as LatLng?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as dynamic,
      contact: freezed == contact
          ? _value.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as dynamic,
      customerNumber: freezed == customerNumber
          ? _value.customerNumber
          : customerNumber // ignore: cast_nullable_to_non_nullable
              as dynamic,
      connectionNumber: freezed == connectionNumber
          ? _value.connectionNumber
          : connectionNumber // ignore: cast_nullable_to_non_nullable
              as dynamic,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as dynamic,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      areaId: freezed == areaId
          ? _value.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
      connections: null == connections
          ? _value.connections
          : connections // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
          _$UserModelImpl value, $Res Function(_$UserModelImpl) then) =
      __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @NullableLatLngConverter() LatLng? position,
      int? createdAt,
      int? updatedAt,
      dynamic address,
      dynamic contact,
      dynamic customerNumber,
      dynamic connectionNumber,
      dynamic name,
      dynamic userId,
      dynamic areaId,
      bool isDeleted,
      List<String> connections});
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
      _$UserModelImpl _value, $Res Function(_$UserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? position = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? address = freezed,
    Object? contact = freezed,
    Object? customerNumber = freezed,
    Object? connectionNumber = freezed,
    Object? name = freezed,
    Object? userId = freezed,
    Object? areaId = freezed,
    Object? isDeleted = null,
    Object? connections = null,
  }) {
    return _then(_$UserModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as LatLng?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      address: freezed == address ? _value.address! : address,
      contact: freezed == contact ? _value.contact! : contact,
      customerNumber:
          freezed == customerNumber ? _value.customerNumber! : customerNumber,
      connectionNumber: freezed == connectionNumber
          ? _value.connectionNumber!
          : connectionNumber,
      name: freezed == name ? _value.name! : name,
      userId: freezed == userId ? _value.userId! : userId,
      areaId: freezed == areaId ? _value.areaId! : areaId,
      isDeleted: null == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool,
      connections: null == connections
          ? _value._connections
          : connections // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserModelImpl with DiagnosticableTreeMixin implements _UserModel {
  const _$UserModelImpl(
      {this.id = '',
      @NullableLatLngConverter() this.position,
      this.createdAt,
      this.updatedAt,
      this.address = '',
      this.contact = '',
      this.customerNumber = '',
      this.connectionNumber = '',
      this.name = '',
      this.userId = '',
      this.areaId = '',
      this.isDeleted = false,
      final List<String> connections = const []})
      : _connections = connections;

  factory _$UserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserModelImplFromJson(json);

//! -------------------------------------------------------------------------------------------| BIO
  @override
  @JsonKey()
  final String id;
  @override
  @NullableLatLngConverter()
  final LatLng? position;
  @override
  final int? createdAt;
  @override
  final int? updatedAt;
  @override
  @JsonKey()
  final dynamic address;
  @override
  @JsonKey()
  final dynamic contact;
  @override
  @JsonKey()
  final dynamic customerNumber;
  @override
  @JsonKey()
  final dynamic connectionNumber;
  @override
  @JsonKey()
  final dynamic name;
  @override
  @JsonKey()
  final dynamic userId;
  @override
  @JsonKey()
  final dynamic areaId;
  @override
  @JsonKey()
  final bool isDeleted;
  final List<String> _connections;
  @override
  @JsonKey()
  List<String> get connections {
    if (_connections is EqualUnmodifiableListView) return _connections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_connections);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserModel(id: $id, position: $position, createdAt: $createdAt, updatedAt: $updatedAt, address: $address, contact: $contact, customerNumber: $customerNumber, connectionNumber: $connectionNumber, name: $name, userId: $userId, areaId: $areaId, isDeleted: $isDeleted, connections: $connections)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserModel'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('position', position))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('updatedAt', updatedAt))
      ..add(DiagnosticsProperty('address', address))
      ..add(DiagnosticsProperty('contact', contact))
      ..add(DiagnosticsProperty('customerNumber', customerNumber))
      ..add(DiagnosticsProperty('connectionNumber', connectionNumber))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('userId', userId))
      ..add(DiagnosticsProperty('areaId', areaId))
      ..add(DiagnosticsProperty('isDeleted', isDeleted))
      ..add(DiagnosticsProperty('connections', connections));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other.address, address) &&
            const DeepCollectionEquality().equals(other.contact, contact) &&
            const DeepCollectionEquality()
                .equals(other.customerNumber, customerNumber) &&
            const DeepCollectionEquality()
                .equals(other.connectionNumber, connectionNumber) &&
            const DeepCollectionEquality().equals(other.name, name) &&
            const DeepCollectionEquality().equals(other.userId, userId) &&
            const DeepCollectionEquality().equals(other.areaId, areaId) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            const DeepCollectionEquality()
                .equals(other._connections, _connections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      position,
      createdAt,
      updatedAt,
      const DeepCollectionEquality().hash(address),
      const DeepCollectionEquality().hash(contact),
      const DeepCollectionEquality().hash(customerNumber),
      const DeepCollectionEquality().hash(connectionNumber),
      const DeepCollectionEquality().hash(name),
      const DeepCollectionEquality().hash(userId),
      const DeepCollectionEquality().hash(areaId),
      isDeleted,
      const DeepCollectionEquality().hash(_connections));

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserModelImplToJson(
      this,
    );
  }
}

abstract class _UserModel implements UserModel {
  const factory _UserModel(
      {final String id,
      @NullableLatLngConverter() final LatLng? position,
      final int? createdAt,
      final int? updatedAt,
      final dynamic address,
      final dynamic contact,
      final dynamic customerNumber,
      final dynamic connectionNumber,
      final dynamic name,
      final dynamic userId,
      final dynamic areaId,
      final bool isDeleted,
      final List<String> connections}) = _$UserModelImpl;

  factory _UserModel.fromJson(Map<String, dynamic> json) =
      _$UserModelImpl.fromJson;

//! -------------------------------------------------------------------------------------------| BIO
  @override
  String get id;
  @override
  @NullableLatLngConverter()
  LatLng? get position;
  @override
  int? get createdAt;
  @override
  int? get updatedAt;
  @override
  dynamic get address;
  @override
  dynamic get contact;
  @override
  dynamic get customerNumber;
  @override
  dynamic get connectionNumber;
  @override
  dynamic get name;
  @override
  dynamic get userId;
  @override
  dynamic get areaId;
  @override
  bool get isDeleted;
  @override
  List<String> get connections;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
