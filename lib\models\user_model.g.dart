// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserModelImpl _$$UserModelImplFromJson(Map<String, dynamic> json) =>
    _$UserModelImpl(
      id: json['id'] as String? ?? '',
      position: const NullableLatLngConverter()
          .fromJson(json['position'] as Map<String, dynamic>?),
      createdAt: (json['createdAt'] as num?)?.toInt(),
      updatedAt: (json['updatedAt'] as num?)?.toInt(),
      address: json['address'] ?? '',
      contact: json['contact'] ?? '',
      customerNumber: json['customerNumber'] ?? '',
      connectionNumber: json['connectionNumber'] ?? '',
      name: json['name'] ?? '',
      userId: json['userId'] ?? '',
      areaId: json['areaId'] ?? '',
      isDeleted: json['isDeleted'] as bool? ?? false,
      connections: (json['connections'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$UserModelImplToJson(_$UserModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'position': const NullableLatLngConverter().toJson(instance.position),
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'address': instance.address,
      'contact': instance.contact,
      'customerNumber': instance.customerNumber,
      'connectionNumber': instance.connectionNumber,
      'name': instance.name,
      'userId': instance.userId,
      'areaId': instance.areaId,
      'isDeleted': instance.isDeleted,
      'connections': instance.connections,
    };
