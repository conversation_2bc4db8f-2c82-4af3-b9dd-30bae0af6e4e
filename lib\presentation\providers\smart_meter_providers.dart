import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../domain/entities/smart_meter.dart';
import '../../domain/repositories/smart_meter_repository.dart';
import '../../domain/usecases/smart_meter_usecases.dart';

/// Provider for the smart meter repository
final smartMeterRepositoryProvider = Provider<SmartMeterRepository>((ref) {
  throw UnimplementedError('SmartMeterRepository provider not implemented');
});

/// Provider for the get all smart meters use case
final getAllSmartMetersProvider = Provider<GetAllSmartMeters>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return GetAllSmartMeters(repository);
});

/// Provider for the get smart meter by ID use case
final getSmartMeterByIdProvider = Provider<GetSmartMeterById>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return GetSmartMeterById(repository);
});

/// Provider for the save smart meter use case
final saveSmartMeterProvider = Provider<SaveSmartMeter>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return SaveSmartMeter(repository);
});

/// Provider for the delete smart meter use case
final deleteSmartMeterProvider = Provider<DeleteSmartMeter>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return DeleteSmartMeter(repository);
});

/// Provider for the update smart meter reading use case
final updateSmartMeterReadingProvider = Provider<UpdateSmartMeterReading>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return UpdateSmartMeterReading(repository);
});

/// Provider for the update smart meter position use case
final updateSmartMeterPositionProvider = Provider<UpdateSmartMeterPosition>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return UpdateSmartMeterPosition(repository);
});

/// Provider for the sync smart meter use case
final syncSmartMeterProvider = Provider<SyncSmartMeter>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return SyncSmartMeter(repository);
});

/// Provider for the sync all smart meters use case
final syncAllSmartMetersProvider = Provider<SyncAllSmartMeters>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return SyncAllSmartMeters(repository);
});

/// Provider for the clear and fetch all smart meters use case
final clearAndFetchAllSmartMetersProvider = Provider<ClearAndFetchAllSmartMeters>((ref) {
  final repository = ref.watch(smartMeterRepositoryProvider);
  return ClearAndFetchAllSmartMeters(repository);
});

/// State notifier for smart meters
class SmartMeterNotifier extends StateNotifier<List<SmartMeter>> {
  final GetAllSmartMeters _getAllSmartMeters;
  final SaveSmartMeter _saveSmartMeter;
  final DeleteSmartMeter _deleteSmartMeter;
  final SyncSmartMeter _syncSmartMeter;
  final SyncAllSmartMeters _syncAllSmartMeters;
  final ClearAndFetchAllSmartMeters _clearAndFetchAllSmartMeters;

  SmartMeterNotifier({
    required GetAllSmartMeters getAllSmartMeters,
    required SaveSmartMeter saveSmartMeter,
    required DeleteSmartMeter deleteSmartMeter,
    required SyncSmartMeter syncSmartMeter,
    required SyncAllSmartMeters syncAllSmartMeters,
    required ClearAndFetchAllSmartMeters clearAndFetchAllSmartMeters,
  })  : _getAllSmartMeters = getAllSmartMeters,
        _saveSmartMeter = saveSmartMeter,
        _deleteSmartMeter = deleteSmartMeter,
        _syncSmartMeter = syncSmartMeter,
        _syncAllSmartMeters = syncAllSmartMeters,
        _clearAndFetchAllSmartMeters = clearAndFetchAllSmartMeters,
        super([]);

  /// Load all smart meters
  Future<void> loadSmartMeters() async {
    state = await _getAllSmartMeters();
  }

  /// Add a smart meter
  Future<void> addSmartMeter(SmartMeter smartMeter) async {
    await _saveSmartMeter(smartMeter);
    state = [...state, smartMeter];
  }

  /// Update a smart meter
  Future<void> updateSmartMeter(SmartMeter smartMeter) async {
    await _saveSmartMeter(smartMeter);
    state = [
      for (final meter in state)
        if (meter.id == smartMeter.id) smartMeter else meter
    ];
  }

  /// Delete a smart meter
  Future<void> deleteSmartMeter(String id, {bool permanent = false}) async {
    await _deleteSmartMeter(id, permanent: permanent);
    state = state.where((meter) => meter.id != id).toList();
  }

  /// Sync a smart meter
  Future<void> syncSmartMeter(SmartMeter smartMeter) async {
    await _syncSmartMeter(smartMeter);
    state = [
      for (final meter in state)
        if (meter.id == smartMeter.id)
          smartMeter.copyWith(isSync: true)
        else
          meter
    ];
  }

  /// Sync all smart meters
  Future<void> syncAllSmartMeters() async {
    await _syncAllSmartMeters();
    state = state.map((meter) => meter.copyWith(isSync: true)).toList();
  }

  /// Clear and fetch all smart meters
  Future<void> clearAndFetchAllSmartMeters() async {
    await _clearAndFetchAllSmartMeters();
    state = await _getAllSmartMeters();
  }
}

/// Provider for the smart meter notifier
final smartMeterNotifierProvider =
    StateNotifierProvider<SmartMeterNotifier, List<SmartMeter>>((ref) {
  return SmartMeterNotifier(
    getAllSmartMeters: ref.watch(getAllSmartMetersProvider),
    saveSmartMeter: ref.watch(saveSmartMeterProvider),
    deleteSmartMeter: ref.watch(deleteSmartMeterProvider),
    syncSmartMeter: ref.watch(syncSmartMeterProvider),
    syncAllSmartMeters: ref.watch(syncAllSmartMetersProvider),
    clearAndFetchAllSmartMeters: ref.watch(clearAndFetchAllSmartMetersProvider),
  );
});
