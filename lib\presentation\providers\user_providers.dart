import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/usecases/user_usecases.dart';

/// Provider for the user repository
final userRepositoryProvider = Provider<UserRepository>((ref) {
  throw UnimplementedError('UserRepository provider not implemented');
});

/// Provider for the get all users use case
final getAllUsersProvider = Provider<GetAllUsers>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return GetAllUsers(repository);
});

/// Provider for the get user by ID use case
final getUserByIdProvider = Provider<GetUserById>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return GetUserById(repository);
});

/// Provider for the save user use case
final saveUserProvider = Provider<SaveUser>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return SaveUser(repository);
});

/// Provider for the delete user use case
final deleteUserProvider = Provider<DeleteUser>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return DeleteUser(repository);
});

/// Provider for the generate fake users use case
final generateFakeUsersProvider = Provider<GenerateFakeUsers>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return GenerateFakeUsers(repository);
});

/// Provider for the get users with location use case
final getUsersWithLocationProvider = Provider<GetUsersWithLocation>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return GetUsersWithLocation(repository);
});

/// Provider for the update user position use case
final updateUserPositionProvider = Provider<UpdateUserPosition>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return UpdateUserPosition(repository);
});

/// Provider for the sync user use case
final syncUserProvider = Provider<SyncUser>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return SyncUser(repository);
});

/// Provider for the sync all users use case
final syncAllUsersProvider = Provider<SyncAllUsers>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return SyncAllUsers(repository);
});

/// Provider for the clear and fetch all users use case
final clearAndFetchAllUsersProvider = Provider<ClearAndFetchAllUsers>((ref) {
  final repository = ref.watch(userRepositoryProvider);
  return ClearAndFetchAllUsers(repository);
});

/// State notifier for users
class UserNotifier extends StateNotifier<List<User>> {
  final GetAllUsers _getAllUsers;
  final SaveUser _saveUser;
  final DeleteUser _deleteUser;
  final GenerateFakeUsers _generateFakeUsers;
  final UpdateUserPosition _updateUserPosition;
  final SyncUser _syncUser;
  final SyncAllUsers _syncAllUsers;
  final ClearAndFetchAllUsers _clearAndFetchAllUsers;

  UserNotifier({
    required GetAllUsers getAllUsers,
    required SaveUser saveUser,
    required DeleteUser deleteUser,
    required GenerateFakeUsers generateFakeUsers,
    required UpdateUserPosition updateUserPosition,
    required SyncUser syncUser,
    required SyncAllUsers syncAllUsers,
    required ClearAndFetchAllUsers clearAndFetchAllUsers,
  })  : _getAllUsers = getAllUsers,
        _saveUser = saveUser,
        _deleteUser = deleteUser,
        _generateFakeUsers = generateFakeUsers,
        _updateUserPosition = updateUserPosition,
        _syncUser = syncUser,
        _syncAllUsers = syncAllUsers,
        _clearAndFetchAllUsers = clearAndFetchAllUsers,
        super([]);

  /// Load all users
  Future<void> loadUsers() async {
    state = await _getAllUsers();
  }

  /// Add a user
  Future<void> addUser(User user) async {
    await _saveUser(user);
    state = [...state, user];
  }

  /// Update a user
  Future<void> updateUser(User user) async {
    await _saveUser(user);
    state = [
      for (final u in state)
        if (u.id == user.id) user else u
    ];
  }

  /// Delete a user
  Future<void> deleteUser(String id, {bool permanent = false}) async {
    await _deleteUser(id, permanent: permanent);
    state = state.where((user) => user.id != id).toList();
  }

  /// Generate fake users
  Future<List<User>> generateFakeUsers(int count) async {
    final users = await _generateFakeUsers(count);
    state = [...state, ...users];
    return users;
  }

  /// Update user position
  Future<void> updatePosition(String id, double latitude, double longitude) async {
    await _updateUserPosition(id, latitude, longitude);
    state = [
      for (final user in state)
        if (user.id == id)
          user.copyWith(
            latitude: latitude,
            longitude: longitude,
            updatedAt: DateTime.now(),
          )
        else
          user
    ];
  }

  /// Sync a user
  Future<void> syncUser(User user) async {
    await _syncUser(user);
    state = [
      for (final u in state)
        if (u.id == user.id)
          user.copyWith(isSync: true)
        else
          u
    ];
  }

  /// Sync all users
  Future<void> syncAllUsers() async {
    await _syncAllUsers();
    state = state.map((user) => user.copyWith(isSync: true)).toList();
  }

  /// Clear and fetch all users
  Future<void> clearAndFetchAllUsers() async {
    await _clearAndFetchAllUsers();
    state = await _getAllUsers();
  }
}

/// Provider for the user notifier
final userNotifierProvider =
    StateNotifierProvider<UserNotifier, List<User>>((ref) {
  return UserNotifier(
    getAllUsers: ref.watch(getAllUsersProvider),
    saveUser: ref.watch(saveUserProvider),
    deleteUser: ref.watch(deleteUserProvider),
    generateFakeUsers: ref.watch(generateFakeUsersProvider),
    updateUserPosition: ref.watch(updateUserPositionProvider),
    syncUser: ref.watch(syncUserProvider),
    syncAllUsers: ref.watch(syncAllUsersProvider),
    clearAndFetchAllUsers: ref.watch(clearAndFetchAllUsersProvider),
  );
});
