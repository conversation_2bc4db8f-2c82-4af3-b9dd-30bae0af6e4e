import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../domain/entities/valve.dart';
import '../../domain/repositories/valve_repository.dart';
import '../../domain/usecases/valve_usecases.dart';

/// Provider for the valve repository
final valveRepositoryProvider = Provider<ValveRepository>((ref) {
  throw UnimplementedError('ValveRepository provider not implemented');
});

/// Provider for the get all valves use case
final getAllValvesProvider = Provider<GetAllValves>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return GetAllValves(repository);
});

/// Provider for the get valve by ID use case
final getValveByIdProvider = Provider<GetValveById>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return GetValveById(repository);
});

/// Provider for the save valve use case
final saveValveProvider = Provider<SaveValve>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return SaveValve(repository);
});

/// Provider for the delete valve use case
final deleteValveProvider = Provider<DeleteValve>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return DeleteValve(repository);
});

/// Provider for the update valve status use case
final updateValveStatusProvider = Provider<UpdateValveStatus>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return UpdateValveStatus(repository);
});

/// Provider for the record valve maintenance use case
final recordValveMaintenanceProvider = Provider<RecordValveMaintenance>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return RecordValveMaintenance(repository);
});

/// Provider for the update valve position use case
final updateValvePositionProvider = Provider<UpdateValvePosition>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return UpdateValvePosition(repository);
});

/// Provider for the sync valve use case
final syncValveProvider = Provider<SyncValve>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return SyncValve(repository);
});

/// Provider for the sync all valves use case
final syncAllValvesProvider = Provider<SyncAllValves>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return SyncAllValves(repository);
});

/// Provider for the clear and fetch all valves use case
final clearAndFetchAllValvesProvider = Provider<ClearAndFetchAllValves>((ref) {
  final repository = ref.watch(valveRepositoryProvider);
  return ClearAndFetchAllValves(repository);
});

/// State notifier for valves
class ValveNotifier extends StateNotifier<List<Valve>> {
  final GetAllValves _getAllValves;
  final SaveValve _saveValve;
  final DeleteValve _deleteValve;
  final UpdateValveStatus _updateValveStatus;
  final RecordValveMaintenance _recordValveMaintenance;
  final UpdateValvePosition _updateValvePosition;
  final SyncValve _syncValve;
  final SyncAllValves _syncAllValves;
  final ClearAndFetchAllValves _clearAndFetchAllValves;

  ValveNotifier({
    required GetAllValves getAllValves,
    required SaveValve saveValve,
    required DeleteValve deleteValve,
    required UpdateValveStatus updateValveStatus,
    required RecordValveMaintenance recordValveMaintenance,
    required UpdateValvePosition updateValvePosition,
    required SyncValve syncValve,
    required SyncAllValves syncAllValves,
    required ClearAndFetchAllValves clearAndFetchAllValves,
  })  : _getAllValves = getAllValves,
        _saveValve = saveValve,
        _deleteValve = deleteValve,
        _updateValveStatus = updateValveStatus,
        _recordValveMaintenance = recordValveMaintenance,
        _updateValvePosition = updateValvePosition,
        _syncValve = syncValve,
        _syncAllValves = syncAllValves,
        _clearAndFetchAllValves = clearAndFetchAllValves,
        super([]);

  /// Load all valves
  Future<void> loadValves() async {
    state = await _getAllValves();
  }

  /// Add a valve
  Future<void> addValve(Valve valve) async {
    await _saveValve(valve);
    state = [...state, valve];
  }

  /// Update a valve
  Future<void> updateValve(Valve valve) async {
    await _saveValve(valve);
    state = [
      for (final v in state)
        if (v.id == valve.id) valve else v
    ];
  }

  /// Delete a valve
  Future<void> deleteValve(String id, {bool permanent = false}) async {
    await _deleteValve(id, permanent: permanent);
    state = state.where((valve) => valve.id != id).toList();
  }

  /// Update valve status
  Future<void> updateStatus(String id, String status) async {
    await _updateValveStatus(id, status);
    state = [
      for (final valve in state)
        if (valve.id == id)
          valve.copyWith(status: status, updatedAt: DateTime.now())
        else
          valve
    ];
  }

  /// Record valve maintenance
  Future<void> recordMaintenance(String id) async {
    await _recordValveMaintenance(id);
    state = [
      for (final valve in state)
        if (valve.id == id)
          valve.copyWith(
            lastMaintenance: DateTime.now(),
            updatedAt: DateTime.now(),
          )
        else
          valve
    ];
  }

  /// Update valve position
  Future<void> updatePosition(String id, double latitude, double longitude) async {
    await _updateValvePosition(id, latitude, longitude);
    state = [
      for (final valve in state)
        if (valve.id == id)
          valve.copyWith(
            latitude: latitude,
            longitude: longitude,
            updatedAt: DateTime.now(),
          )
        else
          valve
    ];
  }

  /// Sync a valve
  Future<void> syncValve(Valve valve) async {
    await _syncValve(valve);
    state = [
      for (final v in state)
        if (v.id == valve.id)
          valve.copyWith(isSync: true)
        else
          v
    ];
  }

  /// Sync all valves
  Future<void> syncAllValves() async {
    await _syncAllValves();
    state = state.map((valve) => valve.copyWith(isSync: true)).toList();
  }

  /// Clear and fetch all valves
  Future<void> clearAndFetchAllValves() async {
    await _clearAndFetchAllValves();
    state = await _getAllValves();
  }
}

/// Provider for the valve notifier
final valveNotifierProvider =
    StateNotifierProvider<ValveNotifier, List<Valve>>((ref) {
  return ValveNotifier(
    getAllValves: ref.watch(getAllValvesProvider),
    saveValve: ref.watch(saveValveProvider),
    deleteValve: ref.watch(deleteValveProvider),
    updateValveStatus: ref.watch(updateValveStatusProvider),
    recordValveMaintenance: ref.watch(recordValveMaintenanceProvider),
    updateValvePosition: ref.watch(updateValvePositionProvider),
    syncValve: ref.watch(syncValveProvider),
    syncAllValves: ref.watch(syncAllValvesProvider),
    clearAndFetchAllValves: ref.watch(clearAndFetchAllValvesProvider),
  );
});
