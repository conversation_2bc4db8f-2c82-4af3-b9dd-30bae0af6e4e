import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../domain/entities/connector.dart';

/// A reusable form widget for adding or editing a connector
class ConnectorForm extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController diameterController;
  final TextEditingController widthController;
  final TextEditingController heightController;
  final TextEditingController modelNumberController;
  final TextEditingController descriptionController;
  final TextEditingController priceController;
  final String selectedConnectorType;
  final String selectedMaterial;
  final String selectedManufacturer;
  final String selectedPressureRating;
  final Function(String) onConnectorTypeChanged;
  final Function(String) onMaterialChanged;
  final Function(String) onManufacturerChanged;
  final Function(String) onPressureRatingChanged;
  final VoidCallback onSubmit;
  final String submitButtonText;

  const ConnectorForm({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.diameterController,
    required this.widthController,
    required this.heightController,
    required this.modelNumberController,
    required this.descriptionController,
    required this.priceController,
    required this.selectedConnectorType,
    required this.selectedMaterial,
    required this.selectedManufacturer,
    required this.selectedPressureRating,
    required this.onConnectorTypeChanged,
    required this.onMaterialChanged,
    required this.onManufacturerChanged,
    required this.onPressureRatingChanged,
    required this.onSubmit,
    this.submitButtonText = 'Save',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          CustomTextField(
            controller: nameController,
            label: 'Connector Name',
            hint: 'Enter connector name',
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedConnectorType,
            options: const [
              'tee',
              'elbow',
              'coupling',
              'cross',
              'socket',
              'reducer',
              'cap',
              'union',
              'adapter',
              'nipple',
              'flange',
            ],
            label: 'Connector Type',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onConnectorTypeChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedMaterial,
            options: const [
              'PVC',
              'Steel',
              'Cast Iron',
              'Brass',
              'Copper',
              'HDPE',
              'Other',
            ],
            label: 'Material',
            onChanged: (value) {
              if (value != null) {
                onMaterialChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: diameterController,
            label: 'Diameter',
            hint: 'e.g., 100mm, 4 inches',
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: widthController,
            label: 'Width',
            hint: 'e.g., 150mm',
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: heightController,
            label: 'Height',
            hint: 'e.g., 100mm',
          ),
          const SizedBox(height: 16),
          CustomTextField.price(
            controller: priceController,
            label: 'Price Per Unit',
            hint: 'e.g., 250.00',
            currency: 'NPR',
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedManufacturer,
            options: const [
              'Generic',
              'ABC Pipes',
              'XYZ Fittings',
              'Local Manufacturer',
              'Other',
            ],
            label: 'Manufacturer',
            onChanged: (value) {
              if (value != null) {
                onManufacturerChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: modelNumberController,
            label: 'Model Number',
            hint: 'e.g., T-100-S',
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedPressureRating,
            options: const [
              'Standard (10 bar)',
              'Low (5 bar)',
              'Medium (15 bar)',
              'High (25 bar)',
              'Very High (40+ bar)',
            ],
            label: 'Pressure Rating',
            onChanged: (value) {
              if (value != null) {
                onPressureRatingChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: descriptionController,
            label: 'Description',
            hint: 'Enter additional details',
            maxLines: 3,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: onSubmit,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
            ),
            child: Text(submitButtonText),
          ),
        ],
      ),
    );
  }

  /// Factory constructor for creating a form for adding a new connector
  factory ConnectorForm.add({
    required GlobalKey<FormState> formKey,
    required TextEditingController nameController,
    required TextEditingController diameterController,
    required TextEditingController widthController,
    required TextEditingController heightController,
    required TextEditingController modelNumberController,
    required TextEditingController descriptionController,
    required TextEditingController priceController,
    required String selectedConnectorType,
    required String selectedMaterial,
    required String selectedManufacturer,
    required String selectedPressureRating,
    required Function(String) onConnectorTypeChanged,
    required Function(String) onMaterialChanged,
    required Function(String) onManufacturerChanged,
    required Function(String) onPressureRatingChanged,
    required VoidCallback onSubmit,
  }) {
    return ConnectorForm(
      formKey: formKey,
      nameController: nameController,
      diameterController: diameterController,
      widthController: widthController,
      heightController: heightController,
      modelNumberController: modelNumberController,
      descriptionController: descriptionController,
      priceController: priceController,
      selectedConnectorType: selectedConnectorType,
      selectedMaterial: selectedMaterial,
      selectedManufacturer: selectedManufacturer,
      selectedPressureRating: selectedPressureRating,
      onConnectorTypeChanged: onConnectorTypeChanged,
      onMaterialChanged: onMaterialChanged,
      onManufacturerChanged: onManufacturerChanged,
      onPressureRatingChanged: onPressureRatingChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Add Connector',
    );
  }

  /// Factory constructor for creating a form for editing an existing connector
  factory ConnectorForm.edit({
    required GlobalKey<FormState> formKey,
    required Connector connector,
    required TextEditingController nameController,
    required TextEditingController diameterController,
    required TextEditingController widthController,
    required TextEditingController heightController,
    required TextEditingController modelNumberController,
    required TextEditingController descriptionController,
    required TextEditingController priceController,
    required String selectedConnectorType,
    required String selectedMaterial,
    required String selectedManufacturer,
    required String selectedPressureRating,
    required Function(String) onConnectorTypeChanged,
    required Function(String) onMaterialChanged,
    required Function(String) onManufacturerChanged,
    required Function(String) onPressureRatingChanged,
    required VoidCallback onSubmit,
  }) {
    return ConnectorForm(
      formKey: formKey,
      nameController: nameController,
      diameterController: diameterController,
      widthController: widthController,
      heightController: heightController,
      modelNumberController: modelNumberController,
      descriptionController: descriptionController,
      priceController: priceController,
      selectedConnectorType: selectedConnectorType,
      selectedMaterial: selectedMaterial,
      selectedManufacturer: selectedManufacturer,
      selectedPressureRating: selectedPressureRating,
      onConnectorTypeChanged: onConnectorTypeChanged,
      onMaterialChanged: onMaterialChanged,
      onManufacturerChanged: onManufacturerChanged,
      onPressureRatingChanged: onPressureRatingChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Save Changes',
    );
  }
}
