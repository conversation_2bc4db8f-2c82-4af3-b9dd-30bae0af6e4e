import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../domain/entities/pipeline.dart';

/// A reusable form widget for adding or editing a pipeline
class PipelineForm extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController wallThicknessController;
  final TextEditingController standardController;
  final TextEditingController descriptionController;
  final TextEditingController priceController;
  final String selectedPipeType;
  final String selectedPipeDiameter;
  final String selectedMaterial;
  final String selectedPressureRating;
  final String selectedTemperatureRating;
  final String selectedManufacturer;
  final String selectedState;
  final Function(String) onPipeTypeChanged;
  final Function(String) onPipeDiameterChanged;
  final Function(String) onMaterialChanged;
  final Function(String) onPressureRatingChanged;
  final Function(String) onTemperatureRatingChanged;
  final Function(String) onManufacturerChanged;
  final Function(String) onStateChanged;
  final VoidCallback onSubmit;
  final String submitButtonText;

  const PipelineForm({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.wallThicknessController,
    required this.standardController,
    required this.descriptionController,
    required this.priceController,
    required this.selectedPipeType,
    required this.selectedPipeDiameter,
    required this.selectedMaterial,
    required this.selectedPressureRating,
    required this.selectedTemperatureRating,
    required this.selectedManufacturer,
    required this.selectedState,
    required this.onPipeTypeChanged,
    required this.onPipeDiameterChanged,
    required this.onMaterialChanged,
    required this.onPressureRatingChanged,
    required this.onTemperatureRatingChanged,
    required this.onManufacturerChanged,
    required this.onStateChanged,
    required this.onSubmit,
    this.submitButtonText = 'Save',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          CustomTextField(
            controller: nameController,
            label: 'Pipeline Name',
            hint: 'Enter pipeline name',
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedPipeType,
            options: const [
              'PVC',
              'Steel',
              'HDPE',
              'Cast Iron',
              'Copper',
              'Concrete',
              'Other',
            ],
            label: 'Pipe Type',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onPipeTypeChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedPipeDiameter,
            options: const [
              '50mm',
              '75mm',
              '100mm',
              '150mm',
              '200mm',
              '250mm',
              '300mm',
              'Other',
            ],
            label: 'Pipe Diameter',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onPipeDiameterChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: wallThicknessController,
            label: 'Wall Thickness',
            hint: 'e.g., 5mm, 0.25 inches',
          ),
          const SizedBox(height: 16),
          CustomTextField.price(
            controller: priceController,
            label: 'Price Per Meter',
            hint: 'e.g., 150.00',
            currency: 'NPR',
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedMaterial,
            options: const [
              'PVC',
              'Steel',
              'Cast Iron',
              'Ductile Iron',
              'HDPE',
              'Copper',
              'Concrete',
              'Other',
            ],
            label: 'Material',
            onChanged: (value) {
              if (value != null) {
                onMaterialChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedPressureRating,
            options: const [
              'Standard (10 bar)',
              'Low (5 bar)',
              'Medium (15 bar)',
              'High (25 bar)',
              'Very High (40+ bar)',
            ],
            label: 'Pressure Rating',
            onChanged: (value) {
              if (value != null) {
                onPressureRatingChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedTemperatureRating,
            options: const [
              'Normal (-20°C to 60°C)',
              'Low (-40°C to 40°C)',
              'High (0°C to 100°C)',
              'Very High (0°C to 200°C)',
            ],
            label: 'Temperature Rating',
            onChanged: (value) {
              if (value != null) {
                onTemperatureRatingChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedManufacturer,
            options: const [
              'Generic',
              'ABC Pipes',
              'XYZ Fittings',
              'Local Manufacturer',
              'Other',
            ],
            label: 'Manufacturer',
            onChanged: (value) {
              if (value != null) {
                onManufacturerChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: standardController,
            label: 'Standard/Specification',
            hint: 'e.g., ASTM D1785, ISO 4427',
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedState,
            options: const [
              'Active',
              'Inactive',
              'Under Maintenance',
              'Planned',
            ],
            label: 'Default State',
            onChanged: (value) {
              if (value != null) {
                onStateChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: descriptionController,
            label: 'Description',
            hint: 'Enter additional details',
            maxLines: 3,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: onSubmit,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
            ),
            child: Text(submitButtonText),
          ),
        ],
      ),
    );
  }

  /// Factory constructor for creating a form for adding a new pipeline
  factory PipelineForm.add({
    required GlobalKey<FormState> formKey,
    required TextEditingController nameController,
    required TextEditingController wallThicknessController,
    required TextEditingController standardController,
    required TextEditingController descriptionController,
    required TextEditingController priceController,
    required String selectedPipeType,
    required String selectedPipeDiameter,
    required String selectedMaterial,
    required String selectedPressureRating,
    required String selectedTemperatureRating,
    required String selectedManufacturer,
    required String selectedState,
    required Function(String) onPipeTypeChanged,
    required Function(String) onPipeDiameterChanged,
    required Function(String) onMaterialChanged,
    required Function(String) onPressureRatingChanged,
    required Function(String) onTemperatureRatingChanged,
    required Function(String) onManufacturerChanged,
    required Function(String) onStateChanged,
    required VoidCallback onSubmit,
  }) {
    return PipelineForm(
      formKey: formKey,
      nameController: nameController,
      wallThicknessController: wallThicknessController,
      standardController: standardController,
      descriptionController: descriptionController,
      priceController: priceController,
      selectedPipeType: selectedPipeType,
      selectedPipeDiameter: selectedPipeDiameter,
      selectedMaterial: selectedMaterial,
      selectedPressureRating: selectedPressureRating,
      selectedTemperatureRating: selectedTemperatureRating,
      selectedManufacturer: selectedManufacturer,
      selectedState: selectedState,
      onPipeTypeChanged: onPipeTypeChanged,
      onPipeDiameterChanged: onPipeDiameterChanged,
      onMaterialChanged: onMaterialChanged,
      onPressureRatingChanged: onPressureRatingChanged,
      onTemperatureRatingChanged: onTemperatureRatingChanged,
      onManufacturerChanged: onManufacturerChanged,
      onStateChanged: onStateChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Add Pipeline',
    );
  }

  /// Factory constructor for creating a form for editing an existing pipeline
  factory PipelineForm.edit({
    required GlobalKey<FormState> formKey,
    required Pipeline pipeline,
    required TextEditingController nameController,
    required TextEditingController wallThicknessController,
    required TextEditingController standardController,
    required TextEditingController descriptionController,
    required TextEditingController priceController,
    required String selectedPipeType,
    required String selectedPipeDiameter,
    required String selectedMaterial,
    required String selectedPressureRating,
    required String selectedTemperatureRating,
    required String selectedManufacturer,
    required String selectedState,
    required Function(String) onPipeTypeChanged,
    required Function(String) onPipeDiameterChanged,
    required Function(String) onMaterialChanged,
    required Function(String) onPressureRatingChanged,
    required Function(String) onTemperatureRatingChanged,
    required Function(String) onManufacturerChanged,
    required Function(String) onStateChanged,
    required VoidCallback onSubmit,
  }) {
    return PipelineForm(
      formKey: formKey,
      nameController: nameController,
      wallThicknessController: wallThicknessController,
      standardController: standardController,
      descriptionController: descriptionController,
      priceController: priceController,
      selectedPipeType: selectedPipeType,
      selectedPipeDiameter: selectedPipeDiameter,
      selectedMaterial: selectedMaterial,
      selectedPressureRating: selectedPressureRating,
      selectedTemperatureRating: selectedTemperatureRating,
      selectedManufacturer: selectedManufacturer,
      selectedState: selectedState,
      onPipeTypeChanged: onPipeTypeChanged,
      onPipeDiameterChanged: onPipeDiameterChanged,
      onMaterialChanged: onMaterialChanged,
      onPressureRatingChanged: onPressureRatingChanged,
      onTemperatureRatingChanged: onTemperatureRatingChanged,
      onManufacturerChanged: onManufacturerChanged,
      onStateChanged: onStateChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Save Changes',
    );
  }
}
