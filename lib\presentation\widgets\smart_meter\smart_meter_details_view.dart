import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../core/widgets/detail_item.dart';
import '../../../core/widgets/status_badge.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../domain/entities/smart_meter.dart';
import '../../../utils/nepali_names.dart';

/// A widget for displaying smart meter details
class SmartMeterDetailsView extends ConsumerWidget {
  final SmartMeter smartMeter;
  final VoidCallback? onEdit;

  const SmartMeterDetailsView({
    super.key,
    required this.smartMeter,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final specs = smartMeter.specifications;
    
    // Get a Nepali name for the smart meter
    final displayName = NepaliFriendlyNames.getNameByType('smartMeter', smartMeter.title);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomCard(
          child: ListTile(
            leading: const Icon(Icons.speed, size: 40),
            title: Text(
              displayName,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            subtitle: Text('${specs['type'] ?? 'Standard'} | ${specs['size'] ?? 'Standard'}'),
            trailing: StatusBadge.smartMeter(smartMeter.status),
            onTap: onEdit,
          ),
        ),
        const SizedBox(height: 16),
        DetailItem(
          label: 'Reading',
          value: specs['reading'] ?? '0',
        ),
        DetailItem(
          label: 'Price Per Unit',
          value: smartMeter.pricePerUnit != null
              ? '${smartMeter.pricePerUnit!.toStringAsFixed(2)} NPR'
              : 'Not set',
        ),
        DetailItem(
          label: 'Location',
          value: 'Lat: ${smartMeter.latitude.toStringAsFixed(6)}, Lng: ${smartMeter.longitude.toStringAsFixed(6)}',
        ),
        DetailItem(
          label: 'ID',
          value: smartMeter.id,
        ),
        DetailItem(
          label: 'Created At',
          value: _formatTimestamp(smartMeter.createdAt.millisecondsSinceEpoch),
        ),
        DetailItem(
          label: 'Updated At',
          value: _formatTimestamp(smartMeter.updatedAt.millisecondsSinceEpoch),
        ),
        if (smartMeter.lastReading != null)
          DetailItem(
            label: 'Last Reading Date',
            value: _formatTimestamp(smartMeter.lastReading!.millisecondsSinceEpoch),
          ),
      ],
    );
  }

  String _formatTimestamp(int timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'Invalid date';
    }
  }
}
