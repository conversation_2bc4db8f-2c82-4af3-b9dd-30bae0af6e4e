import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../domain/entities/smart_meter.dart';

/// A reusable form widget for adding or editing a smart meter
class SmartMeterForm extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController readingController;
  final TextEditingController priceController;
  final String selectedMeterType;
  final String selectedMeterSize;
  final String selectedStatus;
  final Function(String) onMeterTypeChanged;
  final Function(String) onMeterSizeChanged;
  final Function(String) onStatusChanged;
  final VoidCallback onSubmit;
  final String submitButtonText;

  const SmartMeterForm({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.readingController,
    required this.priceController,
    required this.selectedMeterType,
    required this.selectedMeterSize,
    required this.selectedStatus,
    required this.onMeterTypeChanged,
    required this.onMeterSizeChanged,
    required this.onStatusChanged,
    required this.onSubmit,
    this.submitButtonText = 'Save',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          CustomTextField(
            controller: nameController,
            label: 'Smart Meter Name',
            hint: 'Enter smart meter name',
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedMeterType,
            options: const [
              'Digital',
              'Analog',
              'Smart',
              'Ultrasonic',
              'Electromagnetic',
              'Other',
            ],
            label: 'Meter Type',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onMeterTypeChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedMeterSize,
            options: const [
              '15mm',
              '20mm',
              '25mm',
              '32mm',
              '40mm',
              '50mm',
              '65mm',
              '80mm',
              '100mm',
              '150mm',
              '200mm',
            ],
            label: 'Meter Size',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onMeterSizeChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField.numeric(
            controller: readingController,
            label: 'Initial Reading',
            hint: 'e.g., 0',
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomTextField.price(
            controller: priceController,
            label: 'Price Per Unit',
            hint: 'e.g., 10.50',
            currency: 'NPR',
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedStatus,
            options: const [
              'Active',
              'Inactive',
              'Maintenance',
            ],
            label: 'Status',
            onChanged: (value) {
              if (value != null) {
                onStatusChanged(value);
              }
            },
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: onSubmit,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
            ),
            child: Text(submitButtonText),
          ),
        ],
      ),
    );
  }

  /// Factory constructor for creating a form for adding a new smart meter
  factory SmartMeterForm.add({
    required GlobalKey<FormState> formKey,
    required TextEditingController nameController,
    required TextEditingController readingController,
    required TextEditingController priceController,
    required String selectedMeterType,
    required String selectedMeterSize,
    required String selectedStatus,
    required Function(String) onMeterTypeChanged,
    required Function(String) onMeterSizeChanged,
    required Function(String) onStatusChanged,
    required VoidCallback onSubmit,
  }) {
    return SmartMeterForm(
      formKey: formKey,
      nameController: nameController,
      readingController: readingController,
      priceController: priceController,
      selectedMeterType: selectedMeterType,
      selectedMeterSize: selectedMeterSize,
      selectedStatus: selectedStatus,
      onMeterTypeChanged: onMeterTypeChanged,
      onMeterSizeChanged: onMeterSizeChanged,
      onStatusChanged: onStatusChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Add Smart Meter',
    );
  }

  /// Factory constructor for creating a form for editing an existing smart meter
  factory SmartMeterForm.edit({
    required GlobalKey<FormState> formKey,
    required SmartMeter smartMeter,
    required TextEditingController nameController,
    required TextEditingController readingController,
    required TextEditingController priceController,
    required String selectedMeterType,
    required String selectedMeterSize,
    required String selectedStatus,
    required Function(String) onMeterTypeChanged,
    required Function(String) onMeterSizeChanged,
    required Function(String) onStatusChanged,
    required VoidCallback onSubmit,
  }) {
    return SmartMeterForm(
      formKey: formKey,
      nameController: nameController,
      readingController: readingController,
      priceController: priceController,
      selectedMeterType: selectedMeterType,
      selectedMeterSize: selectedMeterSize,
      selectedStatus: selectedStatus,
      onMeterTypeChanged: onMeterTypeChanged,
      onMeterSizeChanged: onMeterSizeChanged,
      onStatusChanged: onStatusChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Save Changes',
    );
  }
}
