import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../domain/entities/valve.dart';

/// A reusable form widget for adding or editing a valve
class ValveForm extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController priceController;
  final String selectedValveType;
  final String selectedValveSize;
  final String selectedStatus;
  final String selectedControlType;
  final Function(String) onValveTypeChanged;
  final Function(String) onValveSizeChanged;
  final Function(String) onStatusChanged;
  final Function(String) onControlTypeChanged;
  final VoidCallback onSubmit;
  final String submitButtonText;

  const ValveForm({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.priceController,
    required this.selectedValveType,
    required this.selectedValveSize,
    required this.selectedStatus,
    required this.selectedControlType,
    required this.onValveTypeChanged,
    required this.onValveSizeChanged,
    required this.onStatusChanged,
    required this.onControlTypeChanged,
    required this.onSubmit,
    this.submitButtonText = 'Save',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          CustomTextField(
            controller: nameController,
            label: 'Valve Name',
            hint: 'Enter valve name',
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedValveType,
            options: const [
              'Gate',
              'Ball',
              'Globe',
              'Butterfly',
              'Check',
              'Pressure Relief',
              'Control',
              'Needle',
            ],
            label: 'Valve Type',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onValveTypeChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedValveSize,
            options: const [
              '15mm',
              '20mm',
              '25mm',
              '32mm',
              '40mm',
              '50mm',
              '65mm',
              '80mm',
              '100mm',
              '150mm',
              '200mm',
            ],
            label: 'Valve Size',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onValveSizeChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomTextField.price(
            controller: priceController,
            label: 'Price Per Unit',
            hint: 'e.g., 500.00',
            currency: 'NPR',
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedControlType,
            options: const [
              'Manual',
              'Electric',
            ],
            label: 'Control Type',
            isRequired: true,
            onChanged: (value) {
              if (value != null) {
                onControlTypeChanged(value);
              }
            },
          ),
          const SizedBox(height: 16),
          CustomDropdown.fromStrings(
            value: selectedStatus,
            options: const [
              'Open',
              'Closed',
              'Maintenance',
            ],
            label: 'Status',
            onChanged: (value) {
              if (value != null) {
                onStatusChanged(value);
              }
            },
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: onSubmit,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
            ),
            child: Text(submitButtonText),
          ),
          const SizedBox(height: 16),
          const Text(
            'Note: This will add the valve to the database. You can later add it to the map from the map screen.',
            style: TextStyle(
              fontStyle: FontStyle.italic,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Factory constructor for creating a form for adding a new valve
  factory ValveForm.add({
    required GlobalKey<FormState> formKey,
    required TextEditingController nameController,
    required TextEditingController priceController,
    required String selectedValveType,
    required String selectedValveSize,
    required String selectedStatus,
    required String selectedControlType,
    required Function(String) onValveTypeChanged,
    required Function(String) onValveSizeChanged,
    required Function(String) onStatusChanged,
    required Function(String) onControlTypeChanged,
    required VoidCallback onSubmit,
  }) {
    return ValveForm(
      formKey: formKey,
      nameController: nameController,
      priceController: priceController,
      selectedValveType: selectedValveType,
      selectedValveSize: selectedValveSize,
      selectedStatus: selectedStatus,
      selectedControlType: selectedControlType,
      onValveTypeChanged: onValveTypeChanged,
      onValveSizeChanged: onValveSizeChanged,
      onStatusChanged: onStatusChanged,
      onControlTypeChanged: onControlTypeChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Add Valve',
    );
  }

  /// Factory constructor for creating a form for editing an existing valve
  factory ValveForm.edit({
    required GlobalKey<FormState> formKey,
    required Valve valve,
    required TextEditingController nameController,
    required TextEditingController priceController,
    required String selectedValveType,
    required String selectedValveSize,
    required String selectedStatus,
    required String selectedControlType,
    required Function(String) onValveTypeChanged,
    required Function(String) onValveSizeChanged,
    required Function(String) onStatusChanged,
    required Function(String) onControlTypeChanged,
    required VoidCallback onSubmit,
  }) {
    return ValveForm(
      formKey: formKey,
      nameController: nameController,
      priceController: priceController,
      selectedValveType: selectedValveType,
      selectedValveSize: selectedValveSize,
      selectedStatus: selectedStatus,
      selectedControlType: selectedControlType,
      onValveTypeChanged: onValveTypeChanged,
      onValveSizeChanged: onValveSizeChanged,
      onStatusChanged: onStatusChanged,
      onControlTypeChanged: onControlTypeChanged,
      onSubmit: onSubmit,
      submitButtonText: 'Save Changes',
    );
  }
}
