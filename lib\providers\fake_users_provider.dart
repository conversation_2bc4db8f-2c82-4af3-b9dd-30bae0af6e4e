import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/fake_user_model.dart';
import '../utils/fake_user_generator.dart';

// Provider for the fake users state
final fakeUsersProvider =
    StateNotifierProvider<FakeUsersNotifier, List<FakeUser>>((ref) {
  return FakeUsersNotifier();
});

class FakeUsersNotifier extends StateNotifier<List<FakeUser>> {
  FakeUsersNotifier() : super([]);

  // Generate 50 fake users
  void generateFakeUsers() {
    state = FakeUserGenerator.generateFakeUsers(50);
  }

  // Toggle selection of a user
  void toggleUserSelection(String userId) {
    state = state.map((user) {
      if (user.id == userId) {
        return user.copyWith(isSelected: !user.isSelected);
      }
      return user;
    }).toList();
  }

  // Select a user
  void selectUser(String userId) {
    state = state.map((user) {
      return user.copyWith(isSelected: user.id == userId);
    }).toList();
  }

  // Get the selected user
  FakeUser? getSelectedUser() {
    final selectedUsers = state.where((user) => user.isSelected).toList();
    return selectedUsers.isNotEmpty ? selectedUsers.first : null;
  }

  // Save fake users to Firestore
  Future<void> saveFakeUsersToFirestore() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      // Reference to the fake users collection
      final fakeUsersCollection = firestore.collection('fake_users');

      // Add each user to the batch
      for (final user in state) {
        final docRef = fakeUsersCollection.doc(user.id);
        batch.set(docRef, user.toMap());
      }

      // Commit the batch
      await batch.commit();

      debugPrint('Successfully saved ${state.length} fake users to Firestore');
    } catch (e) {
      debugPrint('Error saving fake users to Firestore: $e');
      rethrow;
    }
  }

  // Save all fake users as real users in the user collection
  Future<void> saveAllUsersToUserCollection(String areaId) async {
    try {
      final firestore = FirebaseFirestore.instance;

      // We need to use multiple batches if we have more than 500 users
      // since Firestore has a limit of 500 operations per batch
      const int batchSize = 450; // Using 450 to be safe
      final int totalBatches = (state.length / batchSize).ceil();

      debugPrint(
          'Saving ${state.length} users to user collection in $totalBatches batches');

      for (int i = 0; i < totalBatches; i++) {
        final batch = firestore.batch();

        // Calculate start and end indices for this batch
        final int start = i * batchSize;
        final int end = (i + 1) * batchSize < state.length
            ? (i + 1) * batchSize
            : state.length;

        // Get users for this batch
        final batchUsers = state.sublist(start, end);

        // Reference to the users collection
        final usersCollection = firestore.collection('user');
        final userDetailsCollection = firestore.collection('user_details');

        // Add each user to the batch
        for (final fakeUser in batchUsers) {
          // Create a unique ID for the user
          final userId = const Uuid().v4();

          // Create user document in the users collection
          final userDocRef = usersCollection.doc(userId);
          batch.set(userDocRef, {
            'id': userId,
            'type': 'user',
            'position': {
              'latitude':
                  0.0, // Default position, will be updated when placed on map
              'longitude': 0.0,
            },
            'title': fakeUser.name,
            'areaId': areaId,
            // No metadata as per requirement
          });

          // Create user details document
          final userDetailsDocRef = userDetailsCollection.doc(userId);
          batch.set(userDetailsDocRef, {
            'details': {
              'name': fakeUser.name,
              'contact': fakeUser.contact,
              'address': fakeUser.address,
              'notes': fakeUser.notes,
            },
            'connections': [],
            'createdAt': DateTime.now().millisecondsSinceEpoch,
            'lastUpdated': DateTime.now().millisecondsSinceEpoch,
          });
        }

        // Commit the batch
        await batch.commit();
        debugPrint(
            'Saved batch ${i + 1}/$totalBatches (${batchUsers.length} users)');
      }

      debugPrint(
          'Successfully saved all ${state.length} users to user collection');
    } catch (e) {
      debugPrint('Error saving users to user collection: $e');
      rethrow;
    }
  }

  // Load fake users from Firestore
  Future<void> loadFakeUsersFromFirestore() async {
    try {
      final firestore = FirebaseFirestore.instance;
      final fakeUsersCollection = firestore.collection('fake_users');

      final snapshot = await fakeUsersCollection.get();

      if (snapshot.docs.isNotEmpty) {
        state = snapshot.docs.map((doc) {
          return FakeUser.fromMap(doc.data());
        }).toList();

        debugPrint(
            'Successfully loaded ${state.length} fake users from Firestore');
      } else {
        // If no users exist in Firestore, generate new ones
        generateFakeUsers();
        debugPrint(
            'No fake users found in Firestore, generated ${state.length} new fake users');
      }
    } catch (e) {
      debugPrint('Error loading fake users from Firestore: $e');
      // If there's an error, generate new users
      generateFakeUsers();
    }
  }
}
