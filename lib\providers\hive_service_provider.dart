import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/interfaces/hive_service_interface.dart';
import '../services/implementations/hive_service_impl.dart';

/// Global instance of HiveService that gets initialized in main.dart
HiveServiceImpl? _hiveServiceInstance;

/// Set the global Hive service instance (called from main.dart)
void setHiveServiceInstance(HiveServiceImpl instance) {
  _hiveServiceInstance = instance;
}

/// Provider for the HiveService
final hiveServiceProvider = Provider<HiveServiceInterface>((ref) {
  if (_hiveServiceInstance == null) {
    throw StateError(
        'HiveService not initialized. The app should show an initialization screen first.');
  }
  return _hiveServiceInstance!;
});
