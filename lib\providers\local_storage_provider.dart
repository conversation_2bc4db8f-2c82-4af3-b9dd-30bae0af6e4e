import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/connectivity_service.dart';
import '../services/hive_service.dart';
import '../services/sync_service.dart';
import 'firestore_provider.dart';

// Provider for the connectivity service
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  final service = ConnectivityService();
  ref.onDispose(() {
    service.dispose();
  });
  return service;
});

// Provider for the sync service
final syncServiceProvider = Provider<SyncService>((ref) {
  final firestoreRepository = ref.watch(firestoreRepositoryProvider);
  final connectivityService = ref.watch(connectivityServiceProvider);

  final service = SyncService(
    firestoreRepository: firestoreRepository,
    connectivityService: connectivityService,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

// Provider for sync status
final syncStatusProvider = StreamProvider.autoDispose<SyncStatus>((ref) {
  final syncService = ref.watch(syncServiceProvider);

  // Return the stream with an initial value
  return syncService.syncStatus.asBroadcastStream();
});

// Provider for sync statistics
final syncStatisticsProvider = Provider<SyncStatistics>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  return syncService.getSyncStatistics();
});
