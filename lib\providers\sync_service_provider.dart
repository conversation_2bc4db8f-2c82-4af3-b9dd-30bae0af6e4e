import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/interfaces/sync_service_interface.dart';
import '../services/implementations/sync_service_impl.dart';
import 'firestore_provider.dart';
import '../services/connectivity_service.dart';
import 'hive_service_provider.dart';

// Provider for the SyncService
final syncServiceProvider = Provider<SyncServiceInterface>((ref) {
  final firestoreRepository = ref.watch(firestoreRepositoryProvider);
  final connectivityService = ConnectivityService();
  final hiveService = ref.watch(hiveServiceProvider);

  return SyncServiceImpl(
    firestoreRepository: firestoreRepository,
    connectivityService: connectivityService,
    hiveService: hiveService,
  );
});
