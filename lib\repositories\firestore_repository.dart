import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/firestore_models.dart';
import '../models/pipeline.dart';
import '../models/user_model.dart';
import '../models/hive_models/hive_user_model.dart';
import '../models/hive_models/hive_valve_model.dart';
import '../models/hive_models/hive_connector_model.dart';
import '../models/hive_models/hive_pipeline_model.dart';

class FirestoreRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection references
  CollectionReference get _areasCollection => _firestore.collection('areas');
  CollectionReference get _elementsCollection =>
      _firestore.collection('elements');
  CollectionReference get _pipelinesCollection =>
      _firestore.collection('pipelines');
  CollectionReference get _usersCollection => _firestore.collection('user');
  CollectionReference get _valvesCollection => _firestore.collection('valves');
  CollectionReference get _connectorsCollection =>
      _firestore.collection('connectors');
  CollectionReference get _connectorListingCollection =>
      _firestore.collection('connector_listing');
  CollectionReference get _pipelineListingCollection =>
      _firestore.collection('pipeline_listing');
  CollectionReference get _categoriesCollection =>
      _firestore.collection('categories');

  // Cache for areas
  List<FirestoreArea> _cachedAreas = [];
  DateTime? _areasCacheTimestamp;

  // Initialize the database with default area if needed
  Future<void> initializeDatabase() async {
    // Check if areas collection is empty
    final areasSnapshot = await _areasCollection.limit(1).get();
    if (areasSnapshot.docs.isEmpty) {
      // Create a default area covering a large region
      await createArea(
        'Default Area',
        LatLngBounds(
          southwest: const LatLng(-90, -180),
          northeast: const LatLng(90, 180),
        ),
      );
    }
  }

  // Create a new geographic area
  Future<String> createArea(String name, LatLngBounds bounds) async {
    final areaRef = _areasCollection.doc();

    await areaRef.set({
      'name': name,
      'bounds': {
        'northeast':
            GeoPoint(bounds.northeast.latitude, bounds.northeast.longitude),
        'southwest':
            GeoPoint(bounds.southwest.latitude, bounds.southwest.longitude),
      },
      'summary': {
        'pipelineCount': 0,
        'userCount': 0,
        'valveCount': 0,
        'connectorCount': 0,
      }
    });

    // Invalidate cache
    _cachedAreas = [];
    _areasCacheTimestamp = null;

    return areaRef.id;
  }

  // Get all areas
  Future<List<FirestoreArea>> getAreas() async {
    // Check if cache is valid (less than 5 minutes old)
    final now = DateTime.now();
    if (_cachedAreas.isNotEmpty &&
        _areasCacheTimestamp != null &&
        now.difference(_areasCacheTimestamp!).inMinutes < 5) {
      return _cachedAreas;
    }

    final areasSnapshot = await _areasCollection.get();
    _cachedAreas = areasSnapshot.docs
        .map((doc) => FirestoreArea.fromFirestore(doc))
        .toList();
    _areasCacheTimestamp = now;

    return _cachedAreas;
  }

  // Find area for a position
  Future<String> getAreaForPosition(LatLng position) async {
    final areas = await getAreas();

    // Find the area that contains this position
    for (final area in areas) {
      if (area.containsPoint(position)) {
        return area.id;
      }
    }

    // If no area contains this position, use the first area
    if (areas.isNotEmpty) {
      return areas.first.id;
    }

    // If no areas exist, create a default area
    return await createArea(
      'Default Area',
      LatLngBounds(
        southwest: const LatLng(-90, -180),
        northeast: const LatLng(90, 180),
      ),
    );
  }

  // Find areas that intersect with bounds
  Future<List<String>> getAreasInBounds(LatLngBounds bounds) async {
    final areas = await getAreas();

    return areas
        .where((area) => area.intersectsBounds(bounds))
        .map((area) => area.id)
        .toList();
  }

  // Update area summary counts
  Future<void> updateAreaSummary(
      String areaId, String elementType, int change) async {
    final String countField;
    switch (elementType) {
      case 'pipeline':
        countField = 'summary.pipelineCount';
        break;
      case 'user':
        countField = 'summary.userCount';
        break;
      case 'valve':
        countField = 'summary.valveCount';
        break;
      case 'connector':
        countField = 'summary.connectorCount';
        break;
      default:
        return;
    }

    await _areasCollection.doc(areaId).update({
      countField: FieldValue.increment(change),
    });

    // Invalidate cache
    _cachedAreas = [];
    _areasCacheTimestamp = null;
  }

  // Add a new element with minimal data
  Future<String> addElement(String type, LatLng position, String areaId,
      String title, Map<String, dynamic> metadata) async {
    final elementRef = _elementsCollection.doc();
    final now = DateTime.now().millisecondsSinceEpoch;

    // Store only minimal data in elements collection
    await elementRef.set({
      'type': type,
      'position': GeoPoint(position.latitude, position.longitude),
      'areaId': areaId,
      'title': title,
      'updated_at': now,
      // Only keep essential metadata in the elements collection
      'metadata': {
        'createdAt': now,
        'lastUpdated': now,
        // Keep only essential type-specific metadata
        if (metadata.containsKey('connectorType'))
          'connectorType': metadata['connectorType'],
        if (metadata.containsKey('segmentId'))
          'segmentId': metadata['segmentId'],
      }
    });

    // Update area summary
    await updateAreaSummary(areaId, type, 1);

    // Also add to the respective detailed collection
    // This ensures that when elements are added to the elements collection,
    // they are also added to their respective detailed collections
    final id = elementRef.id;
    switch (type) {
      case 'user':
        await addUserDetails(id, metadata['details'] ?? {'name': title});
        break;
      case 'valve':
        break;
      case 'connector':
        final connectorType = metadata['connectorType'] ?? 0; // Default to tee
        await addConnectorDetails(id, connectorType,
            metadata['specifications'] ?? {'material': 'Standard'});
        break;
      // Pipeline is handled separately with addPipelineElement
    }

    return id;
  }

  // Add a new element with a specific ID (for syncing from local storage)
  Future<void> addElementWithId(String id, String type, LatLng position,
      String areaId, String title, Map<String, dynamic> metadata) async {
    final elementRef = _elementsCollection.doc(id);
    final now = DateTime.now().millisecondsSinceEpoch;

    // Create the document data
    final Map<String, dynamic> docData = {
      'type': type,
      'position': GeoPoint(position.latitude, position.longitude),
      'areaId': areaId,
      'title': title,
      'updated_at': now,
      'metadata': {
        // Only essential timestamps
        'createdAt': metadata['createdAt'] ?? now,
        'lastUpdated': metadata['lastUpdated'] ?? now,
        // Keep only essential type-specific metadata
        if (metadata.containsKey('connectorType'))
          'connectorType': metadata['connectorType'],
        if (metadata.containsKey('segmentId'))
          'segmentId': metadata['segmentId'],
      }
    };

    // For user elements, add user-specific fields directly to the document
    if (type == 'user') {
      // Add userId field
      if (metadata.containsKey('userId')) {
        docData['userId'] = metadata['userId'];
      } else {
        // If userId is not provided, use the element ID
        docData['userId'] = id;
      }

      // Add minimal user details for quick access
      if (metadata.containsKey('name')) {
        docData['name'] = metadata['name'];
      }
      if (metadata.containsKey('contact')) {
        docData['contact'] = metadata['contact'];
      }
      if (metadata.containsKey('address')) {
        docData['address'] = metadata['address'];
      }
    }

    // Store the data in elements collection
    await elementRef.set(docData);

    // Update area summary
    await updateAreaSummary(areaId, type, 1);

    // Also add to the respective detailed collection if not already done
    // This ensures that when elements are added to the elements collection,
    // they are also added to their respective detailed collections
    switch (type) {
      case 'user':
        // Check if user details already exist
        final userDoc = await _usersCollection.doc(id).get();
        if (!userDoc.exists) {
          // Add minimal details if not provided
          await addUserDetails(id, metadata['details'] ?? {'name': title});
        }
        break;
      case 'valve':
        // Check if valve details already exist
        final valveDoc = await _valvesCollection.doc(id).get();
        if (!valveDoc.exists) {
          // Add minimal specifications if not provided
          await addValveDetails(
            valveId: id,
            status: 'Operational',
            type: 'Standard',
            lastMaintainance: 0,
            controlType: 'Manual',
            size: 25.0,
            isDeleted: false,
            isSync: true,
          );
        }
        break;
      case 'connector':
        // Check if connector details already exist
        final connectorDoc = await _connectorsCollection.doc(id).get();
        if (!connectorDoc.exists) {
          // Add minimal specifications if not provided
          final connectorType =
              metadata['connectorType'] ?? 0; // Default to tee
          await addConnectorDetails(id, connectorType,
              metadata['specifications'] ?? {'material': 'Standard'});
        }
        break;
      // Pipeline is handled separately with addPipelineElementWithId
    }
  }

  // Add a pipeline element with all points
  Future<String> addPipelineElement(
      List<LatLng> points, String areaId, String title,
      {String state = 'Active'}) async {
    if (points.isEmpty) {
      throw ArgumentError('Pipeline must have at least one point');
    }

    final elementRef = _elementsCollection.doc();
    final now = DateTime.now().millisecondsSinceEpoch;

    // Calculate center point for the pipeline
    final centerLat =
        points.map((p) => p.latitude).reduce((a, b) => a + b) / points.length;
    final centerLng =
        points.map((p) => p.longitude).reduce((a, b) => a + b) / points.length;

    // Generate a segment ID for the first segment
    final segmentId =
        points.length >= 2 ? _generateSegmentId(points[0], points[1]) : null;

    // Convert all points to the format needed for Firestore
    final List<Map<String, double>> fullPath = points
        .map((point) => {'lat': point.latitude, 'lng': point.longitude})
        .toList();

    debugPrint(
        'Adding pipeline element with ${fullPath.length} points to elements collection');

    // Store full path data in elements collection
    await elementRef.set({
      'type': 'pipeline',
      'position': GeoPoint(centerLat, centerLng),
      'areaId': areaId,
      'title': title,
      'state': state, // Add state to elements collection
      'updated_at': now,
      // Store all points in the path
      'path': fullPath,
      // No metadata as per requirement
    });

    // Update area summary
    await updateAreaSummary(areaId, 'pipeline', 1);

    // Also add to the pipelines collection with full details
    await addPipelineDetails(elementRef.id, points, {
      'segmentId': segmentId,
      'material': 'Standard',
      'pipeType': 'Standard',
      'diameter': 100,
      'pressure': 'Medium',
      'state': state,
    });

    return elementRef.id;
  }

  // Add a pipeline element with a specific ID (for syncing from local storage)
  Future<void> addPipelineElementWithId(
      String id, List<LatLng> points, String areaId, String title,
      {Map<String, dynamic>? properties, String? state}) async {
    if (points.isEmpty) {
      throw ArgumentError('Pipeline must have at least one point');
    }

    final elementRef = _elementsCollection.doc(id);
    final now = DateTime.now().millisecondsSinceEpoch;

    // Calculate center point for the pipeline
    final centerLat =
        points.map((p) => p.latitude).reduce((a, b) => a + b) / points.length;
    final centerLng =
        points.map((p) => p.longitude).reduce((a, b) => a + b) / points.length;

    // Generate a segment ID for the first segment
    final segmentId =
        points.length >= 2 ? _generateSegmentId(points[0], points[1]) : null;

    // Get state from explicit parameter, properties, or default to 'Active'
    final pipelineState = state ?? properties?['state'] ?? 'Active';
    debugPrint('🔄 Using pipeline state: $pipelineState');

    // Convert all points to the format needed for Firestore
    final List<Map<String, double>> fullPath = points
        .map((point) => {'lat': point.latitude, 'lng': point.longitude})
        .toList();

    debugPrint(
        'Adding pipeline element with ${fullPath.length} points to elements collection');

    // Store full path data in elements collection
    await elementRef.set({
      'type': 'pipeline',
      'position': GeoPoint(centerLat, centerLng),
      'areaId': areaId,
      'title': title,
      'state': pipelineState, // Add state to elements collection
      'updated_at': now,
      // Store all points in the path
      'path': fullPath,
      // No metadata as per requirement
    });

    // Update area summary
    await updateAreaSummary(areaId, 'pipeline', 1);

    // Also add to the pipelines collection if not already done
    // This ensures that when pipeline elements are added to the elements collection,
    // they are also added to the pipelines collection with full path data
    final pipelineDoc = await _pipelinesCollection.doc(id).get();
    if (!pipelineDoc.exists) {
      // Ensure properties has all required fields
      final enhancedProperties = {
        'segmentId': segmentId,
        'material': 'Standard',
        'pipeType': 'Standard',
        'diameter': 100,
        'pressure': 'Medium',
        'state': pipelineState,
        ...?properties, // Override defaults with provided properties
      };

      // Add detailed pipeline data to pipelines collection
      await addPipelineDetails(id, points, enhancedProperties);
    }
  }

  // Add detailed pipeline data
  Future<void> addPipelineDetails(String pipelineId, List<LatLng> points,
      Map<String, dynamic> properties) async {
    if (points.length < 2) {
      throw ArgumentError('Pipeline must have at least two points');
    }

    // Get state from properties or default to 'Active'
    final state = properties['state'] ?? 'Active';

    // Calculate path length
    final length = _calculatePathLength(points);

    // Generate a segment ID for the first segment if not provided
    final segmentId = properties['segmentId'] ??
        (points.length >= 2 ? _generateSegmentId(points[0], points[1]) : null);

    final now = DateTime.now().millisecondsSinceEpoch;

    // Create segments with IDs and states
    final List<Map<String, dynamic>> segments = [];
    final List<Map<String, dynamic>> path = [];

    // Process each segment
    for (int i = 0; i < points.length - 1; i++) {
      final start = points[i];
      final end = points[i + 1];

      // Generate a unique segment ID
      final currentSegmentId = _generateSegmentId(start, end);

      // Calculate segment length
      final segmentLength = _calculateDistance(start, end);

      // Add segment info with start and end points
      segments.add({
        'id': currentSegmentId,
        'startPoint': {'lat': start.latitude, 'lng': start.longitude},
        'endPoint': {'lat': end.latitude, 'lng': end.longitude},
        'state': state, // Default to the pipeline's state
        'length': segmentLength,
      });

      // Add points to the path array with segment information
      // For the first segment, add the start point
      if (i == 0) {
        path.add({
          'lat': start.latitude,
          'lng': start.longitude,
          'segmentId': currentSegmentId,
          'state': state,
          'isJunction': false,
          'isStart': true,
          'isEnd': false,
        });
      }

      // Add the end point with segment info
      path.add({
        'lat': end.latitude,
        'lng': end.longitude,
        'segmentId': currentSegmentId,
        'state': state,
        'isJunction':
            i < points.length - 2, // It's a junction if it's not the last point
        'isStart': false,
        'isEnd':
            i == points.length - 2, // It's the end if it's the last segment
      });
    }

    await _pipelinesCollection.doc(pipelineId).set({
      'startPoint': GeoPoint(points.first.latitude, points.first.longitude),
      'endPoint': GeoPoint(points.last.latitude, points.last.longitude),
      // Store the path data with segment IDs and states
      'path': path, // Renamed from enhancedFullPath to path for clarity
      // Store segments separately for easier access
      'segments': segments,
      'length': length,
      'properties': properties,
      'connections': [],
      'state': state, // Add state at the top level
      'metadata': {
        'length': length,
        'pointCount': points.length,
        'segmentCount': segments.length,
        'segmentId': segmentId, // Keep for backward compatibility
        'createdAt': now,
        'lastUpdated': now,
      },
      'createdAt': FieldValue.serverTimestamp(),
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Add detailed user data
  Future<void> addUserDetails(
      String userId, Map<String, dynamic> details) async {
    await _usersCollection.doc(userId).set({
      'details': details,
      'connections': [],
      'createdAt': FieldValue.serverTimestamp(),
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Add detailed valve data
  Future<void> addValveDetails({
    required String valveId,
    required String status,
    required String type,
    required int lastMaintainance,
    required String controlType,
    required double size,
    bool isDeleted = false,
    bool isSync = true,
    double? pricePerUnit,
  }) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    await _valvesCollection.doc(valveId).set({
      'type': type,
      'controlType': controlType,
      'size': size,
      'status': status,
      'lastMaintenance': lastMaintainance,
      'pricePerUnit': pricePerUnit,
      'isDeleted': isDeleted,
      'isSync': isSync,
      'createdAt': now,
      'lastUpdated': now,
    });
  }

  // Add detailed connector data
  Future<void> addConnectorDetails(String connectorId, int connectorType,
      Map<String, dynamic> specifications) async {
    await _connectorsCollection.doc(connectorId).set({
      'type': connectorType,
      'specifications': specifications,
      'connections': [],
      'createdAt': FieldValue.serverTimestamp(),
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Add detailed smart meter data
  Future<void> addSmartMeterDetails(
      String smartMeterId, Map<String, dynamic> specifications) async {
    await _firestore.collection('smart_meters').doc(smartMeterId).set({
      'specifications': specifications,
      'reading': specifications['reading'] ?? '0',
      'type': specifications['type'] ?? 'Standard',
      'createdAt': FieldValue.serverTimestamp(),
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Get elements in the current map view
  Future<List<FirestoreElement>> getElementsInView(LatLngBounds bounds) async {
    // Find areas that intersect with the current view
    final areaIds = await getAreasInBounds(bounds);

    if (areaIds.isEmpty) {
      return [];
    }

    // Get elements in these areas
    final elementsQuery =
        await _elementsCollection.where('areaId', whereIn: areaIds).get();

    return elementsQuery.docs.map((doc) => _createElementFromDoc(doc)).toList();
  }

  // Get elements by type in the current map view
  Future<List<FirestoreElement>> getElementsByTypeInView(
      String type, LatLngBounds bounds,
      {int? afterTimestamp}) async {
    // Find areas that intersect with the current view
    final areaIds = await getAreasInBounds(bounds);

    if (areaIds.isEmpty) {
      return [];
    }

    // Start building the query
    Query query = _elementsCollection
        .where('areaId', whereIn: areaIds)
        .where('type', isEqualTo: type);

    // If afterTimestamp is provided, only get elements updated after that time
    if (afterTimestamp != null) {
      query = query.where('updated_at', isGreaterThan: afterTimestamp);
      debugPrint(
          'Fetching $type elements updated after timestamp: $afterTimestamp');
    }

    final elementsQuery = await query.get();
    debugPrint('Found ${elementsQuery.docs.length} $type elements');

    return elementsQuery.docs.map((doc) => _createElementFromDoc(doc)).toList();
  }

  // Get pipelines in the current map view
  Future<List<FirestorePipeline>> getPipelinesInView(LatLngBounds bounds,
      {int? afterTimestamp}) async {
    // Find areas that intersect with the current view
    final areaIds = await getAreasInBounds(bounds);

    if (areaIds.isEmpty) {
      debugPrint('No areas found in bounds');
      return [];
    }

    debugPrint('Found ${areaIds.length} areas in bounds');

    // Start building the query
    Query query = _elementsCollection
        .where('areaId', whereIn: areaIds)
        .where('type', isEqualTo: 'pipeline');

    // If afterTimestamp is provided, only get elements updated after that time
    if (afterTimestamp != null) {
      query = query.where('updated_at', isGreaterThan: afterTimestamp);
      debugPrint('Fetching pipelines updated after timestamp: $afterTimestamp');
    }

    final pipelinesQuery = await query.get();
    debugPrint('Found ${pipelinesQuery.docs.length} pipeline documents');

    final pipelines = pipelinesQuery.docs.map((doc) {
      try {
        debugPrint('Processing pipeline document ${doc.id}');
        final data = doc.data() as Map<String, dynamic>;
        if (data.containsKey('path')) {
          debugPrint('Path length: ${(data['path'] as List).length}');
        } else {
          debugPrint('No path data found in pipeline document');
        }
        return FirestorePipeline.fromFirestore(doc);
      } catch (e) {
        debugPrint('Error processing pipeline document: $e');
        rethrow;
      }
    }).toList();

    debugPrint('Returning ${pipelines.length} pipeline objects');
    return pipelines;
  }

  // Get detailed information for a specific element
  Future<dynamic> getElementDetails(String elementId, String type) async {
    DocumentSnapshot detailsDoc;

    try {
      switch (type) {
        case 'pipeline':
          detailsDoc = await _pipelinesCollection.doc(elementId).get();
          if (detailsDoc.exists) {
            return FirestorePipelineDetails.fromFirestore(detailsDoc);
          }
          break;
        case 'user':
          detailsDoc = await _usersCollection.doc(elementId).get();
          if (detailsDoc.exists) {
            return FirestoreUserDetails.fromFirestore(detailsDoc);
          } else {
            // If user details don't exist, create default details
            debugPrint(
                'User details not found for $elementId, creating default details');
            final defaultDetails = {'name': 'User $elementId'};
            await addUserDetails(elementId, defaultDetails);
            // Return default details
            return FirestoreUserDetails(
              id: elementId,
              details: defaultDetails,
              connections: [],
            );
          }
        case 'valve':
          detailsDoc = await _valvesCollection.doc(elementId).get();
          if (detailsDoc.exists) {
            return FirestoreValveDetails.fromFirestore(detailsDoc);
          }
          break;
        case 'connector':
          detailsDoc = await _connectorsCollection.doc(elementId).get();
          if (detailsDoc.exists) {
            return FirestoreConnectorDetails.fromFirestore(detailsDoc);
          }
          break;
      }
    } catch (e) {
      debugPrint('Error getting details for $type with ID $elementId: $e');
      // Return default details based on type
      switch (type) {
        case 'user':
          return FirestoreUserDetails(
            id: elementId,
            details: {'name': 'User $elementId'},
            connections: [],
          );
        case 'valve':
          return FirestoreValveDetails(
            id: elementId,
            specifications: {'type': 'Standard', 'status': 'Operational'},
            status: 'Operational',
            lastMaintenance: null,
          );
        case 'connector':
          return FirestoreConnectorDetails(
            id: elementId,
            type: 0, // Default to tee
            specifications: {'material': 'Standard'},
            connections: [],
          );
        case 'pipeline':
          return FirestorePipelineDetails(
            id: elementId,
            startPoint: const GeoPoint(0, 0),
            endPoint: const GeoPoint(0.001, 0.001),
            properties: {'state': 'Active'},
            connections: [],
          );
      }
    }

    // If we get here, return default details based on type
    debugPrint(
        'Detailed information not found for $type with ID $elementId, returning default');
    switch (type) {
      case 'user':
        return FirestoreUserDetails(
          id: elementId,
          details: {'name': 'User $elementId'},
          connections: [],
        );
      case 'valve':
        return FirestoreValveDetails(
          id: elementId,
          specifications: {'type': 'Standard', 'status': 'Operational'},
          status: 'Operational',
          lastMaintenance: null,
        );
      case 'connector':
        return FirestoreConnectorDetails(
          id: elementId,
          type: 0, // Default to tee
          specifications: {'material': 'Standard'},
          connections: [],
        );
      case 'pipeline':
        return FirestorePipelineDetails(
          id: elementId,
          startPoint: const GeoPoint(0, 0),
          endPoint: const GeoPoint(0.001, 0.001),
          properties: {'state': 'Active'},
          connections: [],
        );
      default:
        throw Exception(
            'Detailed information not found for $type with ID $elementId');
    }
  }

  // Update a pipeline element
  Future<void> updatePipelineElement(String pipelineId, List<LatLng> newPoints,
      {String? state}) async {
    if (newPoints.isEmpty) {
      throw ArgumentError('Pipeline must have at least one point');
    }

    // Get the element to find its area and current state if not provided
    final elementDoc = await _elementsCollection.doc(pipelineId).get();
    if (!elementDoc.exists) {
      throw Exception('Pipeline element not found');
    }

    final elementData = elementDoc.data() as Map<String, dynamic>;
    String pipelineState = state ?? elementData['state'] ?? 'Active';

    // Calculate center point for the pipeline
    final centerLat = newPoints.map((p) => p.latitude).reduce((a, b) => a + b) /
        newPoints.length;
    final centerLng =
        newPoints.map((p) => p.longitude).reduce((a, b) => a + b) /
            newPoints.length;

    // Generate a segment ID for the first segment
    final segmentId = newPoints.length >= 2
        ? _generateSegmentId(newPoints[0], newPoints[1])
        : null;

    // Convert all points to the format needed for Firestore
    final List<Map<String, double>> fullPath = newPoints
        .map((point) => {'lat': point.latitude, 'lng': point.longitude})
        .toList();

    debugPrint(
        'Updating pipeline element with ${fullPath.length} points in elements collection');

    // Get current timestamp in milliseconds
    final now = DateTime.now().millisecondsSinceEpoch;

    // Store full path data in elements collection
    await _elementsCollection.doc(pipelineId).update({
      'position': GeoPoint(centerLat, centerLng),
      // Store all points in the path
      'path': fullPath,
      'state': pipelineState, // Add state to elements collection
      'updated_at':
          now, // Use milliseconds timestamp instead of server timestamp
      // No metadata as per requirement
    });

    // Calculate path length
    final length = _calculatePathLength(newPoints);

    // Create segments with IDs and states
    final List<Map<String, dynamic>> segments = [];
    final List<Map<String, dynamic>> path = [];

    // Process each segment
    for (int i = 0; i < newPoints.length - 1; i++) {
      final start = newPoints[i];
      final end = newPoints[i + 1];

      // Generate a unique segment ID
      final currentSegmentId = _generateSegmentId(start, end);

      // Calculate segment length
      final segmentLength = _calculateDistance(start, end);

      // Add segment info with start and end points
      segments.add({
        'id': currentSegmentId,
        'startPoint': {'lat': start.latitude, 'lng': start.longitude},
        'endPoint': {'lat': end.latitude, 'lng': end.longitude},
        'state': pipelineState, // Default to the pipeline's state
        'length': segmentLength,
      });

      // Add points to the path array with segment information
      // For the first segment, add the start point
      if (i == 0) {
        path.add({
          'lat': start.latitude,
          'lng': start.longitude,
          'segmentId': currentSegmentId,
          'state': pipelineState,
          'isJunction': false,
          'isStart': true,
          'isEnd': false,
        });
      }

      // Add the end point with segment info
      path.add({
        'lat': end.latitude,
        'lng': end.longitude,
        'segmentId': currentSegmentId,
        'state': pipelineState,
        'isJunction': i <
            newPoints.length - 2, // It's a junction if it's not the last point
        'isStart': false,
        'isEnd':
            i == newPoints.length - 2, // It's the end if it's the last segment
      });
    }

    // Update the path in the pipelines collection with metadata
    await _pipelinesCollection.doc(pipelineId).update({
      'startPoint':
          GeoPoint(newPoints.first.latitude, newPoints.first.longitude),
      'endPoint': GeoPoint(newPoints.last.latitude, newPoints.last.longitude),
      'path': path, // Renamed from enhancedFullPath to path for clarity
      'segments': segments,
      'length': length,
      'state': pipelineState, // Add state at the top level
      'metadata': {
        'length': length,
        'pointCount': newPoints.length,
        'segmentCount': segments.length,
        'segmentId': segmentId, // Keep for backward compatibility
        'lastUpdated':
            now, // Use milliseconds timestamp instead of server timestamp
      },
      'lastUpdated':
          now, // Use milliseconds timestamp instead of server timestamp
    });
  }

  // Update pipeline details
  Future<void> updatePipelineDetails(
      String pipelineId, Map<String, dynamic> properties,
      {Map<String, String>? segmentStates}) async {
    // Get state from properties or default to 'Active'
    final state = properties['state'] ?? 'Active';

    // Get current timestamp in milliseconds
    final now = DateTime.now().millisecondsSinceEpoch;

    // Update both the pipeline collection and elements collection
    final updateData = {
      'properties': properties,
      'state': state, // Add state at the top level
      'lastUpdated':
          now, // Use milliseconds timestamp instead of server timestamp
    };

    // If segment states are provided, update them
    if (segmentStates != null && segmentStates.isNotEmpty) {
      // First get the current pipeline data to get segments
      final pipelineDoc = await _pipelinesCollection.doc(pipelineId).get();
      if (pipelineDoc.exists) {
        final data = pipelineDoc.data() as Map<String, dynamic>;

        // Update segments if they exist
        if (data.containsKey('segments')) {
          final List<dynamic> segments = data['segments'] as List<dynamic>;
          final List<Map<String, dynamic>> updatedSegments = [];

          for (final segment in segments) {
            final Map<String, dynamic> segmentMap =
                Map<String, dynamic>.from(segment as Map<String, dynamic>);
            final String segmentId = segmentMap['id'] as String;

            // Update segment state if provided
            if (segmentStates.containsKey(segmentId)) {
              segmentMap['state'] = segmentStates[segmentId];
            }

            updatedSegments.add(segmentMap);
          }

          updateData['segments'] = updatedSegments;
        }

        // Update path if it exists (previously fullPath)
        if (data.containsKey('path')) {
          final List<dynamic> pathPoints = data['path'] as List<dynamic>;
          final List<Map<String, dynamic>> updatedPath = [];

          for (final point in pathPoints) {
            final Map<String, dynamic> pointMap =
                Map<String, dynamic>.from(point as Map<String, dynamic>);
            final String? segmentId = pointMap['segmentId'] as String?;

            // Update point state if its segment state is provided
            if (segmentId != null && segmentStates.containsKey(segmentId)) {
              pointMap['state'] = segmentStates[segmentId];
            }

            updatedPath.add(pointMap);
          }

          updateData['path'] = updatedPath;
        }
        // For backward compatibility, also check for fullPath
        else if (data.containsKey('fullPath')) {
          final List<dynamic> fullPath = data['fullPath'] as List<dynamic>;
          final List<Map<String, dynamic>> updatedPath = [];

          for (final point in fullPath) {
            final Map<String, dynamic> pointMap =
                Map<String, dynamic>.from(point as Map<String, dynamic>);
            final String? segmentId = pointMap['segmentId'] as String?;

            // Update point state if its segment state is provided
            if (segmentId != null && segmentStates.containsKey(segmentId)) {
              pointMap['state'] = segmentStates[segmentId];
            }

            updatedPath.add(pointMap);
          }

          // Use the new 'path' field name instead of 'fullPath'
          updateData['path'] = updatedPath;
        }
      }
    }

    await _pipelinesCollection.doc(pipelineId).update(updateData);

    // Also update the state in the elements collection
    await _elementsCollection.doc(pipelineId).update({
      'state': state,
      'updated_at':
          now, // Use milliseconds timestamp instead of server timestamp
    });
  }

  // Update pipeline details with full data from HivePipelineModel.toFirestoreDetails()
  Future<void> updatePipelineDetailsWithFullData(
      String pipelineId, Map<String, dynamic> pipelineDetails) async {
    debugPrint('Updating pipeline $pipelineId with full details');

    // Get the state from the details
    final state = pipelineDetails['state'] as String? ?? 'Active';
    debugPrint('🔄 Pipeline state from details: $state');

    // Ensure state is included at the top level
    if (!pipelineDetails.containsKey('state')) {
      debugPrint('⚠️ State not found at top level, adding it');
      pipelineDetails['state'] = state;
    }

    // Ensure path is included
    if (!pipelineDetails.containsKey('path')) {
      debugPrint('⚠️ Path not found, adding a default path');
      // Add a default path with at least two points
      pipelineDetails['path'] = [
        {'lat': 0.0, 'lng': 0.0},
        {'lat': 0.001, 'lng': 0.001}
      ];
    } else {
      debugPrint(
          '✅ Path found with ${(pipelineDetails['path'] as List).length} points');
    }

    // Ensure state is included in properties
    if (pipelineDetails.containsKey('properties')) {
      final properties = pipelineDetails['properties'] as Map<String, dynamic>;
      if (!properties.containsKey('state') || properties['state'] != state) {
        debugPrint(
            '⚠️ State in properties is missing or different, updating it');
        properties['state'] = state;
        pipelineDetails['properties'] = properties;
      }
    }

    // Get current timestamp in milliseconds
    final now = DateTime.now().millisecondsSinceEpoch;

    // Update the pipeline collection with all details
    debugPrint('🔄 Updating pipeline collection with state: $state');
    await _pipelinesCollection.doc(pipelineId).update({
      ...pipelineDetails,
      'state': state, // Explicitly set state at top level
      'lastUpdated':
          now, // Use milliseconds timestamp instead of server timestamp
    });

    // Also update the state in the elements collection
    debugPrint('🔄 Updating elements collection with state: $state');
    try {
      await _elementsCollection.doc(pipelineId).update({
        'state': state,
        'updated_at':
            now, // Use milliseconds timestamp instead of server timestamp
      });
      debugPrint('✅ Successfully updated state in elements collection');
    } catch (e) {
      debugPrint('⚠️ Error updating elements collection: $e');
      // If the element doesn't exist, create it with minimal data
      if (e.toString().contains('not-found')) {
        debugPrint('⚠️ Pipeline element not found, creating minimal element');
        try {
          // Create a minimal element with the state
          await _elementsCollection.doc(pipelineId).set({
            'type': 'pipeline',
            'state': state,
            'title': pipelineDetails['title'] ?? 'Pipeline',
            'areaId': pipelineDetails['areaId'] ?? 'default',
            'position': pipelineDetails['startPoint'] ??
                {'latitude': 0, 'longitude': 0},
            'created_at': now,
            'updated_at': now,
          });
          debugPrint('✅ Created minimal pipeline element with state: $state');
        } catch (innerError) {
          debugPrint('❌ Error creating minimal pipeline element: $innerError');
        }
      } else {
        rethrow;
      }
    }

    debugPrint(
        '✅ Successfully updated pipeline $pipelineId with full details and state: $state');
  }

  // Add pipeline details with full data from HivePipelineModel.toFirestoreDetails()
  Future<void> addPipelineDetailsWithFullData(
      String pipelineId, Map<String, dynamic> pipelineDetails) async {
    debugPrint('Adding pipeline $pipelineId with full details');

    // Get the state from the details
    final state = pipelineDetails['state'] as String? ?? 'Active';
    debugPrint('🔄 Pipeline state from details: $state');

    // Ensure state is included at the top level
    if (!pipelineDetails.containsKey('state')) {
      debugPrint('⚠️ State not found at top level, adding it');
      pipelineDetails['state'] = state;
    }

    // Ensure path is included
    if (!pipelineDetails.containsKey('path')) {
      debugPrint('⚠️ Path not found, adding a default path');
      // Add a default path with at least two points
      pipelineDetails['path'] = [
        {'lat': 0.0, 'lng': 0.0},
        {'lat': 0.001, 'lng': 0.001}
      ];
    } else {
      debugPrint(
          '✅ Path found with ${(pipelineDetails['path'] as List).length} points');
    }

    // Ensure state is included in properties
    if (pipelineDetails.containsKey('properties')) {
      final properties = pipelineDetails['properties'] as Map<String, dynamic>;
      if (!properties.containsKey('state') || properties['state'] != state) {
        debugPrint(
            '⚠️ State in properties is missing or different, updating it');
        properties['state'] = state;
        pipelineDetails['properties'] = properties;
      }
    }

    // Get current timestamp in milliseconds
    final now = DateTime.now().millisecondsSinceEpoch;

    // Add the pipeline details to the pipelines collection
    debugPrint('🔄 Adding pipeline to pipelines collection with state: $state');
    await _pipelinesCollection.doc(pipelineId).set({
      ...pipelineDetails,
      'state': state, // Explicitly set state at top level
      'createdAt':
          now, // Use milliseconds timestamp instead of server timestamp
      'lastUpdated':
          now, // Use milliseconds timestamp instead of server timestamp
    });

    // Also add the state to the elements collection if it exists
    debugPrint('🔄 Checking if pipeline exists in elements collection');
    final elementDoc = await _elementsCollection.doc(pipelineId).get();
    if (elementDoc.exists) {
      debugPrint('🔄 Updating elements collection with state: $state');
      await _elementsCollection.doc(pipelineId).update({
        'state': state,
        'updated_at': now,
      });
      debugPrint('✅ Successfully updated state in elements collection');
    } else {
      debugPrint('⚠️ Pipeline element not found, creating minimal element');
      try {
        // Create a minimal element with the state
        await _elementsCollection.doc(pipelineId).set({
          'type': 'pipeline',
          'state': state,
          'title': pipelineDetails['title'] ?? 'Pipeline',
          'areaId': pipelineDetails['areaId'] ?? 'default',
          'position':
              pipelineDetails['startPoint'] ?? {'latitude': 0, 'longitude': 0},
          'created_at': now,
          'updated_at': now,
        });
        debugPrint('✅ Created minimal pipeline element with state: $state');
      } catch (e) {
        debugPrint('❌ Error creating minimal pipeline element: $e');
      }
    }

    debugPrint(
        '✅ Successfully added pipeline $pipelineId with full details and state: $state');
  }

  // Update a specific pipeline segment's state
  Future<void> updatePipelineSegmentState(
      String pipelineId, String segmentId, String state) async {
    debugPrint(
        'Updating segment $segmentId in pipeline $pipelineId to state: $state');

    try {
      // First get the current pipeline data to get segments
      final pipelineDoc = await _pipelinesCollection.doc(pipelineId).get();
      if (pipelineDoc.exists) {
        final data = pipelineDoc.data() as Map<String, dynamic>;

        // Update segments if they exist
        if (data.containsKey('segments')) {
          final List<dynamic> segments = data['segments'] as List<dynamic>;
          final List<Map<String, dynamic>> updatedSegments = [];
          bool segmentFound = false;

          for (final segment in segments) {
            final Map<String, dynamic> segmentMap =
                Map<String, dynamic>.from(segment as Map<String, dynamic>);
            final String currentSegmentId = segmentMap['id'] as String;

            // Update segment state if it matches the target segment
            if (currentSegmentId == segmentId) {
              segmentMap['state'] = state;
              segmentFound = true;
              debugPrint(
                  'Found and updated segment $segmentId state to $state');
            }

            updatedSegments.add(segmentMap);
          }

          // If segment wasn't found, add a warning
          if (!segmentFound) {
            debugPrint(
                'Warning: Segment $segmentId not found in pipeline $pipelineId');
          }

          // Get current timestamp in milliseconds
          final now = DateTime.now().millisecondsSinceEpoch;

          // Update the segments array
          await _pipelinesCollection.doc(pipelineId).update({
            'segments': updatedSegments,
            'lastUpdated':
                now, // Use milliseconds timestamp instead of server timestamp
          });

          // Update path if it exists (previously fullPath)
          if (data.containsKey('path')) {
            final List<dynamic> pathPoints = data['path'] as List<dynamic>;
            final List<Map<String, dynamic>> updatedPath = [];

            for (final point in pathPoints) {
              final Map<String, dynamic> pointMap =
                  Map<String, dynamic>.from(point as Map<String, dynamic>);
              final String? pointSegmentId = pointMap['segmentId'] as String?;

              // Update point state if its segment ID matches
              if (pointSegmentId == segmentId) {
                pointMap['state'] = state;
              }

              updatedPath.add(pointMap);
            }

            // Update the path array with timestamp
            await _pipelinesCollection.doc(pipelineId).update({
              'path': updatedPath,
              'lastUpdated': now, // Use the same timestamp for consistency
            });
          }
          // For backward compatibility, also check for fullPath
          else if (data.containsKey('fullPath')) {
            final List<dynamic> fullPath = data['fullPath'] as List<dynamic>;
            final List<Map<String, dynamic>> updatedPath = [];

            for (final point in fullPath) {
              final Map<String, dynamic> pointMap =
                  Map<String, dynamic>.from(point as Map<String, dynamic>);
              final String? pointSegmentId = pointMap['segmentId'] as String?;

              // Update point state if its segment ID matches
              if (pointSegmentId == segmentId) {
                pointMap['state'] = state;
              }

              updatedPath.add(pointMap);
            }

            // Use the new 'path' field name instead of 'fullPath'
            await _pipelinesCollection.doc(pipelineId).update({
              'path': updatedPath,
              'lastUpdated': now, // Use the same timestamp for consistency
            });
          }

          debugPrint('Successfully updated segment state in Firestore');
          return;
        } else {
          debugPrint(
              'Warning: No segments array found in pipeline $pipelineId');
        }
      } else {
        debugPrint('Warning: Pipeline $pipelineId not found in Firestore');
      }
    } catch (e) {
      debugPrint('Error updating segment state: $e');
    }

    // Fallback to the simpler method if the detailed update fails
    return updatePipelineDetails(pipelineId, {},
        segmentStates: {segmentId: state});
  }

  // Update user details
  Future<void> updateUserDetails(
      String userId, Map<String, dynamic> details) async {
    await _usersCollection.doc(userId).update({
      'details': details,
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Update valve details
  Future<void> updateValveDetails(
      String valveId, Map<String, dynamic> specifications) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    await _valvesCollection.doc(valveId).update({
      'specifications': specifications,
      'type': specifications['type'],
      'controlType': specifications['controlType'],
      'size': specifications['size'],
      'status': specifications['status'] ?? 'Operational',
      'lastMaintenance': specifications['lastMaintenance'],
      'pricePerUnit': specifications['pricePerUnit'],
      'isDeleted': specifications['isDeleted'] ?? false,
      'isSync': specifications['isSync'] ?? true,
      'lastUpdated': now,
    });
  }

  // Update connector details
  Future<void> updateConnectorDetails(String connectorId, int connectorType,
      Map<String, dynamic> specifications, List<String> connections) async {
    await _connectorsCollection.doc(connectorId).update({
      'type': connectorType,
      'specifications': specifications,
      'connections': connections,
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Update smart meter details
  Future<void> updateSmartMeterDetails(
      String smartMeterId, Map<String, dynamic> specifications) async {
    await _firestore.collection('smart_meters').doc(smartMeterId).update({
      'specifications': specifications,
      'reading': specifications['reading'] ?? '0',
      'type': specifications['type'] ?? 'Standard',
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Update element title
  Future<void> updateElementTitle(String elementId, String title) async {
    await _elementsCollection.doc(elementId).update({
      'title': title,
      'metadata.lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  // Update element state directly
  Future<void> updateElementState(String elementId, String state) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    try {
      debugPrint('🔄 Directly updating element $elementId state to: $state');
      await _elementsCollection.doc(elementId).update({
        'state': state,
        'updated_at': now,
      });
      debugPrint('✅ Successfully updated element state in elements collection');
    } catch (e) {
      debugPrint('❌ Error updating element state: $e');
      if (e.toString().contains('not-found')) {
        debugPrint('⚠️ Element not found, cannot update state');
      }
      rethrow;
    }
  }

  // Update element metadata
  Future<void> updateElementMetadata(
      String elementId, String title, Map<String, dynamic> metadata) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final updateData = <String, dynamic>{
      'title': title,
      'updated_at': now,
    };

    // Add each metadata field with dot notation
    metadata.forEach((key, value) {
      updateData['metadata.$key'] = value;
    });

    // Ensure both fields are updated for backward compatibility
    updateData['metadata.lastUpdated'] = now;

    // First get the element to check its type
    final elementDoc = await _elementsCollection.doc(elementId).get();
    if (elementDoc.exists) {
      final elementData = elementDoc.data() as Map<String, dynamic>;
      final type = elementData['type'] as String;

      // For user elements, update user-specific fields directly
      if (type == 'user') {
        // Update userId field if provided
        if (metadata.containsKey('userId')) {
          updateData['userId'] = metadata['userId'];
        }

        // Update minimal user details if provided
        if (metadata.containsKey('name')) {
          updateData['name'] = metadata['name'];
        }
        if (metadata.containsKey('contact')) {
          updateData['contact'] = metadata['contact'];
        }
        if (metadata.containsKey('address')) {
          updateData['address'] = metadata['address'];
        }
      }
    }

    await _elementsCollection.doc(elementId).update(updateData);
  }

  // Update element position
  Future<void> updateElementPosition(String elementId, LatLng position) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final updateData = <String, dynamic>{
      'position': GeoPoint(position.latitude, position.longitude),
      'updated_at': now,
      'metadata.lastUpdated': now,
    };

    await _elementsCollection.doc(elementId).update(updateData);
    debugPrint(
        'Updated position for element $elementId to (${position.latitude}, ${position.longitude})');
  }

  // Check if an element exists
  Future<bool> elementExists(String elementId) async {
    final doc = await _elementsCollection.doc(elementId).get();
    return doc.exists;
  }

  // Check if a user exists in the user collection
  Future<bool> userExists(String userId) async {
    final doc = await _usersCollection.doc(userId).get();
    return doc.exists;
  }

  // Check if a valve exists in the valves collection
  Future<bool> valveExists(String valveId) async {
    final doc = await _valvesCollection.doc(valveId).get();
    return doc.exists;
  }

  // Check if a connector exists in the connectors collection
  Future<bool> connectorExists(String connectorId) async {
    final doc = await _connectorsCollection.doc(connectorId).get();
    return doc.exists;
  }

  // Check if a smart meter exists in the smart_meters collection
  Future<bool> smartMeterExists(String smartMeterId) async {
    final doc =
        await _firestore.collection('smart_meters').doc(smartMeterId).get();
    return doc.exists;
  }

  // Check if a pipeline exists in the pipelines collection
  Future<bool> pipelineExists(String pipelineId) async {
    final doc = await _pipelinesCollection.doc(pipelineId).get();
    return doc.exists;
  }

  // Delete an element
  Future<void> deleteElement(String elementId) async {
    try {
      // Get the element to find its type and area
      final elementDoc = await _elementsCollection.doc(elementId).get();
      if (!elementDoc.exists) {
        debugPrint('Element $elementId not found in elements collection');
        return;
      }

      final elementData = elementDoc.data() as Map<String, dynamic>;
      final type = elementData['type'] as String;
      final areaId = elementData['areaId'] as String;

      debugPrint('Deleting $type with ID $elementId from area $areaId');

      // Use a batch to ensure atomic operations
      final batch = _firestore.batch();

      // Add element deletion to batch
      batch.delete(_elementsCollection.doc(elementId));

      // Add type-specific collection deletion to batch
      switch (type) {
        case 'pipeline':
          batch.delete(_pipelinesCollection.doc(elementId));
          break;
        case 'user':
          // Do not delete from user collection, only from elements collection
          // This ensures user details remain in the user collection
          debugPrint(
              'Not deleting user $elementId from user collection, only from elements');
          break;
        case 'valve':
          batch.delete(_valvesCollection.doc(elementId));
          break;
        case 'connector':
          batch.delete(_connectorsCollection.doc(elementId));
          break;
        case 'smart_meter':
          batch.delete(_firestore.collection('smart_meters').doc(elementId));
          break;
      }

      // Commit the batch
      await batch.commit();
      debugPrint('Successfully deleted $type with ID $elementId');

      // Update area summary
      await updateAreaSummary(areaId, type, -1);
    } catch (e) {
      debugPrint('Error in deleteElement: $e');
      throw Exception('Failed to delete element: $e');
    }
  }

  // Convert a UserValvePosition to a Firestore element
  Future<String> addUserValvePosition(UserValvePosition position) async {
    // Determine which area this position belongs to
    final areaId = await getAreaForPosition(position.position);

    String elementId;

    switch (position.type) {
      case PositionType.user:
        elementId = await addElement(
          'user',
          position.position,
          areaId,
          position.title,
          {'type': 'user'},
        );
        await addUserDetails(elementId, {'name': position.title});
        break;
      case PositionType.valve:
        elementId = await addElement(
          'valve',
          position.position,
          areaId,
          position.title,
          {'type': 'valve'},
        );
        await addValveDetails(
          valveId: elementId,
          status: 'Operational',
          type: 'Standard',
          lastMaintainance: 0,
          controlType: 'Manual',
          size: 25.0,
          isDeleted: false,
          isSync: true,
        );
        break;
      case PositionType.connector:
        elementId = await addElement(
          'connector',
          position.position,
          areaId,
          position.title,
          {
            'type': 'connector',
            'connectorType': position.connectorType ?? ConnectorType.tee,
          },
        );
        await addConnectorDetails(
          elementId,
          position.connectorType ?? ConnectorType.tee,
          {'material': 'Standard'},
        );
        break;
      default:
        throw ArgumentError('Invalid position type: ${position.type}');
    }

    return elementId;
  }

  // Add a pipeline from the app's Pipeline model
  Future<String> addPipeline(Pipeline pipeline,
      {String state = 'Active'}) async {
    if (pipeline.points.isEmpty) {
      throw ArgumentError('Pipeline must have at least one point');
    }

    // Determine which area this pipeline belongs to
    final areaId = await getAreaForPosition(pipeline.points.first);

    // Add to elements collection with minimal data (only start/end points)
    final pipelineId = await addPipelineElement(
      pipeline.points,
      areaId,
      'Pipeline ${pipeline.id}',
      state: state,
    );

    // Add detailed information to pipelines collection (full path and properties)
    await addPipelineDetails(
      pipelineId,
      pipeline.points,
      {
        'segmentId': pipeline.segmentId,
        'material': 'Standard',
        'pipeType': 'Standard',
        'diameter': 100,
        'pressure': 'Medium',
        'state': state,
      },
    );

    return pipelineId;
  }

  // Convert Firestore elements to app models
  List<Pipeline> convertToPipelines(
      List<FirestorePipeline> firestorePipelines) {
    debugPrint(
        'Converting ${firestorePipelines.length} FirestorePipelines to Pipelines');

    final pipelines = firestorePipelines.map((fp) {
      try {
        debugPrint('Converting pipeline ${fp.id}');
        final pipeline = fp.toPipeline();
        debugPrint(
            'Successfully converted pipeline ${pipeline.id} with ${pipeline.points.length} points');
        return pipeline;
      } catch (e) {
        debugPrint('Error converting pipeline ${fp.id}: $e');
        // Return a minimal valid pipeline to avoid crashing the app
        return Pipeline(
          id: fp.id,
          points: [const LatLng(0, 0), const LatLng(0.001, 0.001)],
          segmentId: null,
        );
      }
    }).toList();

    debugPrint('Converted ${pipelines.length} pipelines');
    return pipelines;
  }

  List<UserValvePosition> convertToUserValvePositions(
      List<FirestoreElement> elements) {
    return elements.map((element) {
      switch (element.type) {
        case 'user':
          return (element as FirestoreUser).toUserValvePosition();
        case 'valve':
          return (element as FirestoreValve).toUserValvePosition();
        case 'connector':
          return (element as FirestoreConnector).toUserValvePosition();
        default:
          throw ArgumentError('Invalid element type: ${element.type}');
      }
    }).toList();
  }

  // Get default bounds for the world
  LatLngBounds getDefaultBounds() {
    return LatLngBounds(
      southwest: const LatLng(-90, -180),
      northeast: const LatLng(90, 180),
    );
  }

  // Create HiveUserModel from FirestoreElement
  HiveUserModel createHiveUserFromFirestore(FirestoreElement element) {
    final now = DateTime.now();
    final createdAt = element.metadata.containsKey('createdAt')
        ? DateTime.fromMillisecondsSinceEpoch(
            element.metadata['createdAt'] as int)
        : now;

    // Try to get the updated timestamp from either metadata.lastUpdated or updated_at field
    DateTime updatedAt;
    if (element.metadata.containsKey('lastUpdated')) {
      updatedAt = DateTime.fromMillisecondsSinceEpoch(
          element.metadata['lastUpdated'] as int);
    } else {
      // Get the document data to check for updated_at field
      final data = (element as dynamic).data;
      if (data != null &&
          data is Map<String, dynamic> &&
          data.containsKey('updated_at')) {
        updatedAt =
            DateTime.fromMillisecondsSinceEpoch(data['updated_at'] as int);
      } else {
        updatedAt = now;
      }
    }

    return HiveUserModel(
      id: element.id,
      latitude: element.position.latitude,
      longitude: element.position.longitude,
      title: element.title,
      areaId: element.areaId,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isSync: true,
    );
  }

  // Create HiveValveModel from FirestoreElement
  HiveValveModel createHiveValveFromFirestore(FirestoreElement element) {
    final now = DateTime.now();
    final createdAt = element.metadata.containsKey('createdAt')
        ? DateTime.fromMillisecondsSinceEpoch(
            element.metadata['createdAt'] as int)
        : now;

    // Try to get the updated timestamp from either metadata.lastUpdated or updated_at field
    DateTime updatedAt;
    if (element.metadata.containsKey('lastUpdated')) {
      updatedAt = DateTime.fromMillisecondsSinceEpoch(
          element.metadata['lastUpdated'] as int);
    } else {
      // Get the document data to check for updated_at field
      final data = (element as dynamic).data;
      if (data != null &&
          data is Map<String, dynamic> &&
          data.containsKey('updated_at')) {
        updatedAt =
            DateTime.fromMillisecondsSinceEpoch(data['updated_at'] as int);
      } else {
        updatedAt = now;
      }
    }

    // Extract valve-specific data from metadata
    final specifications =
        element.metadata['specifications'] as Map<String, dynamic>? ?? {};
    final status = element.metadata['status'] as String? ?? 'Operational';
    final isDeleted = element.metadata['isDeleted'] as bool? ?? false;
    final pricePerUnit = element.metadata['pricePerUnit'] as double?;
    final lastMaintenance = element.metadata['lastMaintenance'] != null
        ? DateTime.fromMillisecondsSinceEpoch(
            element.metadata['lastMaintenance'] as int)
        : null;

    // Extract valve type and size from specifications
    final valveType = specifications['type'] as String? ?? 'Standard';
    final valveSize = specifications['size'] as String? ?? '25mm';
    final controlType = specifications['controlType'] as String? ?? 'Manual';

    // Parse size to double (remove 'mm' suffix if present)
    double size = 25.0;
    try {
      if (valveSize.endsWith('mm')) {
        size = double.parse(valveSize.replaceAll('mm', ''));
      } else {
        size = double.parse(valveSize);
      }
    } catch (e) {
      debugPrint('Error parsing valve size: $e, using default 25.0');
    }

    return HiveValveModel(
      id: element.id,
      latitude: element.position.latitude,
      longitude: element.position.longitude,
      title: element.title,
      type: valveType,
      size: size,
      controlType: controlType,
      status: status,
      lastMaintenance: lastMaintenance,
      areaId: element.areaId,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isSync: true,
      isDeleted: isDeleted,
      pricePerUnit: pricePerUnit,
    );
  }

  // Create HiveConnectorModel from FirestoreElement
  HiveConnectorModel createHiveConnectorFromFirestore(
      FirestoreElement element) {
    final now = DateTime.now();
    final createdAt = element.metadata.containsKey('createdAt')
        ? DateTime.fromMillisecondsSinceEpoch(
            element.metadata['createdAt'] as int)
        : now;

    // Try to get the updated timestamp from either metadata.lastUpdated or updated_at field
    DateTime updatedAt;
    if (element.metadata.containsKey('lastUpdated')) {
      updatedAt = DateTime.fromMillisecondsSinceEpoch(
          element.metadata['lastUpdated'] as int);
    } else {
      // Get the document data to check for updated_at field
      final data = (element as dynamic).data;
      if (data != null &&
          data is Map<String, dynamic> &&
          data.containsKey('updated_at')) {
        updatedAt =
            DateTime.fromMillisecondsSinceEpoch(data['updated_at'] as int);
      } else {
        updatedAt = now;
      }
    }

    // Get connector type from metadata
    final connectorType = element.metadata.containsKey('connectorType')
        ? element.metadata['connectorType'] as int
        : 0; // Default to tee (0)

    return HiveConnectorModel(
      id: element.id,
      latitude: element.position.latitude,
      longitude: element.position.longitude,
      title: element.title,
      areaId: element.areaId,
      connectorType: connectorType,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isSync: true,
      specifications: {},
      connections: [],
    );
  }

  /* TODO: Uncomment and implement when HiveSmartMeterModel is defined
  // Create HiveSmartMeterModel from FirestoreElement
  HiveSmartMeterModel createHiveSmartMeterFromFirestore(
      FirestoreElement element) {
    final now = DateTime.now();
    final createdAt = element.metadata.containsKey('createdAt')
        ? DateTime.fromMillisecondsSinceEpoch(
            element.metadata['createdAt'] as int)
        : now;

    // Try to get the updated timestamp from either metadata.lastUpdated or updated_at field
    DateTime updatedAt;
    if (element.metadata.containsKey('lastUpdated')) {
      updatedAt = DateTime.fromMillisecondsSinceEpoch(
          element.metadata['lastUpdated'] as int);
    } else {
      // Get the document data to check for updated_at field
      final data = (element as dynamic).data;
      if (data != null &&
          data is Map<String, dynamic> &&
          data.containsKey('updated_at')) {
        updatedAt =
            DateTime.fromMillisecondsSinceEpoch(data['updated_at'] as int);
      } else {
        updatedAt = now;
      }
    }

    // Get status from metadata
    final status = element.metadata.containsKey('status')
        ? element.metadata['status'] as String
        : 'Active'; // Default to Active

    return HiveSmartMeterModel(
      id: element.id,
      latitude: element.position.latitude,
      longitude: element.position.longitude,
      title: element.title,
      areaId: element.areaId,
      status: status,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isSync: true,
      specifications: {},
    );
  }
  */

  // Create HivePipelineModel from FirestorePipeline
  Future<HivePipelineModel> createHivePipelineFromFirestore(
      FirestorePipeline pipeline) async {
    final now = DateTime.now();

    // First convert to Pipeline model to get basic points
    final pipelineModel = pipeline.toPipeline();
    List<LatLng> points = pipelineModel.points;

    // Try to get full path data from the pipelines collection
    try {
      final pipelineDetails = await _pipelinesCollection.doc(pipeline.id).get();
      if (pipelineDetails.exists) {
        final data = pipelineDetails.data() as Map<String, dynamic>;
        if (data.containsKey('fullPath')) {
          final fullPath = data['fullPath'] as List<dynamic>;
          points = fullPath
              .map((p) => LatLng(
                  (p['lat'] as num).toDouble(), (p['lng'] as num).toDouble()))
              .toList();
          debugPrint(
              'Retrieved full path with ${points.length} points for pipeline ${pipeline.id}');
        }
      }
    } catch (e) {
      debugPrint('Error retrieving full path for pipeline ${pipeline.id}: $e');
      // Continue with the basic points if we can't get the full path
    }

    // Get properties from the pipelines collection
    Map<String, dynamic> properties = {};
    try {
      final pipelineDetails = await _pipelinesCollection.doc(pipeline.id).get();
      if (pipelineDetails.exists) {
        final data = pipelineDetails.data() as Map<String, dynamic>;

        // Get the overall pipeline state
        final state = data['state'] as String? ?? 'Active';

        if (data.containsKey('properties')) {
          properties = Map<String, dynamic>.from(
              data['properties'] as Map<String, dynamic>);

          // Ensure state is in properties
          properties['state'] = state;
        } else {
          // Create basic properties with state
          properties = {
            'state': state,
            'pipeType': 'Standard',
            'diameter': 100,
            'material': 'PVC',
            'pressure': 'Medium',
          };
        }

        // Extract segment states from segments array
        if (data.containsKey('segments')) {
          final segments = data['segments'] as List<dynamic>;
          final Map<String, String> segmentStates = {};

          for (final segment in segments) {
            final segmentMap = segment as Map<String, dynamic>;
            final segmentId = segmentMap['id'] as String;
            final segmentState = segmentMap['state'] as String? ?? state;

            segmentStates[segmentId] = segmentState;
          }

          // Add segment states to properties
          if (segmentStates.isNotEmpty) {
            properties['segmentStates'] = segmentStates;
            debugPrint(
                'Retrieved ${segmentStates.length} segment states for pipeline ${pipeline.id}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error retrieving properties for pipeline ${pipeline.id}: $e');
    }

    final segmentId = pipeline.metadata.containsKey('segmentId')
        ? pipeline.metadata['segmentId'] as String? ?? ''
        : '';

    // Try to get the updated timestamp from either metadata.lastUpdated or updated_at field
    DateTime updatedAt;
    if (pipeline.metadata.containsKey('lastUpdated')) {
      updatedAt = DateTime.fromMillisecondsSinceEpoch(
          pipeline.metadata['lastUpdated'] as int);
    } else {
      // Get the document data to check for updated_at field
      final data = (pipeline as dynamic).data;
      if (data != null &&
          data is Map<String, dynamic> &&
          data.containsKey('updated_at')) {
        updatedAt =
            DateTime.fromMillisecondsSinceEpoch(data['updated_at'] as int);
      } else {
        updatedAt = now;
      }
    }

    return HivePipelineModel(
      id: pipeline.id,
      points: points,
      title: pipeline.title,
      areaId: pipeline.areaId,
      segmentId: segmentId,
      createdAt: pipeline.metadata.containsKey('createdAt')
          ? DateTime.fromMillisecondsSinceEpoch(
              pipeline.metadata['createdAt'] as int)
          : now,
      updatedAt: updatedAt,
      isSync: true,
      properties: properties,
    );
  }

  // Helper method to create the appropriate element subclass from a document
  FirestoreElement _createElementFromDoc(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final type = data['type'] as String;

    switch (type) {
      case 'pipeline':
        return FirestorePipeline.fromFirestore(doc);
      case 'user':
        return FirestoreUser.fromFirestore(doc);
      case 'valve':
        return FirestoreValve.fromFirestore(doc);
      case 'connector':
        return FirestoreConnector.fromFirestore(doc);
      case 'smart_meter':
        return FirestoreElement.fromFirestore(doc); // Use base class for now
      default:
        return FirestoreElement.fromFirestore(doc);
    }
  }

  // Helper function to generate consistent segment IDs
  String _generateSegmentId(LatLng start, LatLng end) {
    // Ensure consistent segment ID regardless of point order
    final startStr = '${start.latitude},${start.longitude}';
    final endStr = '${end.latitude},${end.longitude}';

    if (startStr.compareTo(endStr) > 0) {
      return 'segment_${end.latitude}_${end.longitude}_${start.latitude}_${start.longitude}';
    }
    return 'segment_${start.latitude}_${start.longitude}_${end.latitude}_${end.longitude}';
  }

  // Calculate path length in meters
  double _calculatePathLength(List<LatLng> path) {
    double totalDistance = 0;
    for (int i = 0; i < path.length - 1; i++) {
      totalDistance += _calculateDistance(path[i], path[i + 1]);
    }
    return totalDistance;
  }

  // Calculate distance between two points in meters
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // in meters

    final lat1 = point1.latitude * pi / 180;
    final lat2 = point2.latitude * pi / 180;
    final dLat = (point2.latitude - point1.latitude) * pi / 180;
    final dLon = (point2.longitude - point1.longitude) * pi / 180;

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  // Get all elements of a specific type
  Future<List<DocumentSnapshot>> getAllElements(String type) async {
    final query =
        _firestore.collection('elements').where('type', isEqualTo: type);

    final snapshot = await query.get();
    return snapshot.docs;
  }

  // Get all users from user collection
  Future<List<DocumentSnapshot>> getAllUsersFromUserCollection() async {
    debugPrint('FirestoreRepository: Getting all users from user collection');
    try {
      final query = _firestore.collection('user');
      final snapshot = await query.get();
      debugPrint(
          'FirestoreRepository: Found ${snapshot.docs.length} users in user collection');

      // Debug: Print the first document if available
      if (snapshot.docs.isNotEmpty) {
        final firstDoc = snapshot.docs.first;
        debugPrint(
            'FirestoreRepository: First user document ID: ${firstDoc.id}');
        debugPrint(
            'FirestoreRepository: First user document data: ${firstDoc.data()}');
      } else {
        debugPrint('FirestoreRepository: No users found in user collection');
      }

      return snapshot.docs;
    } catch (e) {
      debugPrint(
          'FirestoreRepository: Error getting users from user collection: $e');
      rethrow;
    }
  }

  // Add or update a user in the user collection
  Future<void> addOrUpdateUser(UserModel user) async {
    debugPrint(
        'FirestoreRepository: Adding/updating user ${user.id} in user collection');
    try {
      await _firestore.collection('user').doc(user.id).set(user.toJson());
      debugPrint(
          'FirestoreRepository: Successfully added/updated user ${user.id}');
    } catch (e) {
      debugPrint('FirestoreRepository: Error adding/updating user: $e');
      rethrow;
    }
  }

  // Update a user in the user collection
  Future<void> updateUser(UserModel user) async {
    debugPrint(
        'FirestoreRepository: Updating user ${user.id} in user collection');
    try {
      await _firestore.collection('user').doc(user.id).update(user.toJson());
      debugPrint('FirestoreRepository: Successfully updated user ${user.id}');
    } catch (e) {
      debugPrint('FirestoreRepository: Error updating user: $e');
      rethrow;
    }
  }

  // Update only the position of a user in the user collection
  Future<void> updateUserPosition(
      String userId, Map<String, dynamic> updates) async {
    debugPrint(
        'FirestoreRepository: Updating only position for user $userId in user collection');
    try {
      await _firestore.collection('user').doc(userId).update(updates);
      debugPrint(
          'FirestoreRepository: Successfully updated position for user $userId');
    } catch (e) {
      debugPrint('FirestoreRepository: Error updating user position: $e');
      rethrow;
    }
  }

  // Add a user to the user collection
  Future<void> addUser(UserModel user) async {
    debugPrint(
        'FirestoreRepository: Adding user ${user.id} to user collection');
    try {
      await _firestore.collection('user').doc(user.id).set(user.toJson());
      debugPrint('FirestoreRepository: Successfully added user ${user.id}');
    } catch (e) {
      debugPrint('FirestoreRepository: Error adding user: $e');
      rethrow;
    }
  }

  // Get all pipelines
  Future<List<DocumentSnapshot>> getAllPipelines() async {
    final query = _firestore.collection('pipelines');
    final snapshot = await query.get();
    return snapshot.docs;
  }

  Future<List<DocumentSnapshot>> getElementsUpdatedAfter(
    String type,
    int timestamp,
  ) async {
    final query = _firestore
        .collection('elements')
        .where('type', isEqualTo: type)
        .where('updated_at', isGreaterThan: timestamp)
        .orderBy('updated_at', descending: true);

    final snapshot = await query.get();
    return snapshot.docs;
  }

  Future<List<DocumentSnapshot>> getPipelinesUpdatedAfter(
    int timestamp,
  ) async {
    final query = _firestore
        .collection('pipelines')
        .where('lastUpdated', isGreaterThan: timestamp)
        .orderBy('lastUpdated', descending: true);

    final snapshot = await query.get();
    return snapshot.docs;
  }

  // Add a connector listing item to Firestore
  Future<void> addConnectorListingItem(
      Map<String, dynamic> connectorData) async {
    debugPrint(
        'FirestoreRepository: Adding connector listing item to Firestore');
    try {
      final String id = connectorData['id'] as String;
      await _connectorListingCollection.doc(id).set(connectorData);
      debugPrint(
          'FirestoreRepository: Successfully added connector listing item $id');
    } catch (e) {
      debugPrint(
          'FirestoreRepository: Error adding connector listing item: $e');
      rethrow;
    }
  }

  // Update a connector listing item in Firestore
  Future<void> updateConnectorListingItem(
      String id, Map<String, dynamic> updates) async {
    debugPrint(
        'FirestoreRepository: Updating connector listing item $id in Firestore');
    try {
      await _connectorListingCollection.doc(id).update(updates);
      debugPrint(
          'FirestoreRepository: Successfully updated connector listing item $id');
    } catch (e) {
      debugPrint(
          'FirestoreRepository: Error updating connector listing item: $e');
      rethrow;
    }
  }

  // Get all connector listing items from Firestore
  Future<List<DocumentSnapshot>> getAllConnectorListingItems() async {
    debugPrint(
        'FirestoreRepository: Getting all connector listing items from Firestore');
    try {
      final snapshot = await _connectorListingCollection.get();
      debugPrint(
          'FirestoreRepository: Found ${snapshot.docs.length} connector listing items');
      return snapshot.docs;
    } catch (e) {
      debugPrint(
          'FirestoreRepository: Error getting connector listing items: $e');
      rethrow;
    }
  }

  // Add a pipeline listing item to Firestore
  Future<void> addPipelineListingItem(Map<String, dynamic> pipelineData) async {
    debugPrint(
        'FirestoreRepository: Adding pipeline listing item to Firestore');
    try {
      final String id = pipelineData['id'] as String;
      await _pipelineListingCollection.doc(id).set(pipelineData);
      debugPrint(
          'FirestoreRepository: Successfully added pipeline listing item $id');
    } catch (e) {
      debugPrint('FirestoreRepository: Error adding pipeline listing item: $e');
      rethrow;
    }
  }

  // Update a pipeline listing item in Firestore
  Future<void> updatePipelineListingItem(
      String id, Map<String, dynamic> updates) async {
    debugPrint(
        'FirestoreRepository: Updating pipeline listing item $id in Firestore');
    try {
      await _pipelineListingCollection.doc(id).update(updates);
      debugPrint(
          'FirestoreRepository: Successfully updated pipeline listing item $id');
    } catch (e) {
      debugPrint(
          'FirestoreRepository: Error updating pipeline listing item: $e');
      rethrow;
    }
  }

  // Add a category to Firestore
  Future<void> addCategory(Map<String, dynamic> categoryData) async {
    debugPrint('FirestoreRepository: Adding category to Firestore');
    try {
      final String id = categoryData['id'] as String;
      await _categoriesCollection.doc(id).set(categoryData);
      debugPrint('FirestoreRepository: Successfully added category $id');
    } catch (e) {
      debugPrint('FirestoreRepository: Error adding category: $e');
      rethrow;
    }
  }

  // Get all categories from Firestore
  Future<List<Map<String, dynamic>>> getAllCategories() async {
    debugPrint('FirestoreRepository: Getting all categories from Firestore');
    try {
      final snapshot = await _categoriesCollection.get();
      final categories = snapshot.docs
          .map((doc) => doc.data() as Map<String, dynamic>)
          .toList();
      debugPrint(
          'FirestoreRepository: Successfully got ${categories.length} categories');
      return categories;
    } catch (e) {
      debugPrint('FirestoreRepository: Error getting categories: $e');
      rethrow;
    }
  }

  // Update a category in Firestore
  Future<void> updateCategory(String id, Map<String, dynamic> updates) async {
    debugPrint('FirestoreRepository: Updating category $id in Firestore');
    try {
      await _categoriesCollection.doc(id).update(updates);
      debugPrint('FirestoreRepository: Successfully updated category $id');
    } catch (e) {
      debugPrint('FirestoreRepository: Error updating category: $e');
      rethrow;
    }
  }

  // Delete a category from Firestore
  Future<void> deleteCategory(String id) async {
    debugPrint('FirestoreRepository: Deleting category $id from Firestore');
    try {
      await _categoriesCollection.doc(id).delete();
      debugPrint('FirestoreRepository: Successfully deleted category $id');
    } catch (e) {
      debugPrint('FirestoreRepository: Error deleting category: $e');
      rethrow;
    }
  }

  // Get all pipeline listing items from Firestore
  Future<List<DocumentSnapshot>> getAllPipelineListingItems() async {
    debugPrint(
        'FirestoreRepository: Getting all pipeline listing items from Firestore');
    try {
      final snapshot = await _pipelineListingCollection.get();
      debugPrint(
          'FirestoreRepository: Found ${snapshot.docs.length} pipeline listing items');
      return snapshot.docs;
    } catch (e) {
      debugPrint(
          'FirestoreRepository: Error getting pipeline listing items: $e');
      rethrow;
    }
  }

  // Update entire pipeline state
  Future<void> updatePipelineState(String pipelineId, String state) async {
    debugPrint('Updating pipeline $pipelineId state to $state');

    try {
      // Get the pipeline document
      final pipelineDoc = await _pipelinesCollection.doc(pipelineId).get();
      if (!pipelineDoc.exists) {
        debugPrint(
            '⚠️ Pipeline document not found in pipelines collection, creating it');
        // If pipeline doesn't exist in pipelines collection, try to get it from elements
        final elementDoc = await _elementsCollection.doc(pipelineId).get();
        if (!elementDoc.exists) {
          throw Exception('Pipeline $pipelineId not found in any collection');
        }

        // Create a minimal pipeline document
        await _pipelinesCollection.doc(pipelineId).set({
          'state': state,
          'properties': {'state': state},
          'createdAt': DateTime.now().millisecondsSinceEpoch,
          'lastUpdated': DateTime.now().millisecondsSinceEpoch,
        });

        debugPrint(
            '✅ Created minimal pipeline document in pipelines collection');
        return;
      }

      final data = pipelineDoc.data() as Map<String, dynamic>;

      // Get current timestamp in milliseconds
      final now = DateTime.now().millisecondsSinceEpoch;

      debugPrint('🔄 Updating state in pipelines collection to: $state');

      // Update the state in the pipeline collection
      await _pipelinesCollection.doc(pipelineId).update({
        'state': state,
        'lastUpdated':
            now, // Use milliseconds timestamp instead of server timestamp
      });

      // Also update state in properties
      if (data.containsKey('properties')) {
        final Map<String, dynamic> properties = Map<String, dynamic>.from(
            data['properties'] as Map<String, dynamic>);

        // Update the state
        properties['state'] = state;

        // Update properties
        await _pipelinesCollection.doc(pipelineId).update({
          'properties': properties,
          'lastUpdated':
              now, // Use milliseconds timestamp instead of server timestamp
        });

        debugPrint('✅ Updated state in properties to: $state');
      } else {
        // If properties doesn't exist, create it
        await _pipelinesCollection.doc(pipelineId).update({
          'properties': {'state': state},
          'lastUpdated':
              now, // Use milliseconds timestamp instead of server timestamp
        });

        debugPrint('✅ Created properties with state: $state');
      }

      // Also update the state in the elements collection
      debugPrint('🔄 Updating state in elements collection to: $state');

      try {
        await _elementsCollection.doc(pipelineId).update({
          'state': state,
          'updated_at':
              now, // Use milliseconds timestamp instead of server timestamp
        });

        debugPrint('✅ Updated state in elements collection');
      } catch (e) {
        debugPrint('⚠️ Error updating elements collection: $e');
        // If the element doesn't exist, create it with minimal data
        if (e.toString().contains('not-found')) {
          debugPrint('⚠️ Pipeline element not found, skipping elements update');
        } else {
          rethrow;
        }
      }

      debugPrint('Successfully updated pipeline $pipelineId state to $state');
    } catch (e) {
      debugPrint('Error updating pipeline state: $e');
      rethrow;
    }
  }
}
