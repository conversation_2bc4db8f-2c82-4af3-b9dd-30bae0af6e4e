import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/hive_models/hive_category_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';

class AddCategoryScreen extends ConsumerStatefulWidget {
  const AddCategoryScreen({super.key});

  @override
  ConsumerState<AddCategoryScreen> createState() => _AddCategoryScreenState();
}

class _AddCategoryScreenState extends ConsumerState<AddCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedAssetType = 'all';
  final Map<String, dynamic> _properties = {};

  final List<String> _assetTypes = [
    'all',
    'valve',
    'connector',
    'pipeline',
    'smartMeter',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveCategory() async {
    if (_formKey.currentState!.validate()) {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Create properties map with additional details if needed
      final properties = <String, dynamic>{
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        ..._properties,
      };

      // Create a new category model
      final category = HiveCategoryModel.create(
        name: _nameController.text,
        description: _descriptionController.text,
        assetType: _selectedAssetType,
        properties: properties,
      );

      try {
        // Save to Hive
        await hiveService.saveCategory(category);

        // Save to Firestore
        await firestoreRepository.addCategory(category.toJson());

        // Mark as synced in Hive
        category.isSync = true;
        await hiveService.saveCategory(category);

        // Check if widget is still mounted before using context
        if (!mounted) return;

        // Show success message and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'Category "${_nameController.text}" added successfully')),
        );
        Navigator.pop(context);
      } catch (e) {
        // Check if widget is still mounted before using context
        if (!mounted) return;

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding category: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Asset Category'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Category Name *',
                  hintText: 'Enter a name for this category',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a category name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Enter a description for this category',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedAssetType,
                decoration: const InputDecoration(
                  labelText: 'Asset Type *',
                ),
                items: _assetTypes.map((type) {
                  String displayName;
                  switch (type) {
                    case 'valve':
                      displayName = 'Valve';
                      break;
                    case 'connector':
                      displayName = 'Connector';
                      break;
                    case 'pipeline':
                      displayName = 'Pipeline';
                      break;
                    case 'smartMeter':
                      displayName = 'Smart Meter';
                      break;
                    case 'all':
                      displayName = 'All Assets';
                      break;
                    default:
                      displayName = type;
                  }
                  return DropdownMenuItem(value: type, child: Text(displayName));
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedAssetType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _saveCategory,
                child: const Text('Save Category'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
