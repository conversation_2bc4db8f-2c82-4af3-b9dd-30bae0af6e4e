import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/hive_models/hive_connector_listing_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';
import '../providers/sync_service_provider.dart';

class AddConnectorTypeScreen extends ConsumerStatefulWidget {
  const AddConnectorTypeScreen({super.key});

  @override
  ConsumerState<AddConnectorTypeScreen> createState() =>
      _AddConnectorTypeScreenState();
}

class _AddConnectorTypeScreenState
    extends ConsumerState<AddConnectorTypeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  String _selectedConnectorType = 'tee';
  String _selectedMaterial = 'PVC';
  final _diameterController = TextEditingController();
  final _widthController = TextEditingController();
  final _heightController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedManufacturer = 'Generic';
  final _modelNumberController = TextEditingController();
  final _priceController = TextEditingController();
  String _selectedPressureRating = 'Standard (10 bar)';

  @override
  void dispose() {
    _titleController.dispose();
    _diameterController.dispose();
    _widthController.dispose();
    _heightController.dispose();
    _descriptionController.dispose();
    _modelNumberController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _saveConnectorType() async {
    if (_formKey.currentState!.validate()) {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Create specifications map with all the details
      final specifications = <String, dynamic>{
        'material': _selectedMaterial,
        'diameter': _diameterController.text,
        'width': _widthController.text,
        'height': _heightController.text,
        'description': _descriptionController.text,
        'manufacturer': _selectedManufacturer,
        'modelNumber': _modelNumberController.text,
        'pressureRating': _selectedPressureRating,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      };

      // Parse price per unit if provided
      double? pricePerUnit;
      if (_priceController.text.isNotEmpty) {
        pricePerUnit = double.tryParse(_priceController.text);
      }

      // Create a new connector listing model
      final connector = HiveConnectorListingModel.create(
        title: _titleController.text,
        connectorType: _selectedConnectorType,
        specifications: specifications,
        pricePerUnit: pricePerUnit,
      );

      try {
        // Save to Hive
        await hiveService.saveConnectorListingItem(connector);

        // Save to Firestore
        await firestoreRepository.addConnectorListingItem(connector.toJson());

        // Mark as synced in Hive
        connector.isSync = true;
        await hiveService.saveConnectorListingItem(connector);

        // Check if widget is still mounted before using context
        if (!mounted) return;

        // Show success message and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'Connector type "${_titleController.text}" added successfully')),
        );

        Navigator.pop(context);
      } catch (e) {
        // Check if widget is still mounted before using context
        if (!mounted) return;

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving connector type: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Connector Type'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Connector Name *',
                  hintText: 'Enter connector name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedConnectorType,
                decoration: const InputDecoration(
                  labelText: 'Connector Type *',
                ),
                items: const [
                  DropdownMenuItem(value: 'tee', child: Text('Tee Connector')),
                  DropdownMenuItem(
                      value: 'elbow', child: Text('Elbow Connector')),
                  DropdownMenuItem(
                      value: 'coupling', child: Text('Coupling Connector')),
                  DropdownMenuItem(
                      value: 'cross', child: Text('Cross Connector')),
                  DropdownMenuItem(
                      value: 'socket',
                      child: Text('Coupling/Socket Connector')),
                  DropdownMenuItem(
                      value: 'reducer',
                      child: Text('Reducer(Concentric/Eccentric)')),
                  DropdownMenuItem(value: 'cap', child: Text('Cap/Plug')),
                  DropdownMenuItem(value: 'union', child: Text('Union')),
                  DropdownMenuItem(value: 'adapter', child: Text('Adapter')),
                  DropdownMenuItem(value: 'nipple', child: Text('Nipple')),
                  DropdownMenuItem(value: 'flange', child: Text('Flange')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedConnectorType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedMaterial,
                decoration: const InputDecoration(
                  labelText: 'Material',
                ),
                items: const [
                  DropdownMenuItem(value: 'PVC', child: Text('PVC')),
                  DropdownMenuItem(value: 'Steel', child: Text('Steel')),
                  DropdownMenuItem(
                      value: 'Cast Iron', child: Text('Cast Iron')),
                  DropdownMenuItem(value: 'Brass', child: Text('Brass')),
                  DropdownMenuItem(value: 'Copper', child: Text('Copper')),
                  DropdownMenuItem(value: 'HDPE', child: Text('HDPE')),
                  DropdownMenuItem(value: 'Other', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedMaterial = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _diameterController,
                decoration: const InputDecoration(
                  labelText: 'Diameter',
                  hintText: 'e.g., 100mm, 4 inches',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _widthController,
                decoration: const InputDecoration(
                  labelText: 'Width',
                  hintText: 'e.g., 150mm',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _heightController,
                decoration: const InputDecoration(
                  labelText: 'Height',
                  hintText: 'e.g., 100mm',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'Price Per Unit (NPR)',
                  hintText: 'e.g., 250.00',
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedManufacturer,
                decoration: const InputDecoration(
                  labelText: 'Manufacturer',
                ),
                items: const [
                  DropdownMenuItem(value: 'Generic', child: Text('Generic')),
                  DropdownMenuItem(
                      value: 'ABC Pipes', child: Text('ABC Pipes')),
                  DropdownMenuItem(
                      value: 'XYZ Fittings', child: Text('XYZ Fittings')),
                  DropdownMenuItem(
                      value: 'Local Manufacturer',
                      child: Text('Local Manufacturer')),
                  DropdownMenuItem(value: 'Other', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedManufacturer = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _modelNumberController,
                decoration: const InputDecoration(
                  labelText: 'Model Number',
                  hintText: 'e.g., T-100-S',
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPressureRating,
                decoration: const InputDecoration(
                  labelText: 'Pressure Rating',
                ),
                items: const [
                  DropdownMenuItem(
                      value: 'Standard (10 bar)',
                      child: Text('Standard (10 bar)')),
                  DropdownMenuItem(
                      value: 'Low (5 bar)', child: Text('Low (5 bar)')),
                  DropdownMenuItem(
                      value: 'Medium (15 bar)', child: Text('Medium (15 bar)')),
                  DropdownMenuItem(
                      value: 'High (25 bar)', child: Text('High (25 bar)')),
                  DropdownMenuItem(
                      value: 'Very High (40+ bar)',
                      child: Text('Very High (40+ bar)')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPressureRating = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Enter additional details',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _saveConnectorType,
                child: const Text('Save Connector Type'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
