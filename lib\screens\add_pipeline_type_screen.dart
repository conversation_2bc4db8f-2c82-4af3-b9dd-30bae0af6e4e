import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/hive_models/hive_pipeline_listing_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';

class AddPipelineTypeScreen extends ConsumerStatefulWidget {
  const AddPipelineTypeScreen({super.key});

  @override
  ConsumerState<AddPipelineTypeScreen> createState() =>
      _AddPipelineTypeScreenState();
}

class _AddPipelineTypeScreenState extends ConsumerState<AddPipelineTypeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  String _selectedPipeType = 'PVC';
  String _selectedPipeDiameter = '100mm';
  final _wallThicknessController = TextEditingController();
  String _selectedMaterial = 'PVC';
  String _selectedPressureRating = 'Standard (10 bar)';
  String _selectedTemperatureRating = 'Normal (-20°C to 60°C)';
  String _selectedManufacturer = 'Generic';
  final _standardController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  String _selectedState = 'Active';

  @override
  void dispose() {
    _titleController.dispose();
    _wallThicknessController.dispose();
    _standardController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _savePipelineType() async {
    if (_formKey.currentState!.validate()) {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Create properties map with all the details
      final properties = <String, dynamic>{
        'material': _selectedMaterial,
        'wallThickness': _wallThicknessController.text,
        'pressureRating': _selectedPressureRating,
        'temperatureRating': _selectedTemperatureRating,
        'manufacturer': _selectedManufacturer,
        'standard': _standardController.text,
        'description': _descriptionController.text,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      };

      // Parse price per meter if provided
      double? pricePerUnit;
      if (_priceController.text.isNotEmpty) {
        pricePerUnit = double.tryParse(_priceController.text);
      }

      // Create a new pipeline listing model
      final pipeline = HivePipelineListingModel.create(
        title: _titleController.text,
        pipeType: _selectedPipeType,
        pipeDiameter: _selectedPipeDiameter,
        state: _selectedState,
        properties: properties,
        pricePerUnit: pricePerUnit,
      );

      try {
        // Save to Hive
        await hiveService.savePipelineListingItem(pipeline);

        // Save to Firestore
        await firestoreRepository.addPipelineListingItem(pipeline.toJson());

        // Mark as synced in Hive
        pipeline.isSync = true;
        await hiveService.savePipelineListingItem(pipeline);

        // Check if widget is still mounted before using context
        if (!mounted) return;

        // Show success message and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'Pipeline type "${_titleController.text}" added successfully')),
        );

        Navigator.pop(context);
      } catch (e) {
        // Check if widget is still mounted before using context
        if (!mounted) return;

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving pipeline type: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Pipeline Type'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Pipeline Name *',
                  hintText: 'Enter pipeline name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPipeType,
                decoration: const InputDecoration(
                  labelText: 'Pipe Type *',
                ),
                items: const [
                  DropdownMenuItem(value: 'PVC', child: Text('PVC')),
                  DropdownMenuItem(value: 'Steel', child: Text('Steel')),
                  DropdownMenuItem(value: 'HDPE', child: Text('HDPE')),
                  DropdownMenuItem(
                      value: 'Cast Iron', child: Text('Cast Iron')),
                  DropdownMenuItem(value: 'Copper', child: Text('Copper')),
                  DropdownMenuItem(value: 'Concrete', child: Text('Concrete')),
                  DropdownMenuItem(value: 'Other', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPipeType = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a pipe type';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPipeDiameter,
                decoration: const InputDecoration(
                  labelText: 'Pipe Diameter *',
                ),
                items: const [
                  DropdownMenuItem(
                      value: '50mm', child: Text('50mm (2 inches)')),
                  DropdownMenuItem(
                      value: '75mm', child: Text('75mm (3 inches)')),
                  DropdownMenuItem(
                      value: '100mm', child: Text('100mm (4 inches)')),
                  DropdownMenuItem(
                      value: '150mm', child: Text('150mm (6 inches)')),
                  DropdownMenuItem(
                      value: '200mm', child: Text('200mm (8 inches)')),
                  DropdownMenuItem(
                      value: '250mm', child: Text('250mm (10 inches)')),
                  DropdownMenuItem(
                      value: '300mm', child: Text('300mm (12 inches)')),
                  DropdownMenuItem(value: 'Other', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPipeDiameter = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a pipe diameter';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _wallThicknessController,
                decoration: const InputDecoration(
                  labelText: 'Wall Thickness',
                  hintText: 'e.g., 5mm, 0.25 inches',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'Price Per Meter (NPR)',
                  hintText: 'e.g., 150.00',
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedMaterial,
                decoration: const InputDecoration(
                  labelText: 'Material',
                ),
                items: const [
                  DropdownMenuItem(value: 'PVC', child: Text('PVC')),
                  DropdownMenuItem(value: 'Steel', child: Text('Steel')),
                  DropdownMenuItem(
                      value: 'Cast Iron', child: Text('Cast Iron')),
                  DropdownMenuItem(
                      value: 'Ductile Iron', child: Text('Ductile Iron')),
                  DropdownMenuItem(value: 'HDPE', child: Text('HDPE')),
                  DropdownMenuItem(value: 'Copper', child: Text('Copper')),
                  DropdownMenuItem(value: 'Concrete', child: Text('Concrete')),
                  DropdownMenuItem(value: 'Other', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedMaterial = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPressureRating,
                decoration: const InputDecoration(
                  labelText: 'Pressure Rating',
                ),
                items: const [
                  DropdownMenuItem(
                      value: 'Standard (10 bar)',
                      child: Text('Standard (10 bar)')),
                  DropdownMenuItem(
                      value: 'Low (5 bar)', child: Text('Low (5 bar)')),
                  DropdownMenuItem(
                      value: 'Medium (15 bar)', child: Text('Medium (15 bar)')),
                  DropdownMenuItem(
                      value: 'High (25 bar)', child: Text('High (25 bar)')),
                  DropdownMenuItem(
                      value: 'Very High (40+ bar)',
                      child: Text('Very High (40+ bar)')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPressureRating = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedTemperatureRating,
                decoration: const InputDecoration(
                  labelText: 'Temperature Rating',
                ),
                items: const [
                  DropdownMenuItem(
                      value: 'Normal (-20°C to 60°C)',
                      child: Text('Normal (-20°C to 60°C)')),
                  DropdownMenuItem(
                      value: 'Low (-40°C to 40°C)',
                      child: Text('Low (-40°C to 40°C)')),
                  DropdownMenuItem(
                      value: 'High (0°C to 100°C)',
                      child: Text('High (0°C to 100°C)')),
                  DropdownMenuItem(
                      value: 'Very High (0°C to 200°C)',
                      child: Text('Very High (0°C to 200°C)')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedTemperatureRating = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedManufacturer,
                decoration: const InputDecoration(
                  labelText: 'Manufacturer',
                ),
                items: const [
                  DropdownMenuItem(value: 'Generic', child: Text('Generic')),
                  DropdownMenuItem(
                      value: 'ABC Pipes', child: Text('ABC Pipes')),
                  DropdownMenuItem(
                      value: 'XYZ Fittings', child: Text('XYZ Fittings')),
                  DropdownMenuItem(
                      value: 'Local Manufacturer',
                      child: Text('Local Manufacturer')),
                  DropdownMenuItem(value: 'Other', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedManufacturer = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _standardController,
                decoration: const InputDecoration(
                  labelText: 'Standard/Specification',
                  hintText: 'e.g., ASTM D1785, ISO 4427',
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedState,
                decoration: const InputDecoration(
                  labelText: 'Default State',
                ),
                items: const [
                  DropdownMenuItem(value: 'Active', child: Text('Active')),
                  DropdownMenuItem(value: 'Inactive', child: Text('Inactive')),
                  DropdownMenuItem(
                      value: 'Under Maintenance',
                      child: Text('Under Maintenance')),
                  DropdownMenuItem(value: 'Planned', child: Text('Planned')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedState = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Enter additional details',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _savePipelineType,
                child: const Text('Save Pipeline Type'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
