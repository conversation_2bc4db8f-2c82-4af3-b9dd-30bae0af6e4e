import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/hive_models/hive_smart_meter_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';
import '../utils/nepali_names.dart';

class AddSmartMeterScreen extends ConsumerStatefulWidget {
  const AddSmartMeterScreen({super.key});

  @override
  ConsumerState<AddSmartMeterScreen> createState() =>
      _AddSmartMeterScreenState();
}

class _AddSmartMeterScreenState extends ConsumerState<AddSmartMeterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _readingController = TextEditingController(text: '0');
  final _priceController = TextEditingController();
  String _selectedMeterType = 'Digital';
  String _selectedMeterSize = '15mm';
  String _selectedStatus = 'Active';

  @override
  void dispose() {
    _nameController.dispose();
    _readingController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  Future<void> _saveSmartMeter() async {
    if (_formKey.currentState!.validate()) {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Parse price per unit if provided
      double? pricePerUnit;
      if (_priceController.text.isNotEmpty) {
        pricePerUnit = double.tryParse(_priceController.text);
      }

      // Create a new smart meter model
      final smartMeter = HiveSmartMeterModel(
        id: const Uuid().v4(),
        latitude: 0, // Default values, will be updated when placed on map
        longitude: 0,
        title: _nameController.text,
        type: _selectedMeterType,
        reading: _readingController.text,
        status: _selectedStatus,
        areaId: 'default', // Default area, will be updated when placed on map
        pricePerUnit: pricePerUnit,
      );

      try {
        // Save to Hive
        await hiveService.saveSmartMeter(smartMeter);

        // Save to Firestore
        final specifications = {
          'type': smartMeter.type,
          'reading': smartMeter.reading,
        };
        await firestoreRepository.addSmartMeterDetails(
            smartMeter.id, specifications);

        // Mark as synced in Hive
        smartMeter.isSync = true;
        await hiveService.saveSmartMeter(smartMeter);

        if (!mounted) return;

        // Show success message
        _showSnackBar(
            'Smart meter "${_nameController.text}" added successfully');

        // Navigate back
        Navigator.pop(context, true); // Pass true to refresh the list
      } catch (e) {
        if (!mounted) return;
        _showSnackBar('Error adding smart meter: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Smart Meter'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Meter Name *',
                  hintText: 'Enter meter name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a meter name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedMeterType,
                decoration: const InputDecoration(
                  labelText: 'Meter Type *',
                ),
                items: const [
                  DropdownMenuItem(value: 'Analog', child: Text('Analog')),
                  DropdownMenuItem(value: 'Digital', child: Text('Digital')),
                  DropdownMenuItem(value: 'Prepaid', child: Text('Prepaid')),
                  DropdownMenuItem(value: 'Smart', child: Text('Smart')),
                  DropdownMenuItem(
                      value: 'Ultrasonic', child: Text('Ultrasonic')),
                  DropdownMenuItem(
                      value: 'Electromagnetic', child: Text('Electromagnetic')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedMeterType = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a meter type';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedMeterSize,
                decoration: const InputDecoration(
                  labelText: 'Meter Size *',
                ),
                items: const [
                  DropdownMenuItem(
                      value: '15mm', child: Text('15mm (1/2 inch)')),
                  DropdownMenuItem(
                      value: '20mm', child: Text('20mm (3/4 inch)')),
                  DropdownMenuItem(value: '25mm', child: Text('25mm (1 inch)')),
                  DropdownMenuItem(
                      value: '40mm', child: Text('40mm (1.5 inch)')),
                  DropdownMenuItem(value: '50mm', child: Text('50mm (2 inch)')),
                  DropdownMenuItem(value: '80mm', child: Text('80mm (3 inch)')),
                  DropdownMenuItem(
                      value: '100mm', child: Text('100mm (4 inch)')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedMeterSize = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a meter size';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _readingController,
                decoration: const InputDecoration(
                  labelText: 'Initial Reading *',
                  hintText: 'e.g., 0',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an initial reading';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'Price Per Unit (NPR)',
                  hintText: 'e.g., 10.50',
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                ),
                items: const [
                  DropdownMenuItem(value: 'Active', child: Text('Active')),
                  DropdownMenuItem(value: 'Inactive', child: Text('Inactive')),
                  DropdownMenuItem(
                      value: 'Maintenance', child: Text('Maintenance')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _saveSmartMeter,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                ),
                child: const Text('Add Smart Meter'),
              ),
              const SizedBox(height: 16),
              const Text(
                'Note: This will add the meter to the database. You can later add it to the map from the map screen.',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
