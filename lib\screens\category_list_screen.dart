import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/hive_models/hive_category_model.dart';
import '../utils/nepali_names.dart';
import '../providers/hive_service_provider.dart';
import '../providers/sync_service_provider.dart';
import 'add_category_screen.dart';

class CategoryListScreen extends ConsumerStatefulWidget {
  const CategoryListScreen({super.key});

  @override
  ConsumerState<CategoryListScreen> createState() => _CategoryListScreenState();
}

class _CategoryListScreenState extends ConsumerState<CategoryListScreen> {
  bool _isLoading = false;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    // Check if we need to fetch data from server
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndFetchData();
    });
  }

  Future<void> _checkAndFetchData() async {
    final hiveService = ref.read(hiveServiceProvider);
    final syncService = ref.read(syncServiceProvider);

    // Check if categories list is empty
    final categories = hiveService.getAllCategories();

    if (categories.isEmpty) {
      setState(() {
        _isLoading = true;
      });

      // Fetch data from server
      try {
        await syncService.fetchCategories();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error fetching categories: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _syncWithServer() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = ref.read(syncServiceProvider);
      await syncService.syncCategories();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Categories synced successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error syncing categories: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  void _navigateToAddCategory() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddCategoryScreen(),
      ),
    ).then((_) {
      // Refresh the UI when returning from the add screen
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final hiveService = ref.watch(hiveServiceProvider);
    final categories = hiveService.getAllCategories();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Asset Categories'),
        actions: [
          // Add button
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Category',
            onPressed: _navigateToAddCategory,
          ),
          // Sync button
          IconButton(
            icon: _isSyncing
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.sync),
            tooltip: 'Sync with server',
            onPressed: _isSyncing ? null : _syncWithServer,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              setState(() {});
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Fetching categories from server...'),
                ],
              ),
            )
          : categories.isEmpty
              ? const Center(
                  child: Text('No categories found'),
                )
              : ListView.builder(
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    
                    // Determine icon based on asset type
                    IconData iconData;
                    switch (category.assetType) {
                      case 'valve':
                        iconData = Icons.plumbing;
                        break;
                      case 'connector':
                        iconData = Icons.device_hub;
                        break;
                      case 'pipeline':
                        iconData = Icons.linear_scale;
                        break;
                      case 'smartMeter':
                        iconData = Icons.speed;
                        break;
                      default:
                        iconData = Icons.category;
                    }

                    return ListTile(
                      leading: Icon(iconData),
                      title: Text(category.name),
                      subtitle: Text(category.description),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // TODO: Navigate to category details screen
                      },
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddCategory,
        tooltip: 'Add Category',
        child: const Icon(Icons.add),
      ),
    );
  }
}
