import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/hive_models/hive_connector_listing_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';
import '../utils/nepali_names.dart';

class ConnectorDetailsScreen extends ConsumerStatefulWidget {
  final HiveConnectorListingModel connector;

  const ConnectorDetailsScreen({
    super.key,
    required this.connector,
  });

  @override
  ConsumerState<ConnectorDetailsScreen> createState() =>
      _ConnectorDetailsScreenState();
}

class _ConnectorDetailsScreenState
    extends ConsumerState<ConnectorDetailsScreen> {
  late TextEditingController _titleController;
  late String _selectedConnectorType;
  late String _selectedMaterial;
  late TextEditingController _diameterController;
  late TextEditingController _widthController;
  late TextEditingController _heightController;
  late String _selectedManufacturer;
  late TextEditingController _modelNumberController;
  late String _selectedPressureRating;
  late TextEditingController _descriptionController;
  late TextEditingController _priceController;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final connector = widget.connector;
    final specs = connector.specifications;

    _titleController = TextEditingController(text: connector.title);
    _selectedConnectorType = connector.connectorType;
    _selectedMaterial = specs['material'] ?? 'PVC';
    _diameterController = TextEditingController(text: specs['diameter'] ?? '');
    _widthController = TextEditingController(text: specs['width'] ?? '');
    _heightController = TextEditingController(text: specs['height'] ?? '');
    _selectedManufacturer = specs['manufacturer'] ?? 'Generic';
    _modelNumberController =
        TextEditingController(text: specs['modelNumber'] ?? '');
    _selectedPressureRating = specs['pressureRating'] ?? 'Standard (10 bar)';
    _descriptionController =
        TextEditingController(text: specs['description'] ?? '');
    _priceController = TextEditingController(
      text: connector.pricePerUnit?.toString() ?? '',
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _diameterController.dispose();
    _widthController.dispose();
    _heightController.dispose();
    _modelNumberController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  Future<void> _saveChanges() async {
    final hiveService = ref.read(hiveServiceProvider);
    final firestoreRepository = ref.read(firestoreRepositoryProvider);

    // Create updated specifications map
    final specifications = <String, dynamic>{
      'material': _selectedMaterial,
      'diameter': _diameterController.text,
      'width': _widthController.text,
      'height': _heightController.text,
      'description': _descriptionController.text,
      'manufacturer': _selectedManufacturer,
      'modelNumber': _modelNumberController.text,
      'pressureRating': _selectedPressureRating,
      'createdAt': widget.connector.specifications['createdAt'] ??
          DateTime.now().millisecondsSinceEpoch,
      'updatedAt': DateTime.now().millisecondsSinceEpoch,
    };

    // Parse price per unit if provided
    double? pricePerUnit;
    if (_priceController.text.isNotEmpty) {
      pricePerUnit = double.tryParse(_priceController.text);
    }

    // Update the connector
    final updatedConnector = HiveConnectorListingModel(
      id: widget.connector.id,
      title: _titleController.text,
      connectorType: _selectedConnectorType,
      specifications: specifications,
      isSync: false,
      isDeleted: false,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
      pricePerUnit: pricePerUnit,
    );

    try {
      // Save to Hive
      await hiveService.saveConnectorListingItem(updatedConnector);

      // Save to Firestore
      await firestoreRepository.updateConnectorListingItem(
          updatedConnector.id, updatedConnector.toJson());

      // Mark as synced in Hive
      updatedConnector.isSync = true;
      await hiveService.saveConnectorListingItem(updatedConnector);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                'Connector "${_titleController.text}" updated successfully')),
      );

      // Exit edit mode
      setState(() {
        _isEditing = false;
      });
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating connector: $e')),
      );
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  Future<void> _syncConnector() async {
    final firestoreRepository = ref.read(firestoreRepositoryProvider);
    final hiveService = ref.read(hiveServiceProvider);

    // Save to Firestore
    await firestoreRepository
        .addConnectorListingItem(widget.connector.toJson());

    // Mark as synced in Hive
    widget.connector.isSync = true;
    await hiveService.saveConnectorListingItem(widget.connector);
  }

  Future<void> _deleteConnector() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Connector'),
        content: Text(
            'Are you sure you want to delete "${widget.connector.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    try {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Mark as deleted in Hive
      final connector = widget.connector;
      connector.isDeleted = true;
      connector.isSync = false;
      await hiveService.saveConnectorListingItem(connector);

      // Delete from Firestore
      await firestoreRepository.deleteElement(connector.id);

      // Permanently delete from Hive
      await hiveService.deleteConnectorListingItem(connector.id,
          permanent: true);

      // Show success message
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content:
                Text('Connector "${connector.title}" deleted successfully')),
      );

      // Navigate back
      Navigator.pop(context, true); // Pass true to indicate deletion
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting connector: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final connector = widget.connector;

    // Determine icon based on connector type
    IconData iconData;
    String typeText;

    switch (connector.connectorType) {
      case 'tee':
        iconData = Icons.device_hub;
        typeText = 'Tee Connector';
        break;
      case 'elbow':
        iconData = Icons.turn_right;
        typeText = 'Elbow Connector';
        break;
      case 'coupling':
        iconData = Icons.link;
        typeText = 'Coupling Connector';
        break;
      case 'cross':
        iconData = Icons.add;
        typeText = 'Cross Connector';
        break;
      default:
        iconData = Icons.plumbing;
        typeText = 'Connector';
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Connector' : 'Connector Details'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            onPressed: _isEditing ? _saveChanges : _toggleEditMode,
          ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteConnector,
            ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                setState(() {
                  // Refresh the data
                  final hiveService = ref.read(hiveServiceProvider);
                  final connectors = hiveService.getAllConnectorListingItems();
                  final updatedConnector = connectors.firstWhere(
                    (c) => c.id == widget.connector.id,
                    orElse: () => widget.connector,
                  );

                  if (updatedConnector != widget.connector) {
                    // Update the widget's connector with the latest data
                    widget.connector.title = updatedConnector.title;
                    widget.connector.connectorType =
                        updatedConnector.connectorType;
                    widget.connector.specifications =
                        updatedConnector.specifications;
                    widget.connector.isSync = updatedConnector.isSync;
                    widget.connector.updatedAt = updatedConnector.updatedAt;
                  }
                });
              },
            ),
          if (!_isEditing)
            IconButton(
              icon: Icon(
                widget.connector.isSync ? Icons.cloud_done : Icons.cloud_upload,
                color: widget.connector.isSync ? Colors.green : Colors.orange,
              ),
              onPressed: () {
                if (widget.connector.isSync) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Connector is already synced')),
                  );
                  return;
                }

                // Use a separate method to handle the async operations
                _syncConnector().then((_) {
                  if (mounted) {
                    setState(() {}); // Refresh UI
                    _showSnackBar('Connector synced successfully');
                  }
                }).catchError((e) {
                  if (mounted) {
                    _showSnackBar('Error syncing connector: $e', isError: true);
                  }
                });
              },
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: _isEditing
            ? _buildEditForm()
            : _buildDetailsView(iconData, typeText),
      ),
    );
  }

  Widget _buildDetailsView(IconData iconData, String typeText) {
    final connector = widget.connector;
    final specs = connector.specifications;

    // Get a Nepali name for the connector
    final displayName = NepaliFriendlyNames.getNameByType(
        connector.connectorType, connector.title);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: ListTile(
            leading: Icon(iconData, size: 40),
            title: Text(displayName,
                style: Theme.of(context).textTheme.titleLarge),
            subtitle: Text(typeText),
          ),
        ),
        const SizedBox(height: 16),
        _buildDetailItem('Material', specs['material'] ?? 'Not specified'),
        _buildDetailItem('Diameter', specs['diameter'] ?? 'Not specified'),
        _buildDetailItem('Width', specs['width'] ?? 'Not specified'),
        _buildDetailItem('Height', specs['height'] ?? 'Not specified'),
        _buildDetailItem(
            'Price Per Unit',
            connector.pricePerUnit != null
                ? '${connector.pricePerUnit!.toStringAsFixed(2)} NPR'
                : 'Not specified'),
        _buildDetailItem(
            'Manufacturer', specs['manufacturer'] ?? 'Not specified'),
        _buildDetailItem(
            'Model Number', specs['modelNumber'] ?? 'Not specified'),
        _buildDetailItem(
            'Pressure Rating', specs['pressureRating'] ?? 'Not specified'),
        _buildDetailItem(
            'Description', specs['description'] ?? 'Not specified'),
        _buildDetailItem('ID', connector.id),
        _buildDetailItem('Created At', _formatTimestamp(specs['createdAt'])),
        if (specs['updatedAt'] != null)
          _buildDetailItem('Updated At', _formatTimestamp(specs['updatedAt'])),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
          const Divider(),
        ],
      ),
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Not available';

    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp as int);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  Widget _buildEditForm() {
    return Form(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Connector Name *',
              hintText: 'Enter connector name',
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedConnectorType,
            decoration: const InputDecoration(
              labelText: 'Connector Type *',
            ),
            items: const [
              DropdownMenuItem(value: 'tee', child: Text('Tee Connector')),
              DropdownMenuItem(value: 'elbow', child: Text('Elbow Connector')),
              DropdownMenuItem(
                  value: 'coupling', child: Text('Coupling Connector')),
              DropdownMenuItem(value: 'cross', child: Text('Cross Connector')),
              DropdownMenuItem(
                  value: 'socket', child: Text('Coupling/Socket Connector')),
              DropdownMenuItem(
                  value: 'reducer',
                  child: Text('Reducer(Concentric/Eccentric)')),
              DropdownMenuItem(value: 'cap', child: Text('Cap/Plug')),
              DropdownMenuItem(value: 'union', child: Text('Union')),
              DropdownMenuItem(value: 'adapter', child: Text('Adapter')),
              DropdownMenuItem(value: 'nipple', child: Text('Nipple')),
              DropdownMenuItem(value: 'flange', child: Text('Flange')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedConnectorType = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedMaterial,
            decoration: const InputDecoration(
              labelText: 'Material',
            ),
            items: const [
              DropdownMenuItem(value: 'PVC', child: Text('PVC')),
              DropdownMenuItem(value: 'Steel', child: Text('Steel')),
              DropdownMenuItem(value: 'Cast Iron', child: Text('Cast Iron')),
              DropdownMenuItem(value: 'Brass', child: Text('Brass')),
              DropdownMenuItem(value: 'Copper', child: Text('Copper')),
              DropdownMenuItem(value: 'HDPE', child: Text('HDPE')),
              DropdownMenuItem(value: 'Other', child: Text('Other')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedMaterial = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _diameterController,
            decoration: const InputDecoration(
              labelText: 'Diameter',
              hintText: 'e.g., 100mm, 4 inches',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _widthController,
            decoration: const InputDecoration(
              labelText: 'Width',
              hintText: 'e.g., 150mm',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _heightController,
            decoration: const InputDecoration(
              labelText: 'Height',
              hintText: 'e.g., 100mm',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _priceController,
            decoration: const InputDecoration(
              labelText: 'Price Per Unit (NPR)',
              hintText: 'e.g., 250.00',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedManufacturer,
            decoration: const InputDecoration(
              labelText: 'Manufacturer',
            ),
            items: const [
              DropdownMenuItem(value: 'Generic', child: Text('Generic')),
              DropdownMenuItem(value: 'ABC Pipes', child: Text('ABC Pipes')),
              DropdownMenuItem(
                  value: 'XYZ Fittings', child: Text('XYZ Fittings')),
              DropdownMenuItem(
                  value: 'Local Manufacturer',
                  child: Text('Local Manufacturer')),
              DropdownMenuItem(value: 'Other', child: Text('Other')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedManufacturer = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _modelNumberController,
            decoration: const InputDecoration(
              labelText: 'Model Number',
              hintText: 'e.g., T-100-S',
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedPressureRating,
            decoration: const InputDecoration(
              labelText: 'Pressure Rating',
            ),
            items: const [
              DropdownMenuItem(
                  value: 'Standard (10 bar)', child: Text('Standard (10 bar)')),
              DropdownMenuItem(
                  value: 'Low (5 bar)', child: Text('Low (5 bar)')),
              DropdownMenuItem(
                  value: 'Medium (15 bar)', child: Text('Medium (15 bar)')),
              DropdownMenuItem(
                  value: 'High (25 bar)', child: Text('High (25 bar)')),
              DropdownMenuItem(
                  value: 'Very High (40+ bar)',
                  child: Text('Very High (40+ bar)')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedPressureRating = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Enter additional details',
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                    _initializeControllers(); // Reset to original values
                  });
                },
                icon: const Icon(Icons.cancel),
                label: const Text('Cancel'),
              ),
              ElevatedButton.icon(
                onPressed: _saveChanges,
                icon: const Icon(Icons.save),
                label: const Text('Save Changes'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
