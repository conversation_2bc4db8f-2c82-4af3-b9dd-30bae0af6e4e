import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/pipeline.dart';
import '../models/hive_models/hive_connector_listing_model.dart';
import '../models/hive_models/hive_pipeline_listing_model.dart';
import '../home/<USER>';
import '../utils/nepali_names.dart';
import '../providers/hive_service_provider.dart';
import '../providers/sync_service_provider.dart';
import '../services/sync_service.dart';
import 'add_connector_type_screen.dart';
import 'add_pipeline_type_screen.dart';
import 'connector_details_screen.dart';
import 'pipeline_details_screen.dart';

class ConnectorListScreen extends ConsumerStatefulWidget {
  const ConnectorListScreen({super.key});

  @override
  ConsumerState<ConnectorListScreen> createState() =>
      _ConnectorListScreenState();
}

class _ConnectorListScreenState extends ConsumerState<ConnectorListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Check if we need to fetch data from server
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndFetchData();
    });
  }

  Future<void> _checkAndFetchData() async {
    final hiveService = ref.read(hiveServiceProvider);
    final syncService = ref.read(syncServiceProvider);

    // Check if connector and pipeline lists are empty
    final connectors = hiveService.getAllConnectorListingItems();
    final pipelines = hiveService.getAllPipelineListingItems();

    if (connectors.isEmpty || pipelines.isEmpty) {
      setState(() {
        _isLoading = true;
      });

      // Fetch data from server
      if (connectors.isEmpty) {
        await syncService.fetchConnectorListing();
      }

      if (pipelines.isEmpty) {
        await syncService.fetchPipelineListing();
      }

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _syncWithServer() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = ref.read(syncServiceProvider);

      // Sync based on current tab
      if (_tabController.index == 0) {
        // Sync connectors
        await syncService.syncConnectorListing();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Connectors synced successfully')),
          );
        }
      } else {
        // Sync pipelines
        await syncService.syncPipelineListing();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Pipelines synced successfully')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error syncing data: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Show options to add connector or pipeline
  void _showAddOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.device_hub),
            title: const Text('Add Connector Type'),
            onTap: () {
              Navigator.pop(context);
              _navigateToAddConnectorScreen();
            },
          ),
          ListTile(
            leading: const Icon(Icons.linear_scale),
            title: const Text('Add Pipeline Type'),
            onTap: () {
              Navigator.pop(context);
              _navigateToAddPipelineScreen();
            },
          ),
        ],
      ),
    );
  }

  // Navigate to the add connector type screen
  void _navigateToAddConnectorScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddConnectorTypeScreen(),
      ),
    ).then((_) {
      // Refresh the UI when returning from the add screen
      setState(() {});
    });
  }

  // Navigate to the add pipeline type screen
  void _navigateToAddPipelineScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddPipelineTypeScreen(),
      ),
    ).then((_) {
      // Refresh the UI when returning from the add screen
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final hiveService = ref.watch(hiveServiceProvider);

    // Get connectors from connector_listing box
    final connectorListingItems = hiveService.getAllConnectorListingItems();

    // Get pipelines from pipeline_listing box
    final pipelineListingItems = hiveService.getAllPipelineListingItems();

    // We don't need map elements since we're just showing the listing

    return Scaffold(
      appBar: AppBar(
        title: const Text(NepaliFriendlyNames.connectorListTitle),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'कनेक्टर'),
            Tab(text: 'पाइपलाइन'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add',
            onPressed: () {
              _showAddOptions(context);
            },
          ),
          // Sync button
          IconButton(
            icon: _isSyncing
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.sync),
            tooltip: 'Sync with server',
            onPressed: _isSyncing ? null : _syncWithServer,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              // Reload data from local database
              final mapNotifier = ref.read(mapStateProvider.notifier);
              mapNotifier.loadInitialData();
              setState(() {});
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Fetching data from server...'),
                ],
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                // Connectors Tab
                connectorListingItems.isEmpty
                    ? const Center(
                        child: Text('No connectors found'),
                      )
                    : ListView.builder(
                        itemCount: connectorListingItems.length,
                        itemBuilder: (context, index) {
                          final connector = connectorListingItems[index];

                          // Determine icon based on connector type
                          IconData iconData;
                          String typeText;
                          String connectorType;

                          // Determine icon based on connector type string
                          switch (connector.connectorType) {
                            case 'tee':
                            case 'threeConnector':
                              iconData = Icons.device_hub;
                              typeText = 'तीन-मार्गे जोडने';
                              connectorType = 'threeConnector';
                              break;
                            case 'cross':
                            case 'fourConnector':
                              iconData = Icons.hub;
                              typeText = 'चार-मार्गे जोडने';
                              connectorType = 'fourConnector';
                              break;
                            case 'elbow':
                              iconData = Icons.turn_right;
                              typeText = 'कोण जोडने';
                              connectorType = 'elbow';
                              break;
                            case 'coupling':
                              iconData = Icons.linear_scale;
                              typeText = 'सिधा जोडने';
                              connectorType = 'coupling';
                              break;
                            default:
                              iconData = Icons.device_hub;
                              typeText = 'जोडने';
                              connectorType = 'connector';
                          }

                          // Get a Nepali name for the connector
                          final displayName = connector.title;

                          // Get specifications
                          final specs = connector.specifications;
                          final material = specs['material'] ?? 'N/A';
                          final diameter = specs['diameter'] ?? 'N/A';
                          final width = specs['width'] ?? 'N/A';

                          return ListTile(
                            leading: Icon(iconData),
                            title: Text(displayName),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    'Material: $material | Diameter: $diameter mm | Width: $width'),
                              ],
                            ),
                            isThreeLine: true,
                            trailing:
                                const Icon(Icons.arrow_forward_ios, size: 16),
                            onTap: () {
                              // Navigate to connector details screen
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ConnectorDetailsScreen(
                                    connector: connector,
                                  ),
                                ),
                              ).then((deleted) {
                                // Refresh the UI when returning from the details screen
                                if (deleted == true) {
                                  setState(() {});
                                }
                              });
                            },
                          );
                        },
                      ),
                // Pipelines Tab
                pipelineListingItems.isEmpty
                    ? const Center(
                        child: Text('No pipelines found'),
                      )
                    : ListView.builder(
                        itemCount: pipelineListingItems.length,
                        itemBuilder: (context, index) {
                          final pipeline = pipelineListingItems[index];

                          // Get the pipeline state for color
                          final pipelineState = pipeline.state;
                          final stateColor =
                              Pipeline.getColorForState(pipelineState);

                          return ListTile(
                            leading:
                                Icon(Icons.linear_scale, color: stateColor),
                            title: Text('Name: ${pipeline.title}'),
                            subtitle: Text(
                                'State: $pipelineState | ID: ${pipeline.id.substring(0, 8)}...'),
                            trailing:
                                const Icon(Icons.arrow_forward_ios, size: 16),
                            onTap: () {
                              // Navigate to pipeline details screen
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PipelineDetailsScreen(
                                    pipeline: pipeline,
                                  ),
                                ),
                              ).then((deleted) {
                                // Refresh the UI when returning from the details screen
                                if (deleted == true) {
                                  setState(() {});
                                }
                              });
                            },
                          );
                        },
                      ),
              ],
            ),
    );
  }
}
