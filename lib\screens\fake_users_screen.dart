import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/fake_user_model.dart';
import '../providers/fake_users_provider.dart';

class FakeUsersScreen extends ConsumerStatefulWidget {
  const FakeUsersScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<FakeUsersScreen> createState() => _FakeUsersScreenState();
}

class _FakeUsersScreenState extends ConsumerState<FakeUsersScreen> {
  bool _isLoading = false;
  bool _isSaving = false;
  bool _isSavingToUserCollection = false;

  @override
  void initState() {
    super.initState();
    _loadFakeUsers();
  }

  Future<void> _loadFakeUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(fakeUsersProvider.notifier).loadFakeUsersFromFirestore();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading users: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveFakeUsers() async {
    setState(() {
      _isSaving = true;
    });

    try {
      await ref.read(fakeUsersProvider.notifier).saveFakeUsersToFirestore();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Users saved to server successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving users: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _saveAllUsersToUserCollection() async {
    setState(() {
      _isSavingToUserCollection = true;
    });

    try {
      // Use 'default' as the area ID for now
      await ref
          .read(fakeUsersProvider.notifier)
          .saveAllUsersToUserCollection('default');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('All users saved to user collection successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving users to user collection: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSavingToUserCollection = false;
        });
      }
    }
  }

  Future<void> _regenerateUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      ref.read(fakeUsersProvider.notifier).generateFakeUsers();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final fakeUsers = ref.watch(fakeUsersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('नेपाली प्रयोगकर्ताहरू'),
        actions: [
          // Regenerate users button
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Regenerate Users',
            onPressed: _isLoading || _isSaving || _isSavingToUserCollection
                ? null
                : _regenerateUsers,
          ),
          // Save to server button
          IconButton(
            icon: _isSaving
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.cloud_upload),
            tooltip: 'Save to Fake Users Collection',
            onPressed: _isLoading || _isSaving || _isSavingToUserCollection
                ? null
                : _saveFakeUsers,
          ),
          // Save all to user collection button
          IconButton(
            icon: _isSavingToUserCollection
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.people),
            tooltip: 'Save All to User Collection',
            onPressed: _isLoading || _isSaving || _isSavingToUserCollection
                ? null
                : _saveAllUsersToUserCollection,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Main content - user list or empty state
                Expanded(
                  child: fakeUsers.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text('No users found'),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _regenerateUsers,
                                child: const Text('Generate Users'),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          itemCount: fakeUsers.length,
                          itemBuilder: (context, index) {
                            final user = fakeUsers[index];
                            return _buildUserListItem(user);
                          },
                        ),
                ),

                // Bottom action buttons
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isSavingToUserCollection
                              ? null
                              : _saveAllUsersToUserCollection,
                          icon: _isSavingToUserCollection
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(Icons.people),
                          label:
                              const Text('Save All Users to User Collection'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildUserListItem(FakeUser user) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          child: Text(user.name.substring(0, 1)),
        ),
        title: Text(user.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Phone: ${user.contact}'),
            Text('Address: ${user.address}',
                maxLines: 1, overflow: TextOverflow.ellipsis),
            if (user.notes.isNotEmpty)
              Text('Notes: ${user.notes}',
                  maxLines: 1, overflow: TextOverflow.ellipsis),
          ],
        ),
        isThreeLine: true,
        trailing: Checkbox(
          value: user.isSelected,
          onChanged: (value) {
            ref.read(fakeUsersProvider.notifier).toggleUserSelection(user.id);
          },
        ),
        onTap: () {
          ref.read(fakeUsersProvider.notifier).toggleUserSelection(user.id);
        },
      ),
    );
  }
}
