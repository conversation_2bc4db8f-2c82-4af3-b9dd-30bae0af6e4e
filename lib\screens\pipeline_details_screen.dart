import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/hive_models/hive_pipeline_listing_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';
import '../utils/nepali_names.dart';

class PipelineDetailsScreen extends ConsumerStatefulWidget {
  final HivePipelineListingModel pipeline;

  const PipelineDetailsScreen({
    super.key,
    required this.pipeline,
  });

  @override
  ConsumerState<PipelineDetailsScreen> createState() =>
      _PipelineDetailsScreenState();
}

class _PipelineDetailsScreenState extends ConsumerState<PipelineDetailsScreen> {
  late TextEditingController _titleController;
  late String _selectedPipeType;
  late String _selectedPipeDiameter;
  late TextEditingController _wallThicknessController;
  late String _selectedMaterial;
  late String _selectedPressureRating;
  late String _selectedTemperatureRating;
  late String _selectedManufacturer;
  late TextEditingController _standardController;
  late TextEditingController _descriptionController;
  late TextEditingController _priceController;
  late String _selectedState;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final pipeline = widget.pipeline;
    final props = pipeline.properties;

    _titleController = TextEditingController(text: pipeline.title);
    _selectedPipeType = pipeline.pipeType;
    _selectedPipeDiameter = pipeline.pipeDiameter;
    _wallThicknessController =
        TextEditingController(text: props['wallThickness'] ?? '');
    _selectedMaterial = props['material'] ?? 'PVC';
    _selectedPressureRating = props['pressureRating'] ?? 'Standard (10 bar)';
    _selectedTemperatureRating =
        props['temperatureRating'] ?? 'Normal (-20°C to 60°C)';
    _selectedManufacturer = props['manufacturer'] ?? 'Generic';
    _standardController = TextEditingController(text: props['standard'] ?? '');
    _descriptionController =
        TextEditingController(text: props['description'] ?? '');
    _priceController = TextEditingController(
      text: pipeline.pricePerUnit?.toString() ?? '',
    );
    _selectedState = pipeline.state;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _wallThicknessController.dispose();
    _standardController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  Future<void> _saveChanges() async {
    final hiveService = ref.read(hiveServiceProvider);
    final firestoreRepository = ref.read(firestoreRepositoryProvider);

    // Create updated properties map
    final properties = <String, dynamic>{
      'material': _selectedMaterial,
      'wallThickness': _wallThicknessController.text,
      'pressureRating': _selectedPressureRating,
      'temperatureRating': _selectedTemperatureRating,
      'manufacturer': _selectedManufacturer,
      'standard': _standardController.text,
      'description': _descriptionController.text,
      'createdAt': widget.pipeline.properties['createdAt'] ??
          DateTime.now().millisecondsSinceEpoch,
      'updatedAt': DateTime.now().millisecondsSinceEpoch,
    };

    // Parse price per meter if provided
    double? pricePerUnit;
    if (_priceController.text.isNotEmpty) {
      pricePerUnit = double.tryParse(_priceController.text);
    }

    // Update the pipeline
    final updatedPipeline = HivePipelineListingModel(
      id: widget.pipeline.id,
      title: _titleController.text,
      pipeType: _selectedPipeType,
      pipeDiameter: _selectedPipeDiameter,
      state: _selectedState,
      properties: properties,
      isSync: false,
      isDeleted: false,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
      pricePerUnit: pricePerUnit,
    );

    try {
      // Save to Hive
      await hiveService.savePipelineListingItem(updatedPipeline);

      // Save to Firestore
      await firestoreRepository.updatePipelineListingItem(
          updatedPipeline.id, updatedPipeline.toJson());

      // Mark as synced in Hive
      updatedPipeline.isSync = true;
      await hiveService.savePipelineListingItem(updatedPipeline);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                'Pipeline "${_titleController.text}" updated successfully')),
      );

      // Exit edit mode
      setState(() {
        _isEditing = false;
      });
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating pipeline: $e')),
      );
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  Future<void> _syncPipeline() async {
    final firestoreRepository = ref.read(firestoreRepositoryProvider);
    final hiveService = ref.read(hiveServiceProvider);

    // Save to Firestore
    await firestoreRepository.addPipelineListingItem(widget.pipeline.toJson());

    // Mark as synced in Hive
    widget.pipeline.isSync = true;
    await hiveService.savePipelineListingItem(widget.pipeline);
  }

  Future<void> _deletePipeline() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Pipeline'),
        content:
            Text('Are you sure you want to delete "${widget.pipeline.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    try {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Mark as deleted in Hive
      final pipeline = widget.pipeline;
      pipeline.isDeleted = true;
      pipeline.isSync = false;
      await hiveService.savePipelineListingItem(pipeline);

      // Delete from Firestore
      await firestoreRepository.deleteElement(pipeline.id);

      // Permanently delete from Hive
      await hiveService.deletePipelineListingItem(pipeline.id, permanent: true);

      // Show success message
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Pipeline "${pipeline.title}" deleted successfully')),
      );

      // Navigate back
      Navigator.pop(context, true); // Pass true to indicate deletion
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting pipeline: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Pipeline' : 'Pipeline Details'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            onPressed: _isEditing ? _saveChanges : _toggleEditMode,
          ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deletePipeline,
            ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                setState(() {
                  // Refresh the data
                  final hiveService = ref.read(hiveServiceProvider);
                  final pipelines = hiveService.getAllPipelineListingItems();
                  final updatedPipeline = pipelines.firstWhere(
                    (p) => p.id == widget.pipeline.id,
                    orElse: () => widget.pipeline,
                  );

                  if (updatedPipeline != widget.pipeline) {
                    // Update the widget's pipeline with the latest data
                    widget.pipeline.title = updatedPipeline.title;
                    widget.pipeline.pipeType = updatedPipeline.pipeType;
                    widget.pipeline.pipeDiameter = updatedPipeline.pipeDiameter;
                    widget.pipeline.state = updatedPipeline.state;
                    widget.pipeline.properties = updatedPipeline.properties;
                    widget.pipeline.isSync = updatedPipeline.isSync;
                    widget.pipeline.updatedAt = updatedPipeline.updatedAt;
                  }
                });
              },
            ),
          if (!_isEditing)
            IconButton(
              icon: Icon(
                widget.pipeline.isSync ? Icons.cloud_done : Icons.cloud_upload,
                color: widget.pipeline.isSync ? Colors.green : Colors.orange,
              ),
              onPressed: () {
                if (widget.pipeline.isSync) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Pipeline is already synced')),
                  );
                  return;
                }

                // Use a separate method to handle the async operations
                _syncPipeline().then((_) {
                  if (mounted) {
                    setState(() {}); // Refresh UI
                    _showSnackBar('Pipeline synced successfully');
                  }
                }).catchError((e) {
                  if (mounted) {
                    _showSnackBar('Error syncing pipeline: $e', isError: true);
                  }
                });
              },
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: _isEditing ? _buildEditForm() : _buildDetailsView(),
      ),
    );
  }

  Widget _buildDetailsView() {
    final pipeline = widget.pipeline;
    final props = pipeline.properties;

    // Determine color based on state
    Color stateColor;
    switch (pipeline.state) {
      case 'Active':
        stateColor = Colors.green;
        break;
      case 'Inactive':
        stateColor = Colors.grey;
        break;
      case 'Under Maintenance':
        stateColor = Colors.orange;
        break;
      case 'Planned':
        stateColor = Colors.blue;
        break;
      default:
        stateColor = Colors.black;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: ListTile(
            leading: Icon(Icons.linear_scale, size: 40, color: stateColor),
            title: Text(pipeline.title,
                style: Theme.of(context).textTheme.titleLarge),
            subtitle: Text('${pipeline.pipeType} | ${pipeline.pipeDiameter}'),
            trailing: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: stateColor.withAlpha(51), // 0.2 * 255 = 51
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                pipeline.state,
                style:
                    TextStyle(color: stateColor, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        _buildDetailItem('Pipe Type', pipeline.pipeType),
        _buildDetailItem('Pipe Diameter', pipeline.pipeDiameter),
        _buildDetailItem('Material', props['material'] ?? 'Not specified'),
        _buildDetailItem(
            'Wall Thickness', props['wallThickness'] ?? 'Not specified'),
        _buildDetailItem(
            'Price Per Meter',
            pipeline.pricePerUnit != null
                ? '${pipeline.pricePerUnit!.toStringAsFixed(2)} NPR'
                : 'Not specified'),
        _buildDetailItem(
            'Pressure Rating', props['pressureRating'] ?? 'Not specified'),
        _buildDetailItem('Temperature Rating',
            props['temperatureRating'] ?? 'Not specified'),
        _buildDetailItem(
            'Manufacturer', props['manufacturer'] ?? 'Not specified'),
        _buildDetailItem('Standard', props['standard'] ?? 'Not specified'),
        _buildDetailItem(
            'Description', props['description'] ?? 'Not specified'),
        _buildDetailItem('ID', pipeline.id),
        _buildDetailItem('Created At', _formatTimestamp(props['createdAt'])),
        if (props['updatedAt'] != null)
          _buildDetailItem('Updated At', _formatTimestamp(props['updatedAt'])),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
          const Divider(),
        ],
      ),
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Not available';

    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp as int);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  Widget _buildEditForm() {
    return Form(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Pipeline Name *',
              hintText: 'Enter pipeline name',
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedPipeType,
            decoration: const InputDecoration(
              labelText: 'Pipe Type *',
            ),
            items: const [
              DropdownMenuItem(value: 'PVC', child: Text('PVC')),
              DropdownMenuItem(value: 'Steel', child: Text('Steel')),
              DropdownMenuItem(value: 'HDPE', child: Text('HDPE')),
              DropdownMenuItem(value: 'Cast Iron', child: Text('Cast Iron')),
              DropdownMenuItem(value: 'Copper', child: Text('Copper')),
              DropdownMenuItem(value: 'Concrete', child: Text('Concrete')),
              DropdownMenuItem(value: 'Other', child: Text('Other')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedPipeType = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedPipeDiameter,
            decoration: const InputDecoration(
              labelText: 'Pipe Diameter *',
            ),
            items: const [
              DropdownMenuItem(value: '50mm', child: Text('50mm (2 inches)')),
              DropdownMenuItem(value: '75mm', child: Text('75mm (3 inches)')),
              DropdownMenuItem(value: '100mm', child: Text('100mm (4 inches)')),
              DropdownMenuItem(value: '150mm', child: Text('150mm (6 inches)')),
              DropdownMenuItem(value: '200mm', child: Text('200mm (8 inches)')),
              DropdownMenuItem(
                  value: '250mm', child: Text('250mm (10 inches)')),
              DropdownMenuItem(
                  value: '300mm', child: Text('300mm (12 inches)')),
              DropdownMenuItem(value: 'Other', child: Text('Other')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedPipeDiameter = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _wallThicknessController,
            decoration: const InputDecoration(
              labelText: 'Wall Thickness',
              hintText: 'e.g., 5mm, 0.25 inches',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _priceController,
            decoration: const InputDecoration(
              labelText: 'Price Per Meter (NPR)',
              hintText: 'e.g., 150.00',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedMaterial,
            decoration: const InputDecoration(
              labelText: 'Material',
            ),
            items: const [
              DropdownMenuItem(value: 'PVC', child: Text('PVC')),
              DropdownMenuItem(value: 'Steel', child: Text('Steel')),
              DropdownMenuItem(value: 'Cast Iron', child: Text('Cast Iron')),
              DropdownMenuItem(
                  value: 'Ductile Iron', child: Text('Ductile Iron')),
              DropdownMenuItem(value: 'HDPE', child: Text('HDPE')),
              DropdownMenuItem(value: 'Copper', child: Text('Copper')),
              DropdownMenuItem(value: 'Concrete', child: Text('Concrete')),
              DropdownMenuItem(value: 'Other', child: Text('Other')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedMaterial = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedPressureRating,
            decoration: const InputDecoration(
              labelText: 'Pressure Rating',
            ),
            items: const [
              DropdownMenuItem(
                  value: 'Standard (10 bar)', child: Text('Standard (10 bar)')),
              DropdownMenuItem(
                  value: 'Low (5 bar)', child: Text('Low (5 bar)')),
              DropdownMenuItem(
                  value: 'Medium (15 bar)', child: Text('Medium (15 bar)')),
              DropdownMenuItem(
                  value: 'High (25 bar)', child: Text('High (25 bar)')),
              DropdownMenuItem(
                  value: 'Very High (40+ bar)',
                  child: Text('Very High (40+ bar)')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedPressureRating = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedTemperatureRating,
            decoration: const InputDecoration(
              labelText: 'Temperature Rating',
            ),
            items: const [
              DropdownMenuItem(
                  value: 'Normal (-20°C to 60°C)',
                  child: Text('Normal (-20°C to 60°C)')),
              DropdownMenuItem(
                  value: 'Low (-40°C to 40°C)',
                  child: Text('Low (-40°C to 40°C)')),
              DropdownMenuItem(
                  value: 'High (0°C to 100°C)',
                  child: Text('High (0°C to 100°C)')),
              DropdownMenuItem(
                  value: 'Very High (0°C to 200°C)',
                  child: Text('Very High (0°C to 200°C)')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedTemperatureRating = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedManufacturer,
            decoration: const InputDecoration(
              labelText: 'Manufacturer',
            ),
            items: const [
              DropdownMenuItem(value: 'Generic', child: Text('Generic')),
              DropdownMenuItem(value: 'ABC Pipes', child: Text('ABC Pipes')),
              DropdownMenuItem(
                  value: 'XYZ Fittings', child: Text('XYZ Fittings')),
              DropdownMenuItem(
                  value: 'Local Manufacturer',
                  child: Text('Local Manufacturer')),
              DropdownMenuItem(value: 'Other', child: Text('Other')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedManufacturer = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _standardController,
            decoration: const InputDecoration(
              labelText: 'Standard/Specification',
              hintText: 'e.g., ASTM D1785, ISO 4427',
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedState,
            decoration: const InputDecoration(
              labelText: 'State',
            ),
            items: const [
              DropdownMenuItem(value: 'Active', child: Text('Active')),
              DropdownMenuItem(value: 'Inactive', child: Text('Inactive')),
              DropdownMenuItem(
                  value: 'Under Maintenance', child: Text('Under Maintenance')),
              DropdownMenuItem(value: 'Planned', child: Text('Planned')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedState = value;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Enter additional details',
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                    _initializeControllers(); // Reset to original values
                  });
                },
                icon: const Icon(Icons.cancel),
                label: const Text('Cancel'),
              ),
              ElevatedButton.icon(
                onPressed: _saveChanges,
                icon: const Icon(Icons.save),
                label: const Text('Save Changes'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
