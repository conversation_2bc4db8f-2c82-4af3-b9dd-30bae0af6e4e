import 'package:flutter/material.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: const [
          ListTile(
            leading: Icon(Icons.cloud_sync),
            title: Text('Sync Settings'),
            subtitle: Text('Configure automatic sync options'),
          ),
          Divider(),
          ListTile(
            leading: Icon(Icons.map),
            title: Text('Map Settings'),
            subtitle: Text('Configure map display options'),
          ),
          Divider(),
          ListTile(
            leading: Icon(Icons.storage),
            title: Text('Storage Settings'),
            subtitle: Text('Manage local storage usage'),
          ),
          Divider(),
          ListTile(
            leading: Icon(Icons.info),
            title: Text('About'),
            subtitle: Text('App version and information'),
          ),
        ],
      ),
    );
  }
}
