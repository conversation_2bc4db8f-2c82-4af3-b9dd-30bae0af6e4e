import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/pipeline.dart';
import '../models/hive_models/hive_smart_meter_model.dart';
import '../home/<USER>';
import '../providers/hive_service_provider.dart';
import '../providers/sync_service_provider.dart';
import 'smart_meter_details_screen.dart';
import 'add_smart_meter_screen.dart';

class SmartMeterListScreen extends ConsumerStatefulWidget {
  const SmartMeterListScreen({super.key});

  @override
  ConsumerState<SmartMeterListScreen> createState() =>
      _SmartMeterListScreenState();
}

class _SmartMeterListScreenState extends ConsumerState<SmartMeterListScreen> {
  bool _isLoading = false;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    // Check if we need to fetch data from server
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndFetchData();
    });
  }

  Future<void> _checkAndFetchData() async {
    final hiveService = ref.read(hiveServiceProvider);
    final syncService = ref.read(syncServiceProvider);

    // Check if smart meter list is empty
    final smartMeters = hiveService.getAllSmartMeters();
    if (smartMeters.isEmpty) {
      setState(() {
        _isLoading = true;
      });

      // Fetch smart meters from server
      await syncService.fetchSmartMeterListing();

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _syncWithServer() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = ref.read(syncServiceProvider);
      await syncService.syncSmartMeterListing();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Smart meters synced successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error syncing smart meters: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  // Add method to delete all meters locally
  Future<void> _deleteAllMeters() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Meters'),
        content: const Text(
            'This will delete all meters from local storage. This operation cannot be undone. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE ALL'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final hiveService = ref.read(hiveServiceProvider);

      // Get all meters
      final meters = hiveService.getAllSmartMeters();

      // Delete each meter
      for (final meter in meters) {
        await hiveService.deleteSmartMeter(meter.id, permanent: true);
      }

      // Update the map state to reflect the changes
      final mapNotifier = ref.read(mapStateProvider.notifier);
      await mapNotifier.loadInitialData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All meters deleted successfully'),
            duration: Duration(seconds: 2),
          ),
        );

        // Refresh the UI
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error deleting all meters: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting all meters: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get smart meters from Hive
    final hiveService = ref.read(hiveServiceProvider);
    final smartMeters = hiveService.getAllSmartMeters();

    // Also get meters from map state for any that might not be in Hive yet
    final mapState = ref.watch(mapStateProvider);
    final mapMeters = mapState.userValvePositions
        .where((position) => position.type == PositionType.meter)
        .toList();

    // Create a combined list of all meters
    final List<dynamic> meters = [...smartMeters];

    // Add any map meters that aren't already in the list
    for (final mapMeter in mapMeters) {
      if (!smartMeters.any((m) => m.id == mapMeter.id)) {
        meters.add(mapMeter);
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Smart Meter List'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Smart Meter',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddSmartMeterScreen(),
                ),
              ).then((refresh) {
                if (refresh == true) {
                  setState(() {});
                }
              });
            },
          ),
          // Sync button
          IconButton(
            icon: _isSyncing
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.sync),
            tooltip: 'Sync with server',
            onPressed: _isSyncing ? null : _syncWithServer,
          ),
          // Delete all meters button
          IconButton(
            icon: const Icon(Icons.delete_forever),
            tooltip: 'Delete All Meters',
            onPressed: _deleteAllMeters,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              // Reload data from local database
              final mapNotifier = ref.read(mapStateProvider.notifier);
              mapNotifier.loadInitialData();
              setState(() {});
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Fetching smart meters from server...'),
                ],
              ),
            )
          : meters.isEmpty
              ? const Center(
                  child: Text('No smart meters found'),
                )
              : ListView.builder(
                  itemCount: meters.length,
                  itemBuilder: (context, index) {
                    final meter = meters[index];
                    HiveSmartMeterModel smartMeter;

                    // Check if the meter is already a HiveSmartMeterModel
                    if (meter is HiveSmartMeterModel) {
                      smartMeter = meter;
                    } else {
                      // It's a UserValvePosition from the map
                      // Convert to HiveSmartMeterModel
                      smartMeter = HiveSmartMeterModel(
                        latitude: meter.position.latitude,
                        longitude: meter.position.longitude,
                        title: meter.title,
                        areaId: 'default',
                      );
                    }

                    // Get meter properties
                    final meterType = smartMeter.type;
                    final reading = smartMeter.reading;

                    // Determine color based on status
                    Color statusColor;
                    switch (smartMeter.status) {
                      case 'Active':
                        statusColor = Colors.green;
                        break;
                      case 'Inactive':
                        statusColor = Colors.grey;
                        break;
                      case 'Maintenance':
                        statusColor = Colors.orange;
                        break;
                      default:
                        statusColor = Colors.black;
                    }

                    return ListTile(
                      leading: Icon(Icons.speed, color: statusColor),
                      title: Text(
                        smartMeter.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(
                              'Type: $meterType | Status: ${smartMeter.status}'),
                          Text('Reading: $reading'),
                        ],
                      ),
                      isThreeLine: true,
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // Navigate to smart meter details screen using the already retrieved smart meter
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SmartMeterDetailsScreen(
                              smartMeter: smartMeter,
                            ),
                          ),
                        ).then((deleted) {
                          // Refresh the UI when returning from the details screen
                          if (deleted == true) {
                            setState(() {});
                          }
                        });
                      },
                    );
                  },
                ),
    );
  }
}
