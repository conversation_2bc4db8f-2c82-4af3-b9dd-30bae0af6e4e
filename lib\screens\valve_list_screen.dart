import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/pipeline.dart';
import '../models/hive_models/hive_valve_model.dart';
import '../home/<USER>';

import '../providers/hive_service_provider.dart';
import '../providers/sync_service_provider.dart';
import 'valve_details_screen.dart';
import 'add_valve_screen.dart';

class ValveListScreen extends ConsumerStatefulWidget {
  const ValveListScreen({super.key});

  @override
  ConsumerState<ValveListScreen> createState() => _ValveListScreenState();
}

class _ValveListScreenState extends ConsumerState<ValveListScreen> {
  bool _isLoading = false;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    // Check if we need to fetch data from server
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndFetchData();
    });
  }

  Future<void> _checkAndFetchData() async {
    final hiveService = ref.read(hiveServiceProvider);
    final syncService = ref.read(syncServiceProvider);

    // Check if valve list is empty
    final valves = hiveService.getAllValves();
    if (valves.isEmpty) {
      setState(() {
        _isLoading = true;
      });

      // Fetch valves from server
      await syncService.fetchAllData();

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _syncWithServer() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = ref.read(syncServiceProvider);
      await syncService.syncAllData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Valves synced successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error syncing valves: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  // Add method to delete all valves locally
  Future<void> _deleteAllValves() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Valves'),
        content: const Text(
            'This will delete all valves from local storage. This operation cannot be undone. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE ALL'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final hiveService = ref.read(hiveServiceProvider);

      // Get all valves
      final valves = hiveService.getAllValves();

      // Delete each valve
      for (final valve in valves) {
        await hiveService.deleteValve(valve.id, permanent: true);
      }

      // Update the map state to reflect the changes
      final mapNotifier = ref.read(mapStateProvider.notifier);
      await mapNotifier.loadInitialData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All valves deleted successfully'),
            duration: Duration(seconds: 2),
          ),
        );

        // Refresh the UI
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error deleting all valves: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting all valves: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get valves from Hive
    final hiveService = ref.read(hiveServiceProvider);
    final hiveValves = hiveService.getAllValves();

    // Also get valves from map state for any that might not be in Hive yet
    final mapState = ref.watch(mapStateProvider);
    final mapValves = mapState.userValvePositions
        .where((position) => position.type == PositionType.valve)
        .toList();

    // Create a combined list of all valves
    final List<dynamic> valves = [...hiveValves];

    // Add any map valves that aren't already in the list
    for (final mapValve in mapValves) {
      if (!hiveValves.any((v) => v.id == mapValve.id)) {
        valves.add(mapValve);
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Valve List'),
        actions: [
          // Add button
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Valve',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddValveScreen(),
                ),
              ).then((refresh) {
                if (refresh == true) {
                  setState(() {});
                }
              });
            },
          ),
          // Sync button
          IconButton(
            icon: _isSyncing
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.sync),
            tooltip: 'Sync with server',
            onPressed: _isSyncing ? null : _syncWithServer,
          ),
          // Delete all valves button
          IconButton(
            icon: const Icon(Icons.delete_forever),
            tooltip: 'Delete All Valves',
            onPressed: _deleteAllValves,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              // Reload data from local database
              final mapNotifier = ref.read(mapStateProvider.notifier);
              mapNotifier.loadInitialData();
              setState(() {});
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Fetching valves from server...'),
                ],
              ),
            )
          : valves.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('No valves found'),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.add),
                        label: const Text('Add Valve'),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AddValveScreen(),
                            ),
                          ).then((refresh) {
                            if (refresh == true) {
                              setState(() {});
                            }
                          });
                        },
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: valves.length,
                  itemBuilder: (context, index) {
                    final valve = valves[index];

                    // Check if the valve is already a HiveValveModel
                    HiveValveModel hiveValve;
                    if (valve is HiveValveModel) {
                      hiveValve = valve;
                    } else {
                      // Convert to HiveValveModel
                      hiveValve = HiveValveModel(
                        id: valve.id,
                        latitude: valve.position.latitude,
                        longitude: valve.position.longitude,
                        title: valve.title,
                        type: 'Standard',
                        size: 25.0,
                        controlType: 'Manual',
                        areaId: 'default',
                        status: 'Open',
                      );
                    }

                    // Determine color based on status
                    Color statusColor;
                    switch (hiveValve.status) {
                      case 'Open':
                        statusColor = Colors.green;
                        break;
                      case 'Closed':
                        statusColor = Colors.red;
                        break;
                      case 'Maintenance':
                        statusColor = Colors.orange;
                        break;
                      default:
                        statusColor = Colors.black;
                    }

                    return ListTile(
                      leading: Icon(Icons.plumbing, color: statusColor),
                      title: Text(
                        hiveValve.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(
                              'Type: ${hiveValve.type} | Status: ${hiveValve.status}'),
                          Text('Control: ${hiveValve.controlType}'),
                        ],
                      ),
                      isThreeLine: true,
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // Navigate to valve details screen
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ValveDetailsScreen(
                              valve: hiveValve,
                            ),
                          ),
                        ).then((deleted) {
                          // Refresh the UI when returning from the details screen
                          if (deleted == true) {
                            setState(() {});
                          }
                        });
                      },
                    );
                  },
                ),
    );
  }
}
