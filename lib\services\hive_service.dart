import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

import '../models/hive_models/hive_latlng.dart';
import '../models/hive_models/hive_user_model.dart';
// hive_user_list_model.dart removed - now using only HiveUserModel
import '../models/hive_models/hive_valve_model.dart';
import '../models/hive_models/hive_connector_model.dart';
import '../models/hive_models/hive_pipeline_model.dart';
import '../models/hive_models/hive_smart_meter_model.dart';
import '../models/hive_models/hive_connector_listing_model.dart';
import '../models/hive_models/hive_pipeline_listing_model.dart';
import '../models/hive_models/hive_category_model.dart';
// manual_adapters.dart import removed - not used

class HiveService {
  static const String usersBoxName = 'users';
  static const String userListBoxName = 'userList'; // New box for user list
  static const String valvesBoxName = 'valves';
  static const String connectorsBoxName = 'connectors';
  static const String pipelinesBoxName = 'pipelines';
  static const String smartMetersBoxName = 'smartMeters';

  static Future<void> init() async {
    try {
      // Initialize Hive
      final appDocumentDir = await getApplicationDocumentsDirectory();
      await Hive.initFlutter(appDocumentDir.path);

      // Register adapters
      _registerAdapters();

      // Open boxes with error handling
      try {
        await Hive.openBox<HiveUserModel>(usersBoxName);
      } catch (e) {
        debugPrint('Error opening users box: $e');
        // Try to delete and recreate the box if there's an error
        await Hive.deleteBoxFromDisk(usersBoxName);
        await Hive.openBox<HiveUserModel>(usersBoxName);
      }

      // User list box removed - now using only HiveUserModel

      try {
        await Hive.openBox<HiveValveModel>(valvesBoxName);
      } catch (e) {
        debugPrint('Error opening valves box: $e');
        await Hive.deleteBoxFromDisk(valvesBoxName);
        await Hive.openBox<HiveValveModel>(valvesBoxName);
      }

      try {
        await Hive.openBox<HiveConnectorModel>(connectorsBoxName);
      } catch (e) {
        debugPrint('Error opening connectors box: $e');
        await Hive.deleteBoxFromDisk(connectorsBoxName);
        await Hive.openBox<HiveConnectorModel>(connectorsBoxName);
      }

      try {
        await Hive.openBox<HivePipelineModel>(pipelinesBoxName);
      } catch (e) {
        debugPrint('Error opening pipelines box: $e');
        await Hive.deleteBoxFromDisk(pipelinesBoxName);
        await Hive.openBox<HivePipelineModel>(pipelinesBoxName);
      }

      try {
        await Hive.openBox<HiveSmartMeterModel>(smartMetersBoxName);
      } catch (e) {
        debugPrint('Error opening smart meters box: $e');
        await Hive.deleteBoxFromDisk(smartMetersBoxName);
        await Hive.openBox<HiveSmartMeterModel>(smartMetersBoxName);
      }

      debugPrint('HiveService: All boxes opened successfully');
    } catch (e) {
      debugPrint('HiveService: Error initializing Hive: $e');
    }
  }

  // User methods
  static Box<HiveUserModel> getUsersBox() {
    return Hive.box<HiveUserModel>(userListBoxName);
  }

  static Future<void> saveUser(HiveUserModel user,
      {bool markAsSynced = false}) async {
    final box = getUsersBox();
    if (markAsSynced) {
      user.markAsSynced();
    }

    // Debug log before saving
    debugPrint(
        'HiveService: BEFORE SAVE - User ${user.id} with position (${user.latitude}, ${user.longitude})');

    // Save to box
    await box.put(user.id, user);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Verify the save by retrieving the user again
    final savedUser = box.get(user.id);
    if (savedUser != null) {
      debugPrint(
          'HiveService: AFTER SAVE - User ${savedUser.id} position in box: (${savedUser.latitude}, ${savedUser.longitude})');
    } else {
      debugPrint(
          'HiveService: ERROR - Failed to retrieve user ${user.id} after saving');
    }
  }

  static List<HiveUserModel> getAllUsers({bool includeDeleted = false}) {
    final box = getUsersBox();
    final users = box.values.toList();
    if (!includeDeleted) {
      return users.where((user) => !user.isDeleted).toList();
    }
    return users;
  }

  static List<HiveUserModel> getUnsyncedUsers() {
    final box = getUsersBox();
    return box.values.where((user) => !user.isSync).toList();
  }

  static Future<void> deleteUser(String id, {bool permanent = false}) async {
    final box = getUsersBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final user = box.get(id);
      if (user != null) {
        user.markAsDeleted();
        // Save the changes to the box
        await box.put(user.id, user);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: User ${user.id} marked as deleted');
      }
    }
  }

  // Valve methods
  static Box<HiveValveModel> getValvesBox() {
    return Hive.box<HiveValveModel>(valvesBoxName);
  }

  static Future<void> saveValve(HiveValveModel valve,
      {bool markAsSynced = false}) async {
    final box = getValvesBox();
    if (markAsSynced) {
      valve.markAsSynced();
    }
    await box.put(valve.id, valve);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the valve was saved
    debugPrint(
        'HiveService: Valve ${valve.id} saved to box with position (${valve.latitude}, ${valve.longitude})');
  }

  static List<HiveValveModel> getAllValves({bool includeDeleted = false}) {
    final box = getValvesBox();
    final valves = box.values.toList();
    if (!includeDeleted) {
      return valves.where((valve) => !valve.isDeleted).toList();
    }
    return valves;
  }

  static List<HiveValveModel> getUnsyncedValves() {
    final box = getValvesBox();
    return box.values.where((valve) => !valve.isSync).toList();
  }

  static Future<void> deleteValve(String id, {bool permanent = false}) async {
    final box = getValvesBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final valve = box.get(id);
      if (valve != null) {
        valve.markAsDeleted();
        // Save the changes to the box
        await box.put(valve.id, valve);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: Valve ${valve.id} marked as deleted');
      }
    }
  }

  // Connector methods
  static Box<HiveConnectorModel> getConnectorsBox() {
    return Hive.box<HiveConnectorModel>(connectorsBoxName);
  }

  static Future<void> saveConnector(HiveConnectorModel connector,
      {bool markAsSynced = false}) async {
    final box = getConnectorsBox();
    if (markAsSynced) {
      connector.markAsSynced();
    }
    await box.put(connector.id, connector);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the connector was saved
    debugPrint(
        'HiveService: Connector ${connector.id} saved to box with position (${connector.latitude}, ${connector.longitude})');
  }

  static List<HiveConnectorModel> getAllConnectors(
      {bool includeDeleted = false}) {
    final box = getConnectorsBox();
    final connectors = box.values.toList();
    if (!includeDeleted) {
      return connectors.where((connector) => !connector.isDeleted).toList();
    }
    return connectors;
  }

  static List<HiveConnectorModel> getUnsyncedConnectors() {
    final box = getConnectorsBox();
    return box.values.where((connector) => !connector.isSync).toList();
  }

  static Future<void> deleteConnector(String id,
      {bool permanent = false}) async {
    final box = getConnectorsBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final connector = box.get(id);
      if (connector != null) {
        connector.markAsDeleted();
        // Save the changes to the box
        await box.put(connector.id, connector);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: Connector ${connector.id} marked as deleted');
      }
    }
  }

  // Pipeline methods
  static Box<HivePipelineModel> getPipelinesBox() {
    return Hive.box<HivePipelineModel>(pipelinesBoxName);
  }

  static Future<void> savePipeline(HivePipelineModel pipeline,
      {bool markAsSynced = false}) async {
    final box = getPipelinesBox();
    if (markAsSynced) {
      pipeline.markAsSynced();
    }
    await box.put(pipeline.id, pipeline);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the pipeline was saved
    debugPrint(
        'HiveService: Pipeline ${pipeline.id} saved to box with ${pipeline.points.length} points');
  }

  static List<HivePipelineModel> getAllPipelines(
      {bool includeDeleted = false}) {
    final box = getPipelinesBox();
    final pipelines = box.values.toList();
    if (!includeDeleted) {
      return pipelines.where((pipeline) => !pipeline.isDeleted).toList();
    }
    return pipelines;
  }

  // Get a specific pipeline by ID
  static HivePipelineModel? getPipeline(String id) {
    final box = getPipelinesBox();
    final pipeline = box.get(id);
    if (pipeline != null && !pipeline.isDeleted) {
      return pipeline;
    }
    return null;
  }

  static List<HivePipelineModel> getUnsyncedPipelines() {
    final box = getPipelinesBox();
    final listpipe = box.values.where((pipeline) => !pipeline.isSync).toList();

    return listpipe;
  }

  static Future<void> deletePipeline(String id,
      {bool permanent = false}) async {
    final box = getPipelinesBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final pipeline = box.get(id);
      if (pipeline != null) {
        pipeline.markAsDeleted();
        // Save the changes to the box
        await box.put(pipeline.id, pipeline);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: Pipeline ${pipeline.id} marked as deleted');
      }
    }
  }

  // User List methods removed - now using only HiveUserModel

  // Smart Meter methods
  static Box<HiveSmartMeterModel> getSmartMetersBox() {
    return Hive.box<HiveSmartMeterModel>(smartMetersBoxName);
  }

  static Future<void> saveSmartMeter(HiveSmartMeterModel smartMeter,
      {bool markAsSynced = false}) async {
    final box = getSmartMetersBox();
    if (markAsSynced) {
      smartMeter.markAsSynced();
    }
    await box.put(smartMeter.id, smartMeter);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the smart meter was saved
    debugPrint(
        'HiveService: Smart Meter ${smartMeter.id} saved to box with position (${smartMeter.latitude}, ${smartMeter.longitude})');
  }

  static List<HiveSmartMeterModel> getAllSmartMeters(
      {bool includeDeleted = false}) {
    final box = getSmartMetersBox();
    final smartMeters = box.values.toList();
    if (!includeDeleted) {
      return smartMeters.where((smartMeter) => !smartMeter.isDeleted).toList();
    }
    return smartMeters;
  }

  static List<HiveSmartMeterModel> getUnsyncedSmartMeters() {
    final box = getSmartMetersBox();
    return box.values.where((smartMeter) => !smartMeter.isSync).toList();
  }

  static Future<void> deleteSmartMeter(String id,
      {bool permanent = false}) async {
    final box = getSmartMetersBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final smartMeter = box.get(id);
      if (smartMeter != null) {
        smartMeter.markAsDeleted();
        // Save the changes to the box
        await box.put(smartMeter.id, smartMeter);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint(
            'HiveService: Smart Meter ${smartMeter.id} marked as deleted');
      }
    }
  }

  // Get all unsynced items for map elements only (not including user list)
  static List<dynamic> getAllUnsyncedItems() {
    final List<dynamic> unsyncedItems = [];
    unsyncedItems.addAll(getUnsyncedUsers());
    unsyncedItems.addAll(getUnsyncedValves());
    unsyncedItems.addAll(getUnsyncedConnectors());
    unsyncedItems.addAll(getUnsyncedPipelines());
    unsyncedItems.addAll(getUnsyncedSmartMeters());
    debugPrint('HiveService: Found ${unsyncedItems.length} unsynced map items');
    return unsyncedItems;
  }

  // getAllUnsyncedUserListItems method removed - now using only HiveUserModel

  // Check if there are any unsynced items
  static bool hasUnsyncedItems() {
    return getAllUnsyncedItems().isNotEmpty;
  }

  // Check if the local database is empty (no data in any box)
  static bool isDatabaseEmpty() {
    return getUsersBox().isEmpty &&
        getValvesBox().isEmpty &&
        getConnectorsBox().isEmpty &&
        getPipelinesBox().isEmpty &&
        getSmartMetersBox().isEmpty;
  }

  // hasUnsyncedUserListItems method removed - now using only HiveUserModel

  // Clear all data
  static Future<void> clearAllData() async {
    await getUsersBox().clear();
    await getValvesBox().clear();
    await getConnectorsBox().clear();
    await getPipelinesBox().clear();
    await getSmartMetersBox().clear();
  }

  // Clear only map data (not user list)
  static Future<void> clearMapData() async {
    await getUsersBox().clear();
    await getValvesBox().clear();
    await getConnectorsBox().clear();
    await getPipelinesBox().clear();
    await getSmartMetersBox().clear();
  }

  // Register all adapters manually
  static void _registerAdapters() {
    // Register all adapters manually
    // This is a temporary solution until build_runner generates the adapters

    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(HiveUserModelAdapter());
    }

    // HiveUserListModelAdapter registration removed - now using only HiveUserModel

    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(HiveValveModelAdapter());
    }

    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(HiveConnectorModelAdapter());
    }

    if (!Hive.isAdapterRegistered(9)) {
      Hive.registerAdapter(HivePipelineModelAdapter());
    }

    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(HiveSmartMeterModelAdapter());
    }

    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(HiveLatLngAdapter());
    }

    if (!Hive.isAdapterRegistered(11)) {
      Hive.registerAdapter(HiveConnectorListingModelAdapter());
    }

    if (!Hive.isAdapterRegistered(12)) {
      Hive.registerAdapter(HivePipelineListingModelAdapter());
    }

    if (!Hive.isAdapterRegistered(8)) {
      Hive.registerAdapter(HiveCategoryModelAdapter());
    }
  }
}
