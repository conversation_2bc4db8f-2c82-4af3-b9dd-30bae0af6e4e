import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

import '../../models/hive_models/hive_latlng.dart';
import '../../models/hive_models/hive_user_model.dart';
import '../../models/hive_models/hive_valve_model.dart';
import '../../models/hive_models/hive_connector_model.dart';
import '../../models/hive_models/hive_pipeline_model.dart';
import '../../models/hive_models/hive_smart_meter_model.dart';
import '../../models/hive_models/hive_connector_listing_model.dart';
import '../../models/hive_models/hive_pipeline_listing_model.dart';
import '../../models/hive_models/hive_category_model.dart';
// manual_adapters.dart import removed - not used
import '../interfaces/hive_service_interface.dart';

class HiveServiceImpl implements HiveServiceInterface {
  static const String usersBoxName = 'users';
  static const String valvesBoxName = 'valves';
  static const String connectorsBoxName = 'connectors';
  static const String pipelinesBoxName = 'pipelines';
  static const String smartMetersBoxName = 'smartMeters';
  static const String connectorListingBoxName = 'connectorListing';
  static const String pipelineListingBoxName = 'pipelineListing';
  static const String categoriesBoxName = 'categories';

  @override
  Future<void> init() async {
    try {
      // Initialize Hive
      final appDocumentDir = await getApplicationDocumentsDirectory();
      await Hive.initFlutter(appDocumentDir.path);

      // Register adapters
      _registerAdapters();

      // Open boxes with error handling
      try {
        await Hive.openBox<HiveUserModel>(usersBoxName);
      } catch (e) {
        debugPrint('Error opening users box: $e');
        // Try to delete and recreate the box if there's an error
        await Hive.deleteBoxFromDisk(usersBoxName);
        await Hive.openBox<HiveUserModel>(usersBoxName);
      }

      // User list box removed - now using only HiveUserModel

      try {
        await Hive.openBox<HiveValveModel>(valvesBoxName);
      } catch (e) {
        debugPrint('Error opening valves box: $e');
        await Hive.deleteBoxFromDisk(valvesBoxName);
        await Hive.openBox<HiveValveModel>(valvesBoxName);
      }

      try {
        await Hive.openBox<HiveConnectorModel>(connectorsBoxName);
      } catch (e) {
        debugPrint('Error opening connectors box: $e');
        await Hive.deleteBoxFromDisk(connectorsBoxName);
        await Hive.openBox<HiveConnectorModel>(connectorsBoxName);
      }

      try {
        await Hive.openBox<HivePipelineModel>(pipelinesBoxName);
      } catch (e) {
        debugPrint('Error opening pipelines box (likely type ID conflict): $e');
        debugPrint('Deleting and recreating pipelines box...');
        await Hive.deleteBoxFromDisk(pipelinesBoxName);
        await Hive.openBox<HivePipelineModel>(pipelinesBoxName);
      }

      try {
        await Hive.openBox<HiveSmartMeterModel>(smartMetersBoxName);
      } catch (e) {
        debugPrint('Error opening smart meters box: $e');
        await Hive.deleteBoxFromDisk(smartMetersBoxName);
        await Hive.openBox<HiveSmartMeterModel>(smartMetersBoxName);
      }

      try {
        await Hive.openBox<HiveConnectorListingModel>(connectorListingBoxName);
      } catch (e) {
        debugPrint(
            'Error opening connector listing box (likely type ID conflict): $e');
        debugPrint('Deleting and recreating connector listing box...');
        await Hive.deleteBoxFromDisk(connectorListingBoxName);
        await Hive.openBox<HiveConnectorListingModel>(connectorListingBoxName);
      }

      try {
        await Hive.openBox<HivePipelineListingModel>(pipelineListingBoxName);
      } catch (e) {
        debugPrint(
            'Error opening pipeline listing box (likely type ID conflict): $e');
        debugPrint('Deleting and recreating pipeline listing box...');
        await Hive.deleteBoxFromDisk(pipelineListingBoxName);
        await Hive.openBox<HivePipelineListingModel>(pipelineListingBoxName);
      }

      try {
        await Hive.openBox<HiveCategoryModel>(categoriesBoxName);
      } catch (e) {
        debugPrint('Error opening categories box: $e');
        await Hive.deleteBoxFromDisk(categoriesBoxName);
        await Hive.openBox<HiveCategoryModel>(categoriesBoxName);
      }

      debugPrint('HiveService: All boxes opened successfully');
    } catch (e) {
      debugPrint('HiveService: Error initializing Hive: $e');
    }
  }

  // User methods
  @override
  Box<HiveUserModel> getUsersBox() {
    return Hive.box<HiveUserModel>(usersBoxName);
  }

  @override
  Future<void> saveUser(HiveUserModel user, {bool markAsSynced = false}) async {
    final box = getUsersBox();
    if (markAsSynced) {
      user.markAsSynced();
    }

    // Debug log before saving
    debugPrint(
        'HiveService: BEFORE SAVE - User ${user.id} with position (${user.latitude}, ${user.longitude})');

    // Save to box
    await box.put(user.id, user);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Verify the save by retrieving the user again
    final savedUser = box.get(user.id);
    if (savedUser != null) {
      debugPrint(
          'HiveService: AFTER SAVE - User ${savedUser.id} position in box: (${savedUser.latitude}, ${savedUser.longitude})');
    } else {
      debugPrint(
          'HiveService: ERROR - Failed to retrieve user ${user.id} after saving');
    }
  }

  @override
  List<HiveUserModel> getAllUsers({bool includeDeleted = false}) {
    final box = getUsersBox();
    final users = box.values.toList();
    if (!includeDeleted) {
      return users.where((user) => !user.isDeleted).toList();
    }
    return users;
  }

  @override
  List<HiveUserModel> getUnsyncedUsers() {
    final box = getUsersBox();
    return box.values.where((user) => !user.isSync).toList();
  }

  @override
  Future<void> deleteUser(String id, {bool permanent = false}) async {
    final box = getUsersBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final user = box.get(id);
      if (user != null) {
        user.markAsDeleted();
        // Save the changes to the box
        await box.put(user.id, user);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: User ${user.id} marked as deleted');
      }
    }
  }

  // Valve methods
  @override
  Box<HiveValveModel> getValvesBox() {
    return Hive.box<HiveValveModel>(valvesBoxName);
  }

  @override
  Future<void> saveValve(HiveValveModel valve,
      {bool markAsSynced = false}) async {
    final box = getValvesBox();
    if (markAsSynced) {
      valve.markAsSynced();
    }
    await box.put(valve.id, valve);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the valve was saved
    debugPrint(
        'HiveService: Valve ${valve.id} saved to box with position (${valve.latitude}, ${valve.longitude})');
  }

  @override
  List<HiveValveModel> getAllValves({bool includeDeleted = false}) {
    final box = getValvesBox();
    final valves = box.values.toList();
    if (!includeDeleted) {
      return valves.where((valve) => !valve.isDeleted).toList();
    }
    return valves;
  }

  @override
  List<HiveValveModel> getUnsyncedValves() {
    final box = getValvesBox();
    return box.values.where((valve) => !valve.isSync).toList();
  }

  @override
  Future<void> deleteValve(String id, {bool permanent = false}) async {
    final box = getValvesBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final valve = box.get(id);
      if (valve != null) {
        valve.markAsDeleted();
        // Save the changes to the box
        await box.put(valve.id, valve);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: Valve ${valve.id} marked as deleted');
      }
    }
  }

  // Connector methods
  @override
  Box<HiveConnectorModel> getConnectorsBox() {
    return Hive.box<HiveConnectorModel>(connectorsBoxName);
  }

  @override
  Future<void> saveConnector(HiveConnectorModel connector,
      {bool markAsSynced = false}) async {
    final box = getConnectorsBox();
    if (markAsSynced) {
      connector.markAsSynced();
    }
    await box.put(connector.id, connector);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the connector was saved
    debugPrint(
        'HiveService: Connector ${connector.id} saved to box with position (${connector.latitude}, ${connector.longitude})');
  }

  @override
  List<HiveConnectorModel> getAllConnectors({bool includeDeleted = false}) {
    final box = getConnectorsBox();
    final connectors = box.values.toList();
    if (!includeDeleted) {
      return connectors.where((connector) => !connector.isDeleted).toList();
    }
    return connectors;
  }

  @override
  List<HiveConnectorModel> getUnsyncedConnectors() {
    final box = getConnectorsBox();
    return box.values.where((connector) => !connector.isSync).toList();
  }

  @override
  Future<void> deleteConnector(String id, {bool permanent = false}) async {
    final box = getConnectorsBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final connector = box.get(id);
      if (connector != null) {
        connector.markAsDeleted();
        // Save the changes to the box
        await box.put(connector.id, connector);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: Connector ${connector.id} marked as deleted');
      }
    }
  }

  // Pipeline methods
  @override
  Box<HivePipelineModel> getPipelinesBox() {
    return Hive.box<HivePipelineModel>(pipelinesBoxName);
  }

  @override
  Future<void> savePipeline(HivePipelineModel pipeline,
      {bool markAsSynced = false}) async {
    final box = getPipelinesBox();
    if (markAsSynced) {
      pipeline.markAsSynced();
    }
    await box.put(pipeline.id, pipeline);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the pipeline was saved
    debugPrint(
        'HiveService: Pipeline ${pipeline.id} saved to box with ${pipeline.points.length} points');
  }

  @override
  List<HivePipelineModel> getAllPipelines({bool includeDeleted = false}) {
    final box = getPipelinesBox();
    final pipelines = box.values.toList();
    if (!includeDeleted) {
      return pipelines.where((pipeline) => !pipeline.isDeleted).toList();
    }
    return pipelines;
  }

  // Get a specific pipeline by ID
  @override
  HivePipelineModel? getPipeline(String id) {
    final box = getPipelinesBox();
    final pipeline = box.get(id);
    if (pipeline != null && !pipeline.isDeleted) {
      return pipeline;
    }
    return null;
  }

  @override
  List<HivePipelineModel> getUnsyncedPipelines() {
    final box = getPipelinesBox();
    return box.values.where((pipeline) => !pipeline.isSync).toList();
  }

  @override
  Future<void> deletePipeline(String id, {bool permanent = false}) async {
    final box = getPipelinesBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final pipeline = box.get(id);
      if (pipeline != null) {
        pipeline.markAsDeleted();
        // Save the changes to the box
        await box.put(pipeline.id, pipeline);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: Pipeline ${pipeline.id} marked as deleted');
      }
    }
  }

  // User List methods removed - now using only HiveUserModel

  @override
  List<HiveUserModel> getAllUnsyncedUserItems() {
    // Since we removed user list, this now returns unsynced users from the main users box
    return getUnsyncedUsers();
  }

  @override
  bool hasUnsyncedUserListItems() {
    // Since we removed user list, this now checks unsynced users from the main users box
    return getUnsyncedUsers().isNotEmpty;
  }

  // Smart Meter methods
  @override
  Box<HiveSmartMeterModel> getSmartMetersBox() {
    return Hive.box<HiveSmartMeterModel>(smartMetersBoxName);
  }

  @override
  Future<void> saveSmartMeter(HiveSmartMeterModel smartMeter,
      {bool markAsSynced = false}) async {
    final box = getSmartMetersBox();
    if (markAsSynced) {
      smartMeter.markAsSynced();
    }
    await box.put(smartMeter.id, smartMeter);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the smart meter was saved
    debugPrint(
        'HiveService: Smart Meter ${smartMeter.id} saved to box with position (${smartMeter.latitude}, ${smartMeter.longitude})');
  }

  @override
  List<HiveSmartMeterModel> getAllSmartMeters({bool includeDeleted = false}) {
    final box = getSmartMetersBox();
    final smartMeters = box.values.toList();
    if (!includeDeleted) {
      return smartMeters.where((smartMeter) => !smartMeter.isDeleted).toList();
    }
    return smartMeters;
  }

  @override
  List<HiveSmartMeterModel> getUnsyncedSmartMeters() {
    final box = getSmartMetersBox();
    return box.values.where((smartMeter) => !smartMeter.isSync).toList();
  }

  @override
  Future<void> deleteSmartMeter(String id, {bool permanent = false}) async {
    final box = getSmartMetersBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final smartMeter = box.get(id);
      if (smartMeter != null) {
        smartMeter.markAsDeleted();
        // Save the changes to the box
        await box.put(smartMeter.id, smartMeter);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint(
            'HiveService: Smart Meter ${smartMeter.id} marked as deleted');
      }
    }
  }

  // Get all unsynced items for map elements only (not including user list)
  @override
  List<dynamic> getAllUnsyncedItems() {
    final List<dynamic> unsyncedItems = [];
    unsyncedItems.addAll(getUnsyncedUsers());
    unsyncedItems.addAll(getUnsyncedValves());
    unsyncedItems.addAll(getUnsyncedConnectors());
    unsyncedItems.addAll(getUnsyncedPipelines());
    unsyncedItems.addAll(getUnsyncedSmartMeters());
    debugPrint('HiveService: Found ${unsyncedItems.length} unsynced map items');
    return unsyncedItems;
  }

  // getAllUnsyncedUserListItems method removed - now using only HiveUserModel

  // Check if there are any unsynced items
  @override
  bool hasUnsyncedItems() {
    return getAllUnsyncedItems().isNotEmpty;
  }

  // Check if the local database is empty (no data in any box)
  @override
  bool isDatabaseEmpty() {
    return getUsersBox().isEmpty &&
        getValvesBox().isEmpty &&
        getConnectorsBox().isEmpty &&
        getPipelinesBox().isEmpty &&
        getSmartMetersBox().isEmpty;
  }

  // hasUnsyncedUserListItems method removed - now using only HiveUserModel

  // Connector Listing methods
  @override
  Box<HiveConnectorListingModel> getConnectorListingBox() {
    return Hive.box<HiveConnectorListingModel>(connectorListingBoxName);
  }

  // Category methods
  @override
  Box<HiveCategoryModel> getCategoriesBox() {
    return Hive.box<HiveCategoryModel>(categoriesBoxName);
  }

  @override
  Future<void> saveCategory(HiveCategoryModel category,
      {bool markAsSynced = false}) async {
    final box = getCategoriesBox();
    if (markAsSynced) {
      category.isSync = true;
    }
    await box.put(category.id, category);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the category was saved
    debugPrint('HiveService: Category ${category.id} saved to box');
  }

  @override
  List<HiveCategoryModel> getAllCategories({bool includeDeleted = false}) {
    final box = getCategoriesBox();
    final categories = box.values.toList();
    if (!includeDeleted) {
      return categories.where((category) => !category.isDeleted).toList();
    }
    return categories;
  }

  @override
  List<HiveCategoryModel> getUnsyncedCategories() {
    final box = getCategoriesBox();
    return box.values.where((category) => !category.isSync).toList();
  }

  @override
  Future<void> deleteCategory(String id, {bool permanent = false}) async {
    final box = getCategoriesBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final category = box.get(id);
      if (category != null) {
        category.markAsDeleted();
        // Save the changes to the box
        await box.put(category.id, category);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint('HiveService: Category ${category.id} marked as deleted');
      }
    }
  }

  @override
  Future<void> saveConnectorListingItem(HiveConnectorListingModel connector,
      {bool markAsSynced = false}) async {
    final box = getConnectorListingBox();
    if (markAsSynced) {
      connector.markAsUpdated();
      connector.isSync = true;
    }
    await box.put(connector.id, connector);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the connector listing item was saved
    debugPrint(
        'HiveService: Connector Listing Item ${connector.id} saved to box');
  }

  @override
  List<HiveConnectorListingModel> getAllConnectorListingItems(
      {bool includeDeleted = false}) {
    final box = getConnectorListingBox();
    final connectors = box.values.toList();
    if (!includeDeleted) {
      return connectors.where((connector) => !connector.isDeleted).toList();
    }
    return connectors;
  }

  @override
  List<HiveConnectorListingModel> getUnsyncedConnectorListingItems() {
    final box = getConnectorListingBox();
    return box.values.where((connector) => !connector.isSync).toList();
  }

  @override
  Future<void> deleteConnectorListingItem(String id,
      {bool permanent = false}) async {
    final box = getConnectorListingBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final connector = box.get(id);
      if (connector != null) {
        connector.markAsDeleted();
        // Save the changes to the box
        await box.put(connector.id, connector);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint(
            'HiveService: Connector Listing Item ${connector.id} marked as deleted');
      }
    }
  }

  // Pipeline Listing methods
  @override
  Box<HivePipelineListingModel> getPipelineListingBox() {
    return Hive.box<HivePipelineListingModel>(pipelineListingBoxName);
  }

  @override
  Future<void> savePipelineListingItem(HivePipelineListingModel pipeline,
      {bool markAsSynced = false}) async {
    final box = getPipelineListingBox();
    if (markAsSynced) {
      pipeline.markAsUpdated();
      pipeline.isSync = true;
    }
    await box.put(pipeline.id, pipeline);

    // Ensure changes are immediately written to disk
    await box.flush();

    // Debug log to verify the pipeline listing item was saved
    debugPrint(
        'HiveService: Pipeline Listing Item ${pipeline.id} saved to box');
  }

  @override
  List<HivePipelineListingModel> getAllPipelineListingItems(
      {bool includeDeleted = false}) {
    final box = getPipelineListingBox();
    final pipelines = box.values.toList();
    if (!includeDeleted) {
      return pipelines.where((pipeline) => !pipeline.isDeleted).toList();
    }
    return pipelines;
  }

  @override
  List<HivePipelineListingModel> getUnsyncedPipelineListingItems() {
    final box = getPipelineListingBox();
    return box.values.where((pipeline) => !pipeline.isSync).toList();
  }

  @override
  Future<void> deletePipelineListingItem(String id,
      {bool permanent = false}) async {
    final box = getPipelineListingBox();
    if (permanent) {
      await box.delete(id);
    } else {
      final pipeline = box.get(id);
      if (pipeline != null) {
        pipeline.markAsDeleted();
        // Save the changes to the box
        await box.put(pipeline.id, pipeline);
        // Ensure changes are immediately written to disk
        await box.flush();
        debugPrint(
            'HiveService: Pipeline Listing Item ${pipeline.id} marked as deleted');
      }
    }
  }

  // Clear all data
  @override
  Future<void> clearAllData() async {
    await getUsersBox().clear();
    await getValvesBox().clear();
    await getConnectorsBox().clear();
    await getPipelinesBox().clear();
    await getSmartMetersBox().clear();
    await getConnectorListingBox().clear();
    await getPipelineListingBox().clear();
    await getCategoriesBox().clear();
    debugPrint('HiveService: All data cleared');
  }

  // Clear only map data (not user list)
  @override
  Future<void> clearMapData() async {
    await getUsersBox().clear();
    await getValvesBox().clear();
    await getConnectorsBox().clear();
    await getPipelinesBox().clear();
    await getSmartMetersBox().clear();
    // Don't clear connector_listing and pipeline_listing as they are separate from map elements
    debugPrint('HiveService: Map data cleared');
  }

  // Register all adapters manually
  void _registerAdapters() {
    // Register all adapters manually
    // This is a temporary solution until build_runner generates the adapters

    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(HiveUserModelAdapter());
    }

    // HiveUserListModelAdapter registration removed - now using only HiveUserModel

    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(HiveValveModelAdapter());
    }

    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(HiveConnectorModelAdapter());
    }

    if (!Hive.isAdapterRegistered(9)) {
      Hive.registerAdapter(HivePipelineModelAdapter());
    }

    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(HiveSmartMeterModelAdapter());
    }

    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(HiveLatLngAdapter());
    }

    if (!Hive.isAdapterRegistered(11)) {
      Hive.registerAdapter(HiveConnectorListingModelAdapter());
    }

    if (!Hive.isAdapterRegistered(12)) {
      Hive.registerAdapter(HivePipelineListingModelAdapter());
    }

    if (!Hive.isAdapterRegistered(8)) {
      Hive.registerAdapter(HiveCategoryModelAdapter());
    }
  }
}
