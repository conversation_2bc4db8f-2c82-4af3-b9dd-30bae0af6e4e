import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart'; // Keep for GeoPoint type

import '../../models/hive_models/hive_user_model.dart';
import '../../models/hive_models/hive_valve_model.dart';
import '../../models/hive_models/hive_connector_model.dart';
import '../../models/hive_models/hive_pipeline_model.dart';
import '../../models/hive_models/hive_connector_listing_model.dart';
import '../../models/hive_models/hive_pipeline_listing_model.dart';
import '../../models/hive_models/hive_smart_meter_model.dart';
import '../../models/hive_models/hive_category_model.dart';
import '../../models/firestore_models.dart';
import '../../repositories/firestore_repository.dart';
import '../connectivity_service.dart';
import '../interfaces/hive_service_interface.dart';
import '../interfaces/sync_service_interface.dart';
import '../sync_service.dart';

class SyncServiceImpl implements SyncServiceInterface {
  final FirestoreRepository _firestoreRepository;
  final ConnectivityService _connectivityService;
  final HiveServiceInterface _hiveService;

  // Stream controller for sync status
  late final StreamController<SyncStatus> _syncStatusController;
  @override
  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;

  // Current sync status
  SyncStatus _status = SyncStatus.idle;
  @override
  SyncStatus get status => _status;

  // Sync statistics
  int _totalItems = 0;
  int _syncedItems = 0;
  int _failedItems = 0;

  // Timer for periodic checks
  Timer? _checkTimer;

  SyncServiceImpl({
    required FirestoreRepository firestoreRepository,
    required ConnectivityService connectivityService,
    required HiveServiceInterface hiveService,
  })  : _firestoreRepository = firestoreRepository,
        _connectivityService = connectivityService,
        _hiveService = hiveService {
    // Initialize the stream controller
    _syncStatusController = StreamController<SyncStatus>.broadcast();

    debugPrint('SyncService initialized with status: $_status');

    // Delay periodic checks to allow Hive to fully initialize
    Future.delayed(const Duration(seconds: 3), () {
      try {
        // Check for unsynced items and update status accordingly
        _updateStatusBasedOnUnsyncedItems();

        // Setup periodic checks for unsynced items
        _setupPeriodicChecks();

        debugPrint('SyncService periodic checks started');
      } catch (e) {
        debugPrint('Error starting sync service periodic checks: $e');
      }
    });
  }

  // Setup periodic checks for unsynced items
  void _setupPeriodicChecks() {
    // We'll use a periodic timer to check for changes
    _checkTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      debugPrint('SyncService: Checking for unsynced items');
      _updateStatusBasedOnUnsyncedItems();
    });
  }

  // Update status based on unsynced items
  Future<void> _updateStatusBasedOnUnsyncedItems() async {
    try {
      final hasUnsynced = await hasUnsyncedItems();
      if (hasUnsynced && _status != SyncStatus.syncing) {
        _updateStatus(SyncStatus.idle);
      } else if (!hasUnsynced && _status != SyncStatus.syncing) {
        _updateStatus(SyncStatus.upToDate);
      }
    } catch (e) {
      debugPrint('Error checking unsynced items: $e');
      // Don't update status if there's an error (boxes might not be ready)
    }
  }

  // Update the sync status
  void _updateStatus(SyncStatus newStatus) {
    if (_status != newStatus) {
      debugPrint('SyncService status changing from $_status to $newStatus');
      _status = newStatus;
      _syncStatusController.add(_status);
    }
  }

  @override
  void forceStatusUpdate() {
    debugPrint('SyncService forcing status update: $_status');
    _syncStatusController.add(_status);
  }

  @override
  Future<bool> hasUnsyncedItems() async {
    try {
      return _hiveService.hasUnsyncedItems();
    } catch (e) {
      debugPrint('Error checking for unsynced items: $e');
      return false; // Return false if boxes aren't ready
    }
  }

  @override
  bool shouldFetchFromFirestore() {
    // Only return true if the local database is empty - this is the most reliable
    // way to detect a fresh installation or reinstallation
    if (_hiveService.isDatabaseEmpty()) {
      debugPrint('🔍 Local database is empty, should fetch from Firestore');
      return true;
    }

    // If database is not empty, don't fetch from Firestore automatically
    // Data should only be synced when the sync button is pressed
    debugPrint(
        '🔍 Local database is not empty, should NOT fetch from Firestore');
    debugPrint('🔍 Data will only be synced when the sync button is pressed');

    return false; // Only fetch if database is empty (fresh installation)
  }

  @override
  Future<bool> fetchAllData() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      debugPrint('⚠️ No connectivity, cannot fetch data from Firestore');
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);
    debugPrint('🔄 Starting to fetch all data from Firestore...');

    try {
      // Clear any existing data if this is a fresh installation
      if (_hiveService.isDatabaseEmpty()) {
        debugPrint(
            '🧹 Database is empty, ensuring it\'s clean before fetching');
        await _hiveService.clearAllData();
      }

      // Fetch all users
      debugPrint('👤 Fetching users from elements collection...');
      final allUsers = await _firestoreRepository.getAllElements('user');
      debugPrint('✅ Found ${allUsers.length} users in elements collection');

      // Only process users from elements collection
      if (allUsers.isEmpty) {
        debugPrint('⚠️ No users found in elements collection');
      } else {
        // Process users from elements collection
        int usersSaved = 0;
        int userErrors = 0;
        debugPrint(
            '🔄 Processing ${allUsers.length} users from elements collection...');
        for (final userDoc in allUsers) {
          try {
            final element = FirestoreElement.fromFirestore(userDoc);
            final hiveUser =
                _firestoreRepository.createHiveUserFromFirestore(element);
            await _hiveService.saveUser(hiveUser, markAsSynced: true);
            usersSaved++;
          } catch (e) {
            debugPrint('❌ Error processing user from elements collection: $e');
            userErrors++;
          }
        }
        debugPrint(
            '✅ Processed users from elements collection: $usersSaved saved, $userErrors errors');
      }

      // Fetch all valves
      debugPrint('🔄 Fetching valves from elements collection...');
      final allValves = await _firestoreRepository.getAllElements('valve');
      debugPrint('✅ Found ${allValves.length} valves in elements collection');

      int valvesSaved = 0;
      int valveErrors = 0;
      for (final valveDoc in allValves) {
        try {
          final element = FirestoreElement.fromFirestore(valveDoc);
          final hiveValve =
              _firestoreRepository.createHiveValveFromFirestore(element);
          await _hiveService.saveValve(hiveValve, markAsSynced: true);
          valvesSaved++;
        } catch (e) {
          debugPrint('❌ Error processing valve: $e');
          valveErrors++;
        }
      }
      debugPrint('✅ Processed valves: $valvesSaved saved, $valveErrors errors');

      // Fetch all connectors
      debugPrint('🔄 Fetching connectors from elements collection...');
      final allConnectors =
          await _firestoreRepository.getAllElements('connector');
      debugPrint(
          '✅ Found ${allConnectors.length} connectors in elements collection');

      int connectorsSaved = 0;
      int connectorErrors = 0;
      for (final connectorDoc in allConnectors) {
        try {
          final element = FirestoreElement.fromFirestore(connectorDoc);
          final hiveConnector =
              _firestoreRepository.createHiveConnectorFromFirestore(element);
          await _hiveService.saveConnector(hiveConnector, markAsSynced: true);
          connectorsSaved++;
        } catch (e) {
          debugPrint('❌ Error processing connector: $e');
          connectorErrors++;
        }
      }
      debugPrint(
          '✅ Processed connectors: $connectorsSaved saved, $connectorErrors errors');

      // Fetch all pipelines from elements collection
      debugPrint('🔄 Fetching pipelines from elements collection...');
      final allPipelineElements =
          await _firestoreRepository.getAllElements('pipeline');
      debugPrint(
          '✅ Found ${allPipelineElements.length} pipeline elements in elements collection');

      // Fetch all pipelines from pipelines collection
      debugPrint('🔄 Fetching pipelines from pipelines collection...');
      final allPipelines = await _firestoreRepository.getAllPipelines();
      debugPrint(
          '✅ Found ${allPipelines.length} pipelines in pipelines collection');

      // Process pipeline elements
      int pipelinesSaved = 0;
      int pipelineErrors = 0;
      debugPrint(
          '🔄 Processing ${allPipelineElements.length} pipelines from elements collection...');
      for (final pipelineDoc in allPipelineElements) {
        try {
          debugPrint('🔄 Processing pipeline document ${pipelineDoc.id}');
          final pipeline = FirestorePipeline.fromFirestore(pipelineDoc);
          final hivePipeline = await _firestoreRepository
              .createHivePipelineFromFirestore(pipeline);
          await _hiveService.savePipeline(hivePipeline, markAsSynced: true);
          pipelinesSaved++;
        } catch (e) {
          debugPrint('❌ Error processing pipeline element: $e');
          pipelineErrors++;
        }
      }
      debugPrint(
          '✅ Processed pipelines from elements: $pipelinesSaved saved, $pipelineErrors errors');

      // Verify data was loaded correctly
      final usersInHive = _hiveService.getAllUsers().length;
      final valvesInHive = _hiveService.getAllValves().length;
      final connectorsInHive = _hiveService.getAllConnectors().length;
      final pipelinesInHive = _hiveService.getAllPipelines().length;

      debugPrint(
          '📊 Data loaded into Hive: $usersInHive users, $valvesInHive valves, '
          '$connectorsInHive connectors, $pipelinesInHive pipelines');

      if (usersInHive == 0 &&
          valvesInHive == 0 &&
          connectorsInHive == 0 &&
          pipelinesInHive == 0) {
        debugPrint('⚠️ Warning: No data was loaded into Hive from Firestore!');
        // If we have data in Firestore but nothing was loaded, something went wrong
        if (allUsers.isNotEmpty ||
            allValves.isNotEmpty ||
            allConnectors.isNotEmpty ||
            allPipelineElements.isNotEmpty) {
          debugPrint(
              '❌ Error: Firestore has data but nothing was loaded into Hive');
          _updateStatus(SyncStatus.failed);
          return false;
        }
      }

      debugPrint('✅ Successfully fetched all data from Firestore');
      _updateStatus(SyncStatus.upToDate);
      return true;
    } catch (e) {
      debugPrint('❌ Error fetching all data from Firestore: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  @override
  Future<bool> syncAllData() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // First, fetch any new data from Firestore
      await _fetchNewDataFromFirestore();

      // Then proceed with uploading local changes
      final unsyncedItems = _hiveService.getAllUnsyncedItems();

      // Debug logging for unsynced items
      debugPrint('🔄 Found ${unsyncedItems.length} unsynced items');

      if (unsyncedItems.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Start syncing
      _updateStatus(SyncStatus.syncing);
      _totalItems = unsyncedItems.length;
      _syncedItems = 0;
      _failedItems = 0;

      // Process items in batches to avoid overloading Firestore
      const batchSize = 10;
      for (var i = 0; i < unsyncedItems.length; i += batchSize) {
        final end = (i + batchSize < unsyncedItems.length)
            ? i + batchSize
            : unsyncedItems.length;
        final batch = unsyncedItems.sublist(i, end);

        // Process batch
        await Future.wait(batch.map((item) => _syncItem(item)));

        // Update progress
        _updateStatus(SyncStatus.syncing);
      }

      // Update final status
      if (_failedItems > 0) {
        if (_syncedItems > 0) {
          _updateStatus(SyncStatus.partialSync);
        } else {
          _updateStatus(SyncStatus.failed);
        }
      } else {
        _updateStatus(SyncStatus.completed);
        // After a short delay, change to upToDate
        Future.delayed(const Duration(seconds: 2), () {
          _updateStatus(SyncStatus.upToDate);
        });
      }

      return _failedItems == 0;
    } catch (e) {
      debugPrint('Error during sync: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  @override
  Future<bool> syncUserList() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all unsynced user list items
      final unsyncedItems = _hiveService.getAllUnsyncedUserListItems();
      debugPrint('🔄 Found ${unsyncedItems.length} unsynced user list items');

      if (unsyncedItems.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Start syncing
      _totalItems = unsyncedItems.length;
      _syncedItems = 0;
      _failedItems = 0;

      // Process each user list item
      for (final item in unsyncedItems) {
        try {
          if (item.isDeleted) {
            // Delete from Firestore - use deleteElement instead of deleteUser
            await _firestoreRepository.deleteElement(item.id);
            // If successful, permanently delete from local storage
            await _hiveService.deleteUserListItem(item.id, permanent: true);
            debugPrint(
                'Deleted user list item ${item.id} from Firestore and local storage');
          } else {
            // Update or create in Firestore
            await _firestoreRepository.addOrUpdateUser(item.toUserModel());
            // Mark as synced
            item.markAsSynced();
            // Save the changes to the box
            await _hiveService.saveUserListItem(item);
            debugPrint('Synced user list item ${item.id} to Firestore');
          }
          _syncedItems++;
        } catch (e) {
          debugPrint('Error syncing user list item: $e');
          _failedItems++;
        }
      }

      // Update final status
      if (_failedItems > 0) {
        if (_syncedItems > 0) {
          _updateStatus(SyncStatus.partialSync);
        } else {
          _updateStatus(SyncStatus.failed);
        }
      } else {
        _updateStatus(SyncStatus.completed);
        // After a short delay, change to upToDate
        Future.delayed(const Duration(seconds: 2), () {
          _updateStatus(SyncStatus.upToDate);
        });
      }

      return _failedItems == 0;
    } catch (e) {
      debugPrint('Error during user list sync: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Sync connector listing items
  @override
  Future<bool> syncConnectorListing() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all unsynced connector listing items
      final unsyncedItems = _hiveService.getUnsyncedConnectorListingItems();
      debugPrint(
          '🔄 Found ${unsyncedItems.length} unsynced connector listing items');

      if (unsyncedItems.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Start syncing
      _totalItems = unsyncedItems.length;
      _syncedItems = 0;
      _failedItems = 0;

      // Process each connector listing item
      for (final item in unsyncedItems) {
        try {
          if (item.isDeleted) {
            // Delete from Firestore
            await _firestoreRepository.deleteElement(item.id);
            // If successful, permanently delete from local storage
            await _hiveService.deleteConnectorListingItem(item.id,
                permanent: true);
            debugPrint(
                'Deleted connector listing item ${item.id} from Firestore and local storage');
          } else {
            // Update or create in Firestore
            await _firestoreRepository.addConnectorListingItem(item.toJson());
            // Mark as synced
            item.isSync = true;
            // Save the changes to the box
            await _hiveService.saveConnectorListingItem(item);
            debugPrint('Synced connector listing item ${item.id} to Firestore');
          }
          _syncedItems++;
        } catch (e) {
          debugPrint('Error syncing connector listing item: $e');
          _failedItems++;
        }
      }

      // Update final status
      if (_failedItems > 0) {
        if (_syncedItems > 0) {
          _updateStatus(SyncStatus.partialSync);
        } else {
          _updateStatus(SyncStatus.failed);
        }
      } else {
        _updateStatus(SyncStatus.completed);
        // After a short delay, change to upToDate
        Future.delayed(const Duration(seconds: 2), () {
          _updateStatus(SyncStatus.upToDate);
        });
      }

      return _failedItems == 0;
    } catch (e) {
      debugPrint('Error during connector listing sync: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Sync pipeline listing items
  @override
  Future<bool> syncPipelineListing() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all unsynced pipeline listing items
      final unsyncedItems = _hiveService.getUnsyncedPipelineListingItems();
      debugPrint(
          '🔄 Found ${unsyncedItems.length} unsynced pipeline listing items');

      if (unsyncedItems.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Start syncing
      _totalItems = unsyncedItems.length;
      _syncedItems = 0;
      _failedItems = 0;

      // Process each pipeline listing item
      for (final item in unsyncedItems) {
        try {
          if (item.isDeleted) {
            // Delete from Firestore
            await _firestoreRepository.deleteElement(item.id);
            // If successful, permanently delete from local storage
            await _hiveService.deletePipelineListingItem(item.id,
                permanent: true);
            debugPrint(
                'Deleted pipeline listing item ${item.id} from Firestore and local storage');
          } else {
            // Update or create in Firestore
            await _firestoreRepository.addPipelineListingItem(item.toJson());
            // Mark as synced
            item.isSync = true;
            // Save the changes to the box
            await _hiveService.savePipelineListingItem(item);
            debugPrint('Synced pipeline listing item ${item.id} to Firestore');
          }
          _syncedItems++;
        } catch (e) {
          debugPrint('Error syncing pipeline listing item: $e');
          _failedItems++;
        }
      }

      // Update final status
      if (_failedItems > 0) {
        if (_syncedItems > 0) {
          _updateStatus(SyncStatus.partialSync);
        } else {
          _updateStatus(SyncStatus.failed);
        }
      } else {
        _updateStatus(SyncStatus.completed);
        // After a short delay, change to upToDate
        Future.delayed(const Duration(seconds: 2), () {
          _updateStatus(SyncStatus.upToDate);
        });
      }

      return _failedItems == 0;
    } catch (e) {
      debugPrint('Error during pipeline listing sync: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Sync smart meter listing items
  @override
  Future<bool> syncSmartMeterListing() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all unsynced smart meter items
      final unsyncedItems = _hiveService
          .getAllSmartMeters()
          .where((meter) => !meter.isSync)
          .toList();
      debugPrint('🔄 Found ${unsyncedItems.length} unsynced smart meter items');

      if (unsyncedItems.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Start syncing
      _totalItems = unsyncedItems.length;
      _syncedItems = 0;
      _failedItems = 0;

      // Process each smart meter item
      for (final item in unsyncedItems) {
        try {
          if (item.isDeleted) {
            // Delete from Firestore
            await _firestoreRepository.deleteElement(item.id);
            // If successful, permanently delete from local storage
            await _hiveService.deleteSmartMeter(item.id, permanent: true);
            debugPrint(
                'Deleted smart meter ${item.id} from Firestore and local storage');
          } else {
            // Create LatLng position
            final position = LatLng(item.latitude, item.longitude);

            // Add to elements collection if it has a valid position
            if (item.latitude != 0 && item.longitude != 0) {
              await _firestoreRepository.addElement('smartMeter', position,
                  item.areaId, item.title, {'type': 'smartMeter'});
            }

            // Add to smart_meters collection
            final specifications = {
              'type': item.type,
              'reading': item.reading,
            };
            await _firestoreRepository.addSmartMeterDetails(
                item.id, specifications);

            // Mark as synced
            item.isSync = true;
            // Save the changes to the box
            await _hiveService.saveSmartMeter(item);
            debugPrint('Synced smart meter ${item.id} to Firestore');
          }
          _syncedItems++;
        } catch (e) {
          debugPrint('Error syncing smart meter: $e');
          _failedItems++;
        }
      }

      // Update final status
      if (_failedItems > 0) {
        if (_syncedItems > 0) {
          _updateStatus(SyncStatus.partialSync);
        } else {
          _updateStatus(SyncStatus.failed);
        }
      } else {
        _updateStatus(SyncStatus.completed);
        // After a short delay, change to upToDate
        Future.delayed(const Duration(seconds: 2), () {
          _updateStatus(SyncStatus.upToDate);
        });
      }

      return _failedItems == 0;
    } catch (e) {
      debugPrint('Error during smart meter sync: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Fetch connector listing from Firestore
  @override
  Future<bool> fetchConnectorListing() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all connector listing items from Firestore
      final connectorDocs =
          await _firestoreRepository.getAllConnectorListingItems();
      debugPrint(
          '🔄 Found ${connectorDocs.length} connector listing items in Firestore');

      if (connectorDocs.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Process each connector listing item
      for (final doc in connectorDocs) {
        try {
          final data = doc.data() as Map<String, dynamic>;

          // Create a HiveConnectorListingModel from the Firestore data
          final connector = HiveConnectorListingModel(
            id: data['id'] as String,
            title: data['title'] as String,
            connectorType: data['connectorType'] as String,
            specifications: (data['specifications'] as Map<String, dynamic>)
                .cast<String, dynamic>(),
            isSync: true,
            isDeleted: false,
            updatedAt: data['updatedAt'] as int? ??
                DateTime.now().millisecondsSinceEpoch,
          );

          // Save to Hive
          await _hiveService.saveConnectorListingItem(connector);
          debugPrint(
              'Fetched and saved connector listing item ${connector.id} from Firestore');
        } catch (e) {
          debugPrint('Error processing connector listing item: $e');
        }
      }

      _updateStatus(SyncStatus.upToDate);
      return true;
    } catch (e) {
      debugPrint('Error fetching connector listing from Firestore: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Fetch pipeline listing from Firestore
  @override
  Future<bool> fetchPipelineListing() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all pipeline listing items from Firestore
      final pipelineDocs =
          await _firestoreRepository.getAllPipelineListingItems();
      debugPrint(
          '🔄 Found ${pipelineDocs.length} pipeline listing items in Firestore');

      if (pipelineDocs.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Process each pipeline listing item
      for (final doc in pipelineDocs) {
        try {
          final data = doc.data() as Map<String, dynamic>;

          // Create a HivePipelineListingModel from the Firestore data
          final pipeline = HivePipelineListingModel(
            id: data['id'] as String,
            title: data['title'] as String,
            pipeType: data['pipeType'] as String,
            pipeDiameter: data['pipeDiameter'] as String,
            state: data['state'] as String,
            properties: (data['properties'] as Map<String, dynamic>)
                .cast<String, dynamic>(),
            isSync: true,
            isDeleted: false,
            updatedAt: data['updatedAt'] as int? ??
                DateTime.now().millisecondsSinceEpoch,
          );

          // Save to Hive
          await _hiveService.savePipelineListingItem(pipeline);
          debugPrint(
              'Fetched and saved pipeline listing item ${pipeline.id} from Firestore');
        } catch (e) {
          debugPrint('Error processing pipeline listing item: $e');
        }
      }

      _updateStatus(SyncStatus.upToDate);
      return true;
    } catch (e) {
      debugPrint('Error fetching pipeline listing from Firestore: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Fetch smart meter listing from Firestore
  @override
  Future<bool> fetchSmartMeterListing() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all smart meters from Firestore elements collection
      final smartMeterElements =
          await _firestoreRepository.getAllElements('smartMeter');
      debugPrint(
          '🔄 Found ${smartMeterElements.length} smart meters in Firestore elements collection');

      if (smartMeterElements.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Process each smart meter
      for (final element in smartMeterElements) {
        try {
          // Get the data from the document
          final data = element.data() as Map<String, dynamic>;
          final position = data['position'] as Map<String, dynamic>;

          // Get the smart meter details from the smart_meters collection
          final details = await _firestoreRepository.getElementDetails(
              element.id, 'smartMeter') as Map<String, dynamic>?;

          if (details != null) {
            // Create a HiveSmartMeterModel from the Firestore data
            final smartMeter = HiveSmartMeterModel(
              id: element.id,
              latitude: position['latitude'] as double,
              longitude: position['longitude'] as double,
              title: data['title'] as String,
              areaId: data['areaId'] as String? ?? 'default',
              type: details['type'] as String? ?? 'Standard',
              reading: details['reading'] as String? ?? '0',
              status: details['status'] as String? ?? 'Active',
              lastReading: details['lastReading'] != null
                  ? DateTime.fromMillisecondsSinceEpoch(
                      details['lastReading'] as int)
                  : null,
              isSync: true,
              isDeleted: false,
              createdAt: DateTime.fromMillisecondsSinceEpoch(
                  data['createdAt'] as int? ??
                      DateTime.now().millisecondsSinceEpoch),
              updatedAt: DateTime.fromMillisecondsSinceEpoch(
                  data['lastUpdated'] as int? ??
                      DateTime.now().millisecondsSinceEpoch),
            );

            // Save to Hive
            await _hiveService.saveSmartMeter(smartMeter);
            debugPrint(
                'Fetched and saved smart meter ${smartMeter.id} from Firestore');
          }
        } catch (e) {
          debugPrint('Error processing smart meter: $e');
        }
      }

      _updateStatus(SyncStatus.upToDate);
      return true;
    } catch (e) {
      debugPrint('Error fetching smart meters from Firestore: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  @override
  Future<bool> clearAndFetchAllData() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);
    debugPrint('🔄 Clearing all data and fetching from Firestore...');

    try {
      // Clear all data
      await _hiveService.clearAllData();
      debugPrint('✅ Cleared all data from local storage');

      // Fetch all data from Firestore
      return await fetchAllData();
    } catch (e) {
      debugPrint('❌ Error clearing and fetching all data: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Sync a single item
  Future<void> _syncItem(dynamic item) async {
    try {
      // Handle different types of items
      if (item is HiveUserModel) {
        if (item.isDeleted) {
          // Delete from Firestore
          await _firestoreRepository.deleteElement(item.id);
          // If successful, permanently delete from local storage
          await _hiveService.deleteUser(item.id, permanent: true);
          debugPrint(
              'Deleted user ${item.id} from Firestore and local storage');
        } else {
          // Update or create in Firestore
          await _syncUser(item);
          // Mark as synced
          item.markAsSynced();
          // Save the changes to the box
          await _hiveService.saveUser(item);
          debugPrint('Synced user ${item.id} to Firestore');
        }
      } else if (item is HiveValveModel) {
        if (item.isDeleted) {
          await _firestoreRepository.deleteElement(item.id);
          await _hiveService.deleteValve(item.id, permanent: true);
          debugPrint(
              'Deleted valve ${item.id} from Firestore and local storage');
        } else {
          await _syncValve(item);
          item.markAsSynced();
          // Save the changes to the box
          await _hiveService.saveValve(item);
          debugPrint('Synced valve ${item.id} to Firestore');
        }
      } else if (item is HiveConnectorModel) {
        if (item.isDeleted) {
          await _firestoreRepository.deleteElement(item.id);
          await _hiveService.deleteConnector(item.id, permanent: true);
          debugPrint(
              'Deleted connector ${item.id} from Firestore and local storage');
        } else {
          await _syncConnector(item);
          item.markAsSynced();
          // Save the changes to the box
          await _hiveService.saveConnector(item);
          debugPrint('Synced connector ${item.id} to Firestore');
        }
      } else if (item is HivePipelineModel) {
        if (item.isDeleted) {
          await _firestoreRepository.deleteElement(item.id);
          await _hiveService.deletePipeline(item.id, permanent: true);
          debugPrint(
              'Deleted pipeline ${item.id} from Firestore and local storage');
        } else {
          await _syncPipeline(item);
          item.markAsSynced();
          // Save the changes to the box
          await _hiveService.savePipeline(item);
          debugPrint('Synced pipeline ${item.id} to Firestore');
        }
      }

      _syncedItems++;
    } catch (e) {
      debugPrint('Error syncing item: $e');
      _failedItems++;
    }
  }

  // Sync a user
  Future<void> _syncUser(HiveUserModel user) async {
    // Check if user exists in elements collection
    final existsInElements = await _firestoreRepository.elementExists(user.id);

    // Check if user exists in user collection
    final existsInUserCollection =
        await _firestoreRepository.userExists(user.id);

    // Update or create in elements collection
    if (existsInElements) {
      // Update existing user in elements collection with minimal data
      debugPrint('Updating existing user ${user.id} in elements collection');

      // Update metadata
      await _firestoreRepository.updateElementMetadata(user.id, user.title, {
        // Include only the user ID for reference when clicking on the map
        'userId': user.id,
      });

      // Update position
      await _firestoreRepository.updateElementPosition(
        user.id,
        LatLng(user.latitude, user.longitude),
      );
    } else {
      // Create new user in elements collection
      debugPrint('Creating new user ${user.id} in elements collection');
      await _firestoreRepository.addElementWithId(
        user.id,
        'user',
        LatLng(user.latitude, user.longitude),
        user.areaId,
        user.title,
        {
          // Include only the user ID for reference when clicking on the map
          'userId': user.id,
        },
      );
    }

    // Always update the position in the user collection regardless of whether the user exists in elements
    // This ensures that when a user is added to the map, their position is updated in the user collection
    final positionUpdates = {
      'latitude': user.latitude,
      'longitude': user.longitude,
      'updatedAt': DateTime.now().millisecondsSinceEpoch,
    };

    // Update or create in user collection
    if (existsInUserCollection) {
      // Update existing user in user collection
      // Only update the position in the user collection, preserving other details
      debugPrint('Updating user ${user.id} position in user collection');

      try {
        // Use the FirestoreRepository to update only the position fields
        await _firestoreRepository.updateUserPosition(user.id, positionUpdates);
        debugPrint(
            'Successfully updated user ${user.id} position in user collection');
      } catch (e) {
        debugPrint('Error updating user position in user collection: $e');
        // Fall back to updating the entire user model if the partial update fails
        await _firestoreRepository.updateUser(user.toUserModel());
      }
    } else {
      // Create new user in user collection
      debugPrint('Creating new user ${user.id} in user collection');
      await _firestoreRepository.addUser(user.toUserModel());
    }
  }

  // Sync a valve
  Future<void> _syncValve(HiveValveModel valve) async {
    // Check if valve exists in elements collection
    final existsInElements = await _firestoreRepository.elementExists(valve.id);

    // Update or create in elements collection
    if (existsInElements) {
      // Update existing valve in elements collection
      debugPrint('Updating existing valve ${valve.id} in elements collection');

      // Update metadata
      await _firestoreRepository.updateElementMetadata(valve.id, valve.title, {
        'type': valve.type,
        'status': valve.status,
      });

      // Update position
      await _firestoreRepository.updateElementPosition(
        valve.id,
        LatLng(valve.latitude, valve.longitude),
      );
    } else {
      // Create new valve in elements collection
      debugPrint('Creating new valve ${valve.id} in elements collection');
      await _firestoreRepository.addElementWithId(
        valve.id,
        'valve',
        LatLng(valve.latitude, valve.longitude),
        valve.areaId,
        valve.title,
        {
          'type': valve.type,
          'status': valve.status,
        },
      );
    }
  }

  // Sync a connector
  Future<void> _syncConnector(HiveConnectorModel connector) async {
    // Check if connector exists in elements collection
    final existsInElements =
        await _firestoreRepository.elementExists(connector.id);

    // Update or create in elements collection
    if (existsInElements) {
      // Update existing connector in elements collection
      debugPrint(
          'Updating existing connector ${connector.id} in elements collection');

      // Update metadata
      await _firestoreRepository
          .updateElementMetadata(connector.id, connector.title, {
        'connectorType': connector.connectorType,
      });

      // Update position
      await _firestoreRepository.updateElementPosition(
        connector.id,
        LatLng(connector.latitude, connector.longitude),
      );
    } else {
      // Create new connector in elements collection
      debugPrint(
          'Creating new connector ${connector.id} in elements collection');
      await _firestoreRepository.addElementWithId(
        connector.id,
        'connector',
        LatLng(connector.latitude, connector.longitude),
        connector.areaId,
        connector.title,
        {
          'connectorType': connector.connectorType,
        },
      );
    }
  }

  // Sync a pipeline
  Future<void> _syncPipeline(HivePipelineModel pipeline) async {
    try {
      debugPrint('🔄 Starting to sync pipeline ${pipeline.id}');
      debugPrint(
          'Pipeline sync status: ${pipeline.isSync ? "Synced" : "Not synced"}');
      debugPrint('Pipeline points: ${pipeline.points.length}');
      debugPrint('Pipeline properties: ${pipeline.properties}');

      // Log the pipeline state specifically
      final state = pipeline.properties['state'] ?? 'Active';
      debugPrint('🔄 Pipeline state to sync: $state (from properties)');
      debugPrint('🔄 Pipeline updatedAt: ${pipeline.updatedAt}');

      // Check if pipeline exists in elements collection
      final existsInElements =
          await _firestoreRepository.elementExists(pipeline.id);

      // Convert HiveLatLng to LatLng for Firestore
      final latLngPoints = pipeline.points
          .map((point) => LatLng(point.latitude, point.longitude))
          .toList();

      // Update or create in elements collection
      if (existsInElements) {
        // Update existing pipeline in elements collection
        debugPrint(
            'Updating existing pipeline ${pipeline.id} in elements collection');
        debugPrint('🔄 Setting state in elements collection to: $state');
        await _firestoreRepository
            .updatePipelineElement(pipeline.id, latLngPoints, state: state);

        // Explicitly update the state in the elements collection
        debugPrint('🔄 Explicitly updating state in elements collection');
        await _firestoreRepository.updateElementState(pipeline.id, state);
      } else {
        // Create new pipeline in elements collection with the same ID as in local storage
        debugPrint(
            'Creating new pipeline ${pipeline.id} in elements collection');
        debugPrint('🔄 Setting state in new pipeline element to: $state');
        // Pass properties directly to addPipelineElementWithId to ensure they're added to both collections
        await _firestoreRepository.addPipelineElementWithId(
            pipeline.id, latLngPoints, pipeline.areaId, pipeline.title,
            properties: pipeline.properties, state: state);
      }

      // Convert pipeline to Firestore details
      final pipelineDetails = pipeline.toFirestoreDetails();

      // Check if pipeline exists in pipelines collection
      final existsInPipelinesCollection =
          await _firestoreRepository.pipelineExists(pipeline.id);

      // Update or create in pipelines collection
      if (existsInPipelinesCollection) {
        // Update existing pipeline in pipelines collection
        debugPrint(
            'Updating existing pipeline ${pipeline.id} in pipelines collection');
        await _firestoreRepository.updatePipelineDetailsWithFullData(
            pipeline.id, pipelineDetails);
      } else {
        // Add the pipeline details to Firestore
        await _firestoreRepository.addPipelineDetailsWithFullData(
            pipeline.id, pipelineDetails);
      }
    } catch (e) {
      debugPrint('❌ Error syncing pipeline: $e');
      rethrow;
    }
  }

  // Fetch new data from Firestore
  Future<void> _fetchNewDataFromFirestore() async {
    try {
      // Get the latest update time from local storage
      final latestUpdateTime = await _getLatestUpdateTime();
      debugPrint('Latest update time: $latestUpdateTime');

      // If we have no data, don't try to fetch updates
      if (latestUpdateTime == null) {
        debugPrint('No data in local storage, skipping update fetch');
        return;
      }

      // Fetch updated data from Firestore
      final timestamp = latestUpdateTime.millisecondsSinceEpoch;
      debugPrint('Fetching items updated after timestamp: $timestamp');

      // Fetch users
      final updatedUsers = await _firestoreRepository.getElementsUpdatedAfter(
        'user',
        timestamp,
      );

      for (final userDoc in updatedUsers) {
        final element = FirestoreElement.fromFirestore(userDoc);
        final hiveUser =
            _firestoreRepository.createHiveUserFromFirestore(element);
        await _hiveService.saveUser(hiveUser, markAsSynced: true);
      }

      // Fetch valves
      final updatedValves = await _firestoreRepository.getElementsUpdatedAfter(
        'valve',
        timestamp,
      );
      for (final valveDoc in updatedValves) {
        final element = FirestoreElement.fromFirestore(valveDoc);
        final hiveValve =
            _firestoreRepository.createHiveValveFromFirestore(element);
        await _hiveService.saveValve(hiveValve, markAsSynced: true);
      }

      // Fetch connectors
      final updatedConnectors =
          await _firestoreRepository.getElementsUpdatedAfter(
        'connector',
        timestamp,
      );
      for (final connectorDoc in updatedConnectors) {
        final element = FirestoreElement.fromFirestore(connectorDoc);
        final hiveConnector =
            _firestoreRepository.createHiveConnectorFromFirestore(element);
        await _hiveService.saveConnector(hiveConnector, markAsSynced: true);
      }

      // Fetch pipelines
      final updatedPipelines =
          await _firestoreRepository.getPipelinesUpdatedAfter(
        timestamp,
      );
      for (final pipelineDoc in updatedPipelines) {
        final pipeline = FirestorePipeline.fromFirestore(pipelineDoc);
        final hivePipeline = await _firestoreRepository
            .createHivePipelineFromFirestore(pipeline);
        await _hiveService.savePipeline(hivePipeline, markAsSynced: true);
      }

      debugPrint('Fetched and saved updates from server: '
          '${updatedUsers.length} users, '
          '${updatedValves.length} valves, '
          '${updatedConnectors.length} connectors, '
          '${updatedPipelines.length} pipelines');
    } catch (e) {
      debugPrint('Error fetching new data from Firestore: $e');
      rethrow;
    }
  }

  // Get the latest update time from local storage
  Future<DateTime?> _getLatestUpdateTime() async {
    DateTime? latestTime;

    // Check users
    for (final user in _hiveService.getAllUsers()) {
      if (latestTime == null || user.updatedAt.isAfter(latestTime)) {
        latestTime = user.updatedAt;
      }
    }

    // Check valves
    for (final valve in _hiveService.getAllValves()) {
      if (latestTime == null || valve.updatedAt.isAfter(latestTime)) {
        latestTime = valve.updatedAt;
      }
    }

    // Check connectors
    for (final connector in _hiveService.getAllConnectors()) {
      if (latestTime == null || connector.updatedAt.isAfter(latestTime)) {
        latestTime = connector.updatedAt;
      }
    }

    // Check pipelines
    for (final pipeline in _hiveService.getAllPipelines()) {
      if (latestTime == null || pipeline.updatedAt.isAfter(latestTime)) {
        latestTime = pipeline.updatedAt;
      }
    }

    return latestTime;
  }

  @override
  void dispose() {
    _checkTimer?.cancel();
    _syncStatusController.close();
  }

  // Fetch categories from Firestore
  @override
  Future<bool> fetchCategories() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all categories from Firestore
      final categoriesData = await _firestoreRepository.getAllCategories();
      debugPrint('🔄 Found ${categoriesData.length} categories in Firestore');

      if (categoriesData.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Process each category
      int itemsSaved = 0;
      int itemErrors = 0;
      for (final data in categoriesData) {
        try {
          final category = HiveCategoryModel.fromJson(data);
          await _hiveService.saveCategory(category, markAsSynced: true);
          itemsSaved++;
        } catch (e) {
          debugPrint('❌ Error processing category: $e');
          itemErrors++;
        }
      }

      debugPrint(
          '✅ Processed categories: $itemsSaved saved, $itemErrors errors');

      _updateStatus(SyncStatus.upToDate);
      return true;
    } catch (e) {
      debugPrint('❌ Error fetching categories from Firestore: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Sync categories to Firestore
  @override
  Future<bool> syncCategories() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // Get all unsynced categories
      final unsyncedItems = _hiveService.getUnsyncedCategories();
      debugPrint('🔄 Found ${unsyncedItems.length} unsynced categories');

      if (unsyncedItems.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Start syncing
      _totalItems = unsyncedItems.length;
      _syncedItems = 0;
      _failedItems = 0;

      // Process each category
      for (final item in unsyncedItems) {
        try {
          if (item.isDeleted) {
            // Delete from Firestore
            await _firestoreRepository.deleteCategory(item.id);
            // If successful, permanently delete from local storage
            await _hiveService.deleteCategory(item.id, permanent: true);
            debugPrint(
                'Deleted category ${item.id} from Firestore and local storage');
          } else {
            // Update or create in Firestore
            await _firestoreRepository.addCategory(item.toJson());
            // Mark as synced
            item.isSync = true;
            // Save the changes to the box
            await _hiveService.saveCategory(item);
            debugPrint('Synced category ${item.id} to Firestore');
          }
          _syncedItems++;
        } catch (e) {
          debugPrint('Error syncing category: $e');
          _failedItems++;
        }
      }

      // Update final status
      if (_failedItems > 0) {
        if (_syncedItems > 0) {
          _updateStatus(SyncStatus.partialSync);
        } else {
          _updateStatus(SyncStatus.failed);
        }
      } else {
        _updateStatus(SyncStatus.completed);
        // After a short delay, change to upToDate
        Future.delayed(const Duration(seconds: 2), () {
          _updateStatus(SyncStatus.upToDate);
        });
      }

      return _failedItems == 0;
    } catch (e) {
      debugPrint('Error during category sync: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }
}
