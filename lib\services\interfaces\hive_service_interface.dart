import 'package:hive_flutter/hive_flutter.dart';
import '../../models/hive_models/hive_user_model.dart';
import '../../models/hive_models/hive_user_list_model.dart';
import '../../models/hive_models/hive_valve_model.dart';
import '../../models/hive_models/hive_connector_model.dart';
import '../../models/hive_models/hive_pipeline_model.dart';
import '../../models/hive_models/hive_smart_meter_model.dart';
import '../../models/hive_models/hive_connector_listing_model.dart';
import '../../models/hive_models/hive_pipeline_listing_model.dart';
import '../../models/hive_models/hive_category_model.dart';

/// Interface for the HiveService
abstract class HiveServiceInterface {
  /// Initialize Hive and open all boxes
  Future<void> init();

  /// Get the users box
  Box<HiveUserModel> getUsersBox();

  /// Save a user to the box
  Future<void> saveUser(HiveUserModel user, {bool markAsSynced});

  /// Get all users
  List<HiveUserModel> getAllUsers({bool includeDeleted});

  /// Get all unsynced users
  List<HiveUserModel> getUnsyncedUsers();

  /// Delete a user
  Future<void> deleteUser(String id, {bool permanent});

  /// Get the valves box
  Box<HiveValveModel> getValvesBox();

  /// Save a valve to the box
  Future<void> saveValve(HiveValveModel valve, {bool markAsSynced});

  /// Get all valves
  List<HiveValveModel> getAllValves({bool includeDeleted});

  /// Get all unsynced valves
  List<HiveValveModel> getUnsyncedValves();

  /// Delete a valve
  Future<void> deleteValve(String id, {bool permanent});

  /// Get the connectors box
  Box<HiveConnectorModel> getConnectorsBox();

  /// Save a connector to the box
  Future<void> saveConnector(HiveConnectorModel connector, {bool markAsSynced});

  /// Get all connectors
  List<HiveConnectorModel> getAllConnectors({bool includeDeleted});

  /// Get all unsynced connectors
  List<HiveConnectorModel> getUnsyncedConnectors();

  /// Delete a connector
  Future<void> deleteConnector(String id, {bool permanent});

  /// Get the pipelines box
  Box<HivePipelineModel> getPipelinesBox();

  /// Save a pipeline to the box
  Future<void> savePipeline(HivePipelineModel pipeline, {bool markAsSynced});

  /// Get all pipelines
  List<HivePipelineModel> getAllPipelines({bool includeDeleted});

  /// Get a specific pipeline by ID
  HivePipelineModel? getPipeline(String id);

  /// Get all unsynced pipelines
  List<HivePipelineModel> getUnsyncedPipelines();

  /// Delete a pipeline
  Future<void> deletePipeline(String id, {bool permanent});

  /// Get the user list box
  Box<HiveUserListModel> getUserListBox();

  /// Save a user list item to the box
  Future<void> saveUserListItem(HiveUserListModel user, {bool markAsSynced});

  /// Get all user list items
  List<HiveUserListModel> getAllUserListItems({bool includeDeleted});

  /// Get all unsynced user list items
  List<HiveUserListModel> getUnsyncedUserListItems();

  /// Delete a user list item
  Future<void> deleteUserListItem(String id, {bool permanent});

  /// Get the smart meters box
  Box<HiveSmartMeterModel> getSmartMetersBox();

  /// Save a smart meter to the box
  Future<void> saveSmartMeter(HiveSmartMeterModel smartMeter,
      {bool markAsSynced});

  /// Get all smart meters
  List<HiveSmartMeterModel> getAllSmartMeters({bool includeDeleted});

  /// Get all unsynced smart meters
  List<HiveSmartMeterModel> getUnsyncedSmartMeters();

  /// Delete a smart meter
  Future<void> deleteSmartMeter(String id, {bool permanent});

  /// Get all unsynced items for map elements only (not including user list)
  List<dynamic> getAllUnsyncedItems();

  /// Get all unsynced user list items (separate from map elements)
  List<HiveUserListModel> getAllUnsyncedUserListItems();

  /// Check if there are any unsynced items
  bool hasUnsyncedItems();

  /// Check if the local database is empty (no data in any box)
  bool isDatabaseEmpty();

  /// Check if there are any unsynced user list items
  bool hasUnsyncedUserListItems();

  /// Clear all data
  Future<void> clearAllData();

  /// Clear only map data (not user list)
  Future<void> clearMapData();

  /// Get the connector listing box
  Box<HiveConnectorListingModel> getConnectorListingBox();

  /// Save a connector listing item to the box
  Future<void> saveConnectorListingItem(HiveConnectorListingModel connector,
      {bool markAsSynced});

  /// Get all connector listing items
  List<HiveConnectorListingModel> getAllConnectorListingItems(
      {bool includeDeleted});

  /// Get all unsynced connector listing items
  List<HiveConnectorListingModel> getUnsyncedConnectorListingItems();

  /// Delete a connector listing item
  Future<void> deleteConnectorListingItem(String id, {bool permanent});

  /// Get the pipeline listing box
  Box<HivePipelineListingModel> getPipelineListingBox();

  /// Save a pipeline listing item to the box
  Future<void> savePipelineListingItem(HivePipelineListingModel pipeline,
      {bool markAsSynced});

  /// Get all pipeline listing items
  List<HivePipelineListingModel> getAllPipelineListingItems(
      {bool includeDeleted});

  /// Get all unsynced pipeline listing items
  List<HivePipelineListingModel> getUnsyncedPipelineListingItems();

  /// Delete a pipeline listing item
  Future<void> deletePipelineListingItem(String id, {bool permanent});

  /// Get the categories box
  Box<HiveCategoryModel> getCategoriesBox();

  /// Save a category to the box
  Future<void> saveCategory(HiveCategoryModel category, {bool markAsSynced});

  /// Get all categories
  List<HiveCategoryModel> getAllCategories({bool includeDeleted});

  /// Get all unsynced categories
  List<HiveCategoryModel> getUnsyncedCategories();

  /// Delete a category
  Future<void> deleteCategory(String id, {bool permanent});
}
