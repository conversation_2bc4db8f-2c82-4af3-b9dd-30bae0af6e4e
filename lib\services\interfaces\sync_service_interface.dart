import 'dart:async';
import '../sync_service.dart';

/// Interface for the SyncService
abstract class SyncServiceInterface {
  /// Get the current sync status
  SyncStatus get status;

  /// Get the sync status stream
  Stream<SyncStatus> get syncStatus;

  /// Force a status update
  void forceStatusUpdate();

  /// Check if there are any unsynced items
  Future<bool> hasUnsyncedItems();

  /// Sync all data to Firestore
  Future<bool> syncAllData();

  /// Sync user list to Firestore
  Future<bool> syncUserList();

  /// Sync connector listing to Firestore
  Future<bool> syncConnectorListing();

  /// Sync pipeline listing to Firestore
  Future<bool> syncPipelineListing();

  /// Sync smart meter listing to Firestore
  Future<bool> syncSmartMeterListing();

  /// Sync categories to Firestore
  Future<bool> syncCategories();

  /// Fetch connector listing from Firestore
  Future<bool> fetchConnectorListing();

  /// Fetch pipeline listing from Firestore
  Future<bool> fetchPipelineListing();

  /// Fetch smart meter listing from Firestore
  Future<bool> fetchSmartMeterListing();

  /// Fetch categories from Firestore
  Future<bool> fetchCategories();

  /// Fetch all data from Firestore
  Future<bool> fetchAllData();

  /// Clear all data and fetch from Firestore
  Future<bool> clearAndFetchAllData();

  /// Check if data should be fetched from Firestore
  bool shouldFetchFromFirestore();

  /// Dispose resources
  void dispose();
}
