import 'package:flutter/material.dart';
import 'package:gis/services/hive_service.dart';
import 'package:gis/test_user_position.dart';
import 'package:path_provider/path_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Hive
  final appDocumentDir = await getApplicationDocumentsDirectory();
  //await HiveService.init(appDocumentDir.path);
  
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test User Position',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const TestUserPosition(),
    );
  }
}
