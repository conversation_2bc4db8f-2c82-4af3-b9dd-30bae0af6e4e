import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gis/services/hive_service.dart';
import 'package:gis/models/hive_models/hive_user_model.dart';
import 'package:uuid/uuid.dart';

class TestUserPosition extends StatefulWidget {
  const TestUserPosition({Key? key}) : super(key: key);

  @override
  State<TestUserPosition> createState() => _TestUserPositionState();
}

class _TestUserPositionState extends State<TestUserPosition> {
  String? userId;
  double? latitude;
  double? longitude;
  String statusMessage = '';

  @override
  void initState() {
    super.initState();
    _createTestUser();
  }

  Future<void> _createTestUser() async {
    try {
      // Create a test user
      final id = 'test_user_${const Uuid().v4()}';
      final user = HiveUserModel(
        id: id,
        latitude: 27.7172,
        longitude: 85.3240,
        title: 'Test User',
        areaId: 'default',
      );

      // Save the user to Hive
      await HiveService.saveUser(user);

      // Update state
      setState(() {
        userId = id;
        latitude = user.latitude;
        longitude = user.longitude;
        statusMessage = 'Test user created with ID: $id';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'Error creating test user: $e';
      });
    }
  }

  Future<void> _updateUserPosition() async {
    if (userId == null) {
      setState(() {
        statusMessage = 'No test user available';
      });
      return;
    }

    try {
      // Get the user from Hive
      final user = HiveService.getUsersBox().get(userId!);
      if (user == null) {
        setState(() {
          statusMessage = 'User not found in Hive';
        });
        return;
      }

      // Log the current position
      debugPrint('BEFORE UPDATE: User $userId position: (${user.latitude}, ${user.longitude})');

      // Update the position
      final newLatitude = user.latitude + 0.001;
      final newLongitude = user.longitude + 0.001;
      
      user.latitude = newLatitude;
      user.longitude = newLongitude;
      user.markAsUpdated();
      
      // Save the user
      await HiveService.saveUser(user);

      // Verify the update
      final updatedUser = HiveService.getUsersBox().get(userId!);
      if (updatedUser != null) {
        debugPrint('AFTER UPDATE: User $userId position: (${updatedUser.latitude}, ${updatedUser.longitude})');
        
        setState(() {
          latitude = updatedUser.latitude;
          longitude = updatedUser.longitude;
          statusMessage = 'User position updated to: (${updatedUser.latitude}, ${updatedUser.longitude})';
        });
      } else {
        setState(() {
          statusMessage = 'Failed to retrieve updated user';
        });
      }
    } catch (e) {
      setState(() {
        statusMessage = 'Error updating user position: $e';
      });
    }
  }

  Future<void> _verifyUserPosition() async {
    if (userId == null) {
      setState(() {
        statusMessage = 'No test user available';
      });
      return;
    }

    try {
      // Get the user from Hive
      final user = HiveService.getUsersBox().get(userId!);
      if (user == null) {
        setState(() {
          statusMessage = 'User not found in Hive';
        });
        return;
      }

      setState(() {
        latitude = user.latitude;
        longitude = user.longitude;
        statusMessage = 'Current user position: (${user.latitude}, ${user.longitude})';
      });
    } catch (e) {
      setState(() {
        statusMessage = 'Error verifying user position: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test User Position'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('User ID: ${userId ?? 'None'}'),
            const SizedBox(height: 16),
            Text('Latitude: ${latitude ?? 'N/A'}'),
            Text('Longitude: ${longitude ?? 'N/A'}'),
            const SizedBox(height: 16),
            Text(statusMessage),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _updateUserPosition,
              child: const Text('Update Position'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _verifyUserPosition,
              child: const Text('Verify Position'),
            ),
          ],
        ),
      ),
    );
  }
}
