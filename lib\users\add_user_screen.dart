import 'dart:math';

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../models/fake_user_model.dart';
import '../models/user_model.dart';
import '../home/<USER>';
import '../providers/fake_users_provider.dart';
import 'user_repository.dart';

class AddUserScreen extends ConsumerStatefulWidget {
  final LatLng? initialPosition;

  const AddUserScreen({
    super.key,
    this.initialPosition,
  });

  @override
  ConsumerState<AddUserScreen> createState() => _AddUserScreenState();
}

class _AddUserScreenState extends ConsumerState<AddUserScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _contactController = TextEditingController();
  final _addressController = TextEditingController();
  final _customerNumberController = TextEditingController();
  final _connectionNumberController = TextEditingController();

  bool _isLoading = false;
  bool _isLoadingFakeUsers = false;
  LatLng? _selectedPosition;
  FakeUser? _selectedFakeUser;

  @override
  void initState() {
    super.initState();
    _selectedPosition = widget.initialPosition;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _contactController.dispose();
    _addressController.dispose();
    _customerNumberController.dispose();
    _connectionNumberController.dispose();
    super.dispose();
  }

  Future<void> _saveUser() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create a UserModel with all the form data including customer and connection numbers
      final user = UserModel(
        name: _nameController.text,
        contact: _contactController.text,
        address: _addressController.text,
        customerNumber: _customerNumberController.text,
        connectionNumber: _connectionNumberController.text,
        position:
            _selectedPosition, // This can be null if no location is selected
        areaId: (Random().nextInt(5) + 1).toString(),
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      // Use the repository to save the user properly
      final userRepository = UserRepository();
      await userRepository.addUser(user);

      // Update the map state to show the new user if it has a position
      if (_selectedPosition != null) {
        final mapNotifier = ref.read(mapStateProvider.notifier);
        await mapNotifier.loadInitialData();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('User added successfully')),
        );

        // Close the screen
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding user: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectLocationOnMap() async {
    // Navigate to map screen to select location
    final mapNotifier = ref.read(mapStateProvider.notifier);

    // Enable user adding mode
    mapNotifier.toggleUserMode();

    // Close this screen and let the user select a location on the map
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final fakeUsers = ref.watch(fakeUsersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add New User'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveUser,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Fake user selection
                    const Text(
                      'Select from Nepali Users',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'User Details',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Name',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _contactController,
                      decoration: const InputDecoration(
                        labelText: 'Contact',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: 'Address',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _customerNumberController,
                      decoration: const InputDecoration(
                        labelText: 'Customer Number',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _connectionNumberController,
                      decoration: const InputDecoration(
                        labelText: 'Connection Number',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Location',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_selectedPosition != null)
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Latitude: ${_selectedPosition!.latitude.toStringAsFixed(6)}',
                                style: const TextStyle(fontSize: 14),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Longitude: ${_selectedPosition!.longitude.toStringAsFixed(6)}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ],
                          ),
                        ),
                      )
                    else
                      const Card(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Text('No location selected'),
                        ),
                      ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.map),
                        label: const Text('Select Location on Map'),
                        onPressed: _selectLocationOnMap,
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
