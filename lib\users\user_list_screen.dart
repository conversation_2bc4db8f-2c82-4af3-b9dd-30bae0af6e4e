import 'package:flutter/material.dart';
import 'package:gis/models/user_model.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../home/<USER>';
import 'user_state_provider.dart';
import 'add_user_screen.dart';
import '../utils/nepali_names.dart';

class UserListScreen extends ConsumerStatefulWidget {
  const UserListScreen({super.key});

  @override
  ConsumerState<UserListScreen> createState() => _UserListScreenState();
}

class _UserListScreenState extends ConsumerState<UserListScreen> {
  @override
  void initState() {
    super.initState();
    // Load users when the screen is initialized
    Future.microtask(() => ref.read(userStateProvider.notifier).loadUsers());
  }

  // Generate and save 50 fake users to the user collection
  Future<void> _generateAndSaveFakeUsers() async {
    try {
      // Use the userStateProvider to generate fake users
      final userIds = await ref
          .read(userStateProvider.notifier)
          .generateAndSaveFakeUsers(250);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Successfully generated and saved ${userIds.length} fake users'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error generating fake users: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating fake users: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Sync user list with Firestore
  Future<void> _syncUserList() async {
    try {
      // Show loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Syncing user list...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Sync user list with Firestore using the UserStateNotifier
      final success = await ref.read(userStateProvider.notifier).syncUserList();

      // Show result
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'User list synced successfully'
                : 'Failed to sync user list'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error syncing user list: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error syncing user list: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Add method to delete all users locally
  Future<void> _deleteAllUsers() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Users'),
        content: const Text(
            'This will delete all users from local storage. This operation cannot be undone. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE ALL'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // Delete all users locally
      await ref.read(userStateProvider.notifier).deleteAllUsers();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All users deleted successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error deleting all users: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting all users: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the userStateProvider to rebuild when the state changes
    final userState = ref.watch(userStateProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text(NepaliFriendlyNames.userListTitle),
        actions: [
          // Add button to generate and save fake users to the user collection
          IconButton(
            icon: const Icon(Icons.group_add),
            tooltip: 'Generate Fake Users',
            onPressed: _generateAndSaveFakeUsers,
          ),
          // Sync button
          IconButton(
            icon: const Icon(Icons.sync),
            tooltip: 'Sync User List',
            onPressed: _syncUserList,
          ),
          // Delete all users button
          IconButton(
            icon: const Icon(Icons.delete_forever),
            tooltip: 'Delete All Users',
            onPressed: _deleteAllUsers,
          ),
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () => ref.read(userStateProvider.notifier).loadUsers(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddUserScreen(),
            ),
          ).then((_) => ref.read(userStateProvider.notifier).loadUsers());
        },
        tooltip: 'Add User',
        child: const Icon(Icons.person_add),
      ),
      body: userState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : userState.users.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('No users found'),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            icon: const Icon(Icons.person_add),
                            label: const Text('Add User'),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const AddUserScreen(),
                                ),
                              ).then((_) => ref
                                  .read(userStateProvider.notifier)
                                  .loadUsers());
                            },
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.group_add),
                            label: const Text('Generate 50 Fake Users'),
                            onPressed: _generateAndSaveFakeUsers,
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: userState.users.length,
                  itemBuilder: (context, index) {
                    final user = userState.users[index];
                    // debugPrint("User: ${user.name}");

                    return ListTile(
                      leading: const Icon(Icons.person),
                      title: Text(user.name),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (user.contact.isNotEmpty)
                            Text('Contact: ${user.contact}'),
                        ],
                      ),
                      isThreeLine:
                          user.contact.isNotEmpty || user.contact.isNotEmpty,
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // Navigate to user details or center map on user
                        Navigator.pop(context);

                        // Only center on user location if it's valid
                        if (user.position != null) {
                          // Center map on user location
                          final mapNotifier =
                              ref.read(mapStateProvider.notifier);
                          mapNotifier.centerOnPosition(user.position!);
                        } else {
                          // Show a message that the user doesn't have a location
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                  'This user doesn\'t have a location on the map yet'),
                            ),
                          );
                        }
                      },
                    );
                  },
                ),
    );
  }
}
