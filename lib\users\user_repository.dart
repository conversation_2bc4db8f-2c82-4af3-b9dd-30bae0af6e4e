import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/user_model.dart';
import '../models/hive_models/hive_user_model.dart';
import '../models/hive_models/hive_user_list_model.dart';
import '../services/hive_service.dart';
import '../utils/nepali_names.dart';

class UserRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection reference
  CollectionReference get _usersCollection => _firestore.collection('user');
  CollectionReference get _elementsCollection =>
      _firestore.collection('elements');

  // Get all users from local user list first, then from Firestore if needed
  Future<List<UserModel>> getAllUsers() async {
    try {
      // First try to get users from local storage
      final localUsers = HiveService.getAllUserListItems();

      // If we have local users, return them
      if (localUsers.isNotEmpty) {
        try {
          final userModels =
              localUsers.map((user) => user.toUserModel()).toList();
          debugPrint(
              "UserRepository: Got ${userModels.length} users from local storage");
          return userModels;
        } catch (e) {
          debugPrint(
              'UserRepository: Error converting local users to UserModel: $e');
          // If there's an error with local users, try fetching from Firestore instead
        }
      }

      // If no local users or error with local users, fetch from Firestore
      final query = _usersCollection;
      final snapshot = await query.get();

      // Convert to UserModel objects
      final users =
          snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

      // Save to local storage for future use
      for (final user in users) {
        try {
          final hiveUser = HiveUserListModel.fromUserModel(user);
          await HiveService.saveUserListItem(hiveUser, markAsSynced: true);
        } catch (e) {
          debugPrint('UserRepository: Error saving user to local storage: $e');
          // Continue with the next user even if this one fails
        }
      }

      debugPrint("UserRepository: Got ${users.length} users from Firestore");
      return users;
    } catch (e) {
      debugPrint('UserRepository: Error getting users: $e');
      // Return empty list instead of rethrowing to avoid app crashes
      return [];
    }
  }

  // Sync user list with Firestore
  Future<bool> syncUserList() async {
    try {
      // Get all unsynced user list items
      final unsyncedUsers = HiveService.getUnsyncedUserListItems();

      if (unsyncedUsers.isEmpty) {
        debugPrint('UserRepository: No unsynced user list items to sync');
        return true;
      }

      debugPrint(
          'UserRepository: Syncing ${unsyncedUsers.length} user list items');

      // Process each unsynced user
      for (final user in unsyncedUsers) {
        if (user.isDeleted) {
          // Delete from Firestore
          await _usersCollection.doc(user.id).delete();
          // If successful, permanently delete from local storage
          await HiveService.deleteUserListItem(user.id, permanent: true);
          debugPrint('UserRepository: Deleted user ${user.id} from Firestore');
        } else {
          // Update or create in Firestore
          await _usersCollection
              .doc(user.id)
              .set(user.toFirestore(), SetOptions(merge: true));
          // Mark as synced and save
          user.markAsSynced();
          await HiveService.saveUserListItem(user,
              markAsSynced: false); // We already marked it as synced
          debugPrint('UserRepository: Synced user ${user.id} to Firestore');
        }
      }

      return true;
    } catch (e) {
      debugPrint('UserRepository: Error syncing user list: $e');
      return false;
    }
  }

  // Check if a user exists in the user collection
  Future<bool> userExists(String userId) async {
    final doc = await _usersCollection.doc(userId).get();
    return doc.exists;
  }

  // Check if an element exists in the elements collection
  Future<bool> elementExists(String elementId) async {
    final doc = await _elementsCollection.doc(elementId).get();
    return doc.exists;
  }

  // Get elements by type
  Future<List<DocumentSnapshot>> getElementsByTypeInView(String type) async {
    final query = _elementsCollection.where('type', isEqualTo: type);
    final snapshot = await query.get();
    return snapshot.docs;
  }

  // Add a new user to the user list
  Future<String> addUser(UserModel user) async {
    final userId = user.id.isEmpty
        ? 'user_${DateTime.now().millisecondsSinceEpoch}'
        : user.id;

    // Create a user with the updated ID
    final updatedUser = user.copyWith(id: userId);

    // Save to user list (local storage)
    final hiveUserList = HiveUserListModel.fromUserModel(updatedUser);
    await HiveService.saveUserListItem(hiveUserList);

    // If the user has a position, also save to map elements
    if (updatedUser.position != null) {
      // Save to map elements (local storage)
      final hiveUser = HiveUserModel.fromUserModel(
        updatedUser,
        areaId: 'default',
      );
      await HiveService.saveUser(hiveUser);
    }

    return userId;
  }

  // Generate and save fake users to the user list only
  Future<List<String>> generateAndSaveFakeUsers(int count) async {
    final userIds = <String>[];
    final random = Random();

    // Generate fake users with Nepali names
    for (var i = 0; i < count; i++) {
      final name = NepaliFriendlyNames.getRandomPersonName();
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}_$i';
      // Random connections between 1 and 5
      // Create a random position for some users (about 30% of users)
      // Note: This position is only stored in the user list, not on the map
      LatLng? position;
     /* if (random.nextDouble() < 0.3) {
        // Generate a random position in Nepal (approximate coordinates)
        final lat = 27.5 + random.nextDouble() * 2; // Nepal latitude range
        final lng = 83.5 + random.nextDouble() * 4; // Nepal longitude range
        position = LatLng(lat, lng);
      }*/


      final user = UserModel(
        id: userId,
        position:
            position, // This position is only for reference, not for map display
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        contact: '+977 ${9800000000 + i}',
        customerNumber: '${random.nextInt(999999) + 1}=${random.nextInt(999999) + 1}-${random.nextInt(999999) + 1}',
        address: 'Kathmandu, Nepal',
        name: name,
        isDeleted: false,
        areaId:(Random().nextInt(5) + 1).toString(),
        userId: userId,
        connections: [],
      );

      // Save directly to user list only (not to map elements)
      final hiveUserList = HiveUserListModel.fromUserModel(user);
      await HiveService.saveUserListItem(hiveUserList);

      userIds.add(userId);
      debugPrint(
          'UserRepository: Saved fake user $i with ID $userId to user list');
    }

    return userIds;
  }

  // Delete a user from the user list
  Future<void> deleteUser(String userId) async {
    // Delete from user list (local storage)
    await HiveService.deleteUserListItem(userId);

    // Also check if user exists in map elements and delete if needed
    final userInMap = HiveService.getUsersBox().get(userId);
    if (userInMap != null) {
      await HiveService.deleteUser(userId);
    }
  }

  // Delete a user from Firestore (only called during sync)
  Future<void> deleteUserFromFirestore(String userId) async {
    // Delete from user collection
    await _usersCollection.doc(userId).delete();

    // Check if user exists in elements collection
    final exists = await elementExists(userId);
    if (exists) {
      // Delete from elements collection
      await _elementsCollection.doc(userId).delete();
    }
  }

  // Get a user from Hive by ID
  HiveUserModel? getUserFromHive(String id) {
    final box = HiveService.getUsersBox();
    return box.get(id);
  }

  // Update a user in the user list
  Future<void> updateUser(UserModel user) async {
    try {
      // Update in user list (local storage)
      final userListItem = HiveService.getUserListBox().get(user.id);
      if (userListItem != null) {
        // Update existing user list item
        userListItem.connections = user.connections;

        // Update position if available
        if (user.position != null) {
          userListItem.latitude = user.position!.latitude;
          userListItem.longitude = user.position!.longitude;
        }

        userListItem.updatedAt = DateTime.now();
        userListItem.isSync = false;

        await HiveService.saveUserListItem(userListItem);
      } else {
        // Create new user list item if it doesn't exist
        final newUserListItem = HiveUserListModel.fromUserModel(user);
        await HiveService.saveUserListItem(newUserListItem);
      }

      // Also check if user exists in map elements and update if needed
      final userInMap = HiveService.getUsersBox().get(user.id);
      if (userInMap != null) {
        // Update existing map user
        userInMap.title = user.name; // Keep title in sync with name

        // Update position if available
        if (user.position != null) {
          userInMap.latitude = user.position!.latitude;
          userInMap.longitude = user.position!.longitude;
        }

        userInMap.updatedAt = DateTime.now();
        userInMap.isSync = false;

        await HiveService.saveUser(userInMap);
      } else if (user.position != null) {
        // Create new map user if it has a position
        final newHiveUser = HiveUserModel.fromUserModel(
          user,
          areaId: 'default',
        );
        await HiveService.saveUser(newHiveUser);
      }
    } catch (e) {
      debugPrint('Error updating user: $e');
      rethrow;
    }
  }
}
