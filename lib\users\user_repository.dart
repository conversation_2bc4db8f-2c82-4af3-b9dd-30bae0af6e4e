import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/user_model.dart';
import '../models/hive_models/hive_user_model.dart';
import '../services/hive_service.dart';
import '../utils/nepali_names.dart';

class UserRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection reference
  CollectionReference get _usersCollection => _firestore.collection('user');
  CollectionReference get _elementsCollection =>
      _firestore.collection('elements');

  // Get all users from local storage, filtering by location (having valid latitude and longitude)
  Future<List<UserModel>> getAllUsers() async {
    try {
      // Get users from local storage (HiveUserModel)
      final localUsers = HiveService.getAllUsers();

      // Filter users that have valid location (latitude and longitude not null and not equal to 0.0)
      final usersWithLocation = localUsers
          .where((user) => user.latitude != 0.0 || user.longitude != 0.0)
          .toList();

      // Convert to UserModel objects
      final userModels =
          usersWithLocation.map((user) => user.toUserModel()).toList();

      debugPrint(
          "UserRepository: Got ${userModels.length} users with valid location from local storage (out of ${localUsers.length} total users)");
      return userModels;
    } catch (e) {
      debugPrint('UserRepository: Error getting users: $e');
      // Return empty list instead of rethrowing to avoid app crashes
      return [];
    }
  }

  // Sync user list with Firestore
  Future<bool> syncUserList() async {
    try {
      // Get all unsynced users from HiveUserModel
      final unsyncedUsers = HiveService.getUnsyncedUsers();

      if (unsyncedUsers.isEmpty) {
        debugPrint('UserRepository: No unsynced users to sync');
        return true;
      }

      debugPrint(
          'UserRepository: Syncing ${unsyncedUsers.length} users to Firestore');

      // Process each unsynced user
      for (final user in unsyncedUsers) {
        if (user.isDeleted) {
          // Delete from Firestore
          await _usersCollection.doc(user.id).delete();
          // If successful, permanently delete from local storage
          await HiveService.deleteUser(user.id, permanent: true);
          debugPrint('UserRepository: Deleted user ${user.id} from Firestore');
        } else {
          // Update or create in Firestore using detailed data
          await _usersCollection
              .doc(user.id)
              .set(user.toFirestoreDetails(), SetOptions(merge: true));
          // Mark as synced and save
          user.markAsSynced();
          await HiveService.saveUser(user,
              markAsSynced: false); // We already marked it as synced
          debugPrint('UserRepository: Synced user ${user.id} to Firestore');
        }
      }

      return true;
    } catch (e) {
      debugPrint('UserRepository: Error syncing user list: $e');
      return false;
    }
  }

  // Check if a user exists in the user collection
  Future<bool> userExists(String userId) async {
    final doc = await _usersCollection.doc(userId).get();
    return doc.exists;
  }

  // Check if an element exists in the elements collection
  Future<bool> elementExists(String elementId) async {
    final doc = await _elementsCollection.doc(elementId).get();
    return doc.exists;
  }

  // Get elements by type
  Future<List<DocumentSnapshot>> getElementsByTypeInView(String type) async {
    final query = _elementsCollection.where('type', isEqualTo: type);
    final snapshot = await query.get();
    return snapshot.docs;
  }

  // Add a new user to the user list
  Future<String> addUser(UserModel user) async {
    final userId = user.id.isEmpty
        ? 'user_${DateTime.now().millisecondsSinceEpoch}'
        : user.id;

    // Create a user with the updated ID
    final updatedUser = user.copyWith(id: userId);

    // Save to users collection (local storage) using HiveUserModel
    final hiveUser = HiveUserModel.fromUserModel(
      updatedUser,
      areaId: 'default',
    );
    await HiveService.saveUser(hiveUser);

    return userId;
  }

  // Generate and save fake users to the user list only
  Future<List<String>> generateAndSaveFakeUsers(int count) async {
    final userIds = <String>[];
    final random = Random();

    // Generate fake users with Nepali names
    for (var i = 0; i < count; i++) {
      final name = NepaliFriendlyNames.getRandomPersonName();
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}_$i';
      // Random connections between 1 and 5
      // Create a random position for some users (about 30% of users)
      // Note: This position is only stored in the user list, not on the map
      LatLng? position;
      /* if (random.nextDouble() < 0.3) {
        // Generate a random position in Nepal (approximate coordinates)
        final lat = 27.5 + random.nextDouble() * 2; // Nepal latitude range
        final lng = 83.5 + random.nextDouble() * 4; // Nepal longitude range
        position = LatLng(lat, lng);
      }*/

      final user = UserModel(
        id: userId,
        position:
            position, // This position is only for reference, not for map display
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        contact: '+977 ${9800000000 + i}',
        customerNumber:
            '${random.nextInt(999999) + 1}=${random.nextInt(999999) + 1}-${random.nextInt(999999) + 1}',
        address: 'Kathmandu, Nepal',
        name: name,
        isDeleted: false,
        areaId: (Random().nextInt(5) + 1).toString(),
        userId: userId,
        connections: [],
      );

      // Save directly to users collection using HiveUserModel
      final hiveUser = HiveUserModel.fromUserModel(user, areaId: 'default');
      await HiveService.saveUser(hiveUser);

      userIds.add(userId);
      debugPrint(
          'UserRepository: Saved fake user $i with ID $userId to user list');
    }

    return userIds;
  }

  // Delete a user from the users collection
  Future<void> deleteUser(String userId) async {
    // Delete from users collection (local storage)
    await HiveService.deleteUser(userId);
  }

  // Delete a user from Firestore (only called during sync)
  Future<void> deleteUserFromFirestore(String userId) async {
    // Delete from user collection
    await _usersCollection.doc(userId).delete();

    // Check if user exists in elements collection
    final exists = await elementExists(userId);
    if (exists) {
      // Delete from elements collection
      await _elementsCollection.doc(userId).delete();
    }
  }

  // Get a user from Hive by ID
  HiveUserModel? getUserFromHive(String id) {
    final box = HiveService.getUsersBox();
    return box.get(id);
  }

  // Update a user in the users collection
  Future<void> updateUser(UserModel user) async {
    try {
      // Update in users collection (local storage)
      final existingUser = HiveService.getUsersBox().get(user.id);
      if (existingUser != null) {
        // Update existing user
        existingUser.connections = user.connections;
        existingUser.name = user.name;
        existingUser.title = user.name; // Keep title in sync with name
        existingUser.contact = user.contact ?? '';
        existingUser.address = user.address ?? '';
        existingUser.customerNumber = user.customerNumber ?? '';
        existingUser.connectionNumber = user.connectionNumber ?? '';

        // Update position if available
        if (user.position != null) {
          existingUser.latitude = user.position!.latitude;
          existingUser.longitude = user.position!.longitude;
        }

        existingUser.updatedAt = DateTime.now();
        existingUser.isSync = false;

        await HiveService.saveUser(existingUser);
      } else {
        // Create new user if it doesn't exist
        final newHiveUser = HiveUserModel.fromUserModel(
          user,
          areaId: 'default',
        );
        await HiveService.saveUser(newHiveUser);
      }
    } catch (e) {
      debugPrint('Error updating user: $e');
      rethrow;
    }
  }
}
