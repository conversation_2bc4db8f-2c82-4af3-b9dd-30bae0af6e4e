import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/user_model.dart';
import 'user_repository.dart';

// Provider for the UserRepository
final userRepositoryProvider = Provider<UserRepository>((ref) {
  return UserRepository();
});

// User state class
class UserState {
  final List<UserModel> users;
  final bool isLoading;
  final String? error;

  UserState({
    required this.users,
    this.isLoading = false,
    this.error,
  });

  UserState copyWith({
    List<UserModel>? users,
    bool? isLoading,
    String? error,
  }) {
    return UserState(
      users: users ?? this.users,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// User state notifier
class UserStateNotifier extends StateNotifier<UserState> {
  final UserRepository _userRepository;

  UserStateNotifier(this._userRepository)
      : super(UserState(users: [], isLoading: false));

  // Load all users
  Future<void> loadUsers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final users = await _userRepository.getAllUsers();
      state = state.copyWith(users: users, isLoading: false);
    } catch (e) {
      debugPrint('Error loading users: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load users: $e',
      );
    }
  }

  // Add a new user
  Future<String?> addUser(UserModel user) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final userId = await _userRepository.addUser(user);

      // Reload users to get the updated list
      await loadUsers();

      return userId;
    } catch (e) {
      debugPrint('Error adding user: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to add user: $e',
      );
      return null;
    }
  }

  // Generate and save fake users
  Future<List<String>> generateAndSaveFakeUsers(int count) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final userIds = await _userRepository.generateAndSaveFakeUsers(count);
      // Reload users to get the updated list
      await loadUsers();

      return userIds;
    } catch (e) {
      debugPrint('Error generating fake users: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to generate fake users: $e',
      );
      return [];
    }
  }

  // Delete a user
  Future<void> deleteUser(String userId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _userRepository.deleteUser(userId);

      // Update the local state by removing the deleted user
      final updatedUsers =
          state.users.where((user) => user.id != userId).toList();
      state = state.copyWith(users: updatedUsers, isLoading: false);
    } catch (e) {
      debugPrint('Error deleting user: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete user: $e',
      );
    }
  }

  // Update a user
  Future<void> updateUser(UserModel user) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _userRepository.updateUser(user);

      // Update the local state by replacing the updated user
      final updatedUsers =
          state.users.map((u) => u.id == user.id ? user : u).toList();
      state = state.copyWith(users: updatedUsers, isLoading: false);
    } catch (e) {
      debugPrint('Error updating user: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update user: $e',
      );
    }
  }

  // Sync user list with Firestore
  Future<bool> syncUserList() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final success = await _userRepository.syncUserList();

      // Reload users to get the updated list
      if (success) {
        await loadUsers();
      }

      return success;
    } catch (e) {
      debugPrint('Error syncing user list: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to sync user list: $e',
      );
      return false;
    }
  }

  // Delete all users
  Future<void> deleteAllUsers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Get all user IDs
      final userIds = state.users.map((user) => user.id).toList();

      // Delete each user
      for (final userId in userIds) {
        await _userRepository.deleteUser(userId);
      }

      // Update the state
      state = state.copyWith(users: [], isLoading: false);
    } catch (e) {
      debugPrint('Error deleting all users: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete all users: $e',
      );
      throw e; // Re-throw to allow handling in the UI
    }
  }
}

// Provider for the UserState
final userStateProvider =
    StateNotifierProvider<UserStateNotifier, UserState>((ref) {
  final userRepository = ref.watch(userRepositoryProvider);
  return UserStateNotifier(userRepository);
});
