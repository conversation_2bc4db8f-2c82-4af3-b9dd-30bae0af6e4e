import 'dart:math';
import '../models/fake_user_model.dart';
import '../utils/nepali_names.dart';

class FakeUserGenerator {
  // List of Nepali cities
  static const List<String> nepaliCities = [
    'Kathmandu', 'Pokhara', 'Lalitpur', 'Bhaktapur', 'Biratnagar',
    'Birgunj', 'Dharan', 'Nepalgunj', 'Butwal', 'Dhangadhi',
    'Janakpur', 'He<PERSON>uda', 'Itahari', 'Kirtipur', 'Tulsipur',
    'Siddharthanagar', 'Birendranagar', 'Ghorahi', 'Tikapur', 'Damak',
  ];

  // List of Nepali streets
  static const List<String> nepaliStreets = [
    'Durbar Marg', 'New Road', 'Thamel', 'Asan', 'Indrachowk',
    'Pulchowk', 'Jawalakhel', 'Kupondole', 'Baluwatar', 'Lazimpat',
    'Maharajgunj', 'Cha<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  ];

  // List of Nepali phone prefixes
  static const List<String> phonePrefix = [
    '984', '985', '986', '974', '975',
    '980', '981', '982', '961', '962',
    '972', '963', '964', '965', '966',
  ];

  // Generate a random Nepali address
  static String generateAddress() {
    final random = Random();
    final houseNumber = random.nextInt(500) + 1;
    final street = nepaliStreets[random.nextInt(nepaliStreets.length)];
    final city = nepaliCities[random.nextInt(nepaliCities.length)];
    
    return '$houseNumber $street, $city, Nepal';
  }

  // Generate a random Nepali phone number
  static String generatePhoneNumber() {
    final random = Random();
    final prefix = phonePrefix[random.nextInt(phonePrefix.length)];
    final suffix = (1000000 + random.nextInt(9000000)).toString().substring(1);
    
    return '$prefix$suffix';
  }

  // Generate random notes
  static String generateNotes() {
    final random = Random();
    final noteOptions = [
      'Regular customer',
      'New connection',
      'Needs maintenance',
      'Payment pending',
      'VIP customer',
      'Government official',
      'Business owner',
      'School/Institution',
      'Hospital/Clinic',
      'Restaurant/Hotel',
      '',
      '',
      '',
    ];
    
    return noteOptions[random.nextInt(noteOptions.length)];
  }

  // Generate a list of fake users
  static List<FakeUser> generateFakeUsers(int count) {
    final List<FakeUser> users = [];
    
    for (int i = 0; i < count; i++) {
      final name = NepaliFriendlyNames.getRandomPersonName();
      final contact = generatePhoneNumber();
      final address = generateAddress();
      final notes = generateNotes();
      
      users.add(FakeUser(
        name: name,
        contact: contact,
        address: address,
        notes: notes,
      ));
    }
    
    return users;
  }
}
