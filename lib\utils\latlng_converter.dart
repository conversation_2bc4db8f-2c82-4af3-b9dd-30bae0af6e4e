import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// A custom JSON converter for the LatLng class from Google Maps Flutter.
///
/// This converter handles serialization and deserialization of LatLng objects
/// to and from JSON.
class LatLngConverter implements JsonConverter<LatLng, Map<String, dynamic>> {
  const LatLngConverter();

  @override
  LatLng fromJson(Map<String, dynamic> json) {
    return LatLng(
      json['latitude'] as double,
      json['longitude'] as double,
    );
  }

  @override
  Map<String, dynamic> toJson(LatLng latLng) {
    return {
      'latitude': latLng.latitude,
      'longitude': latLng.longitude,
    };
  }
}

/// A nullable version of the LatLng converter.
class NullableLatLngConverter implements JsonConverter<LatLng?, Map<String, dynamic>?> {
  const NullableLatLngConverter();

  @override
  LatLng? from<PERSON>son(Map<String, dynamic>? json) {
    if (json == null) return null;
    return const LatLngConverter().fromJson(json);
  }

  @override
  Map<String, dynamic>? toJson(LatLng? latLng) {
    if (latLng == null) return null;
    return const LatLngConverter().toJson(latLng);
  }
}
