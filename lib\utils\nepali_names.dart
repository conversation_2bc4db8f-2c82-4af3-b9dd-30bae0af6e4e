import 'dart:math';

/// Utility class to provide Nepali names for the app
class NepaliFriendlyNames {
  // Common Nepali first names
  static const List<String> firstNames = [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
  ];

  // Common Nepali last names
  static const List<String> lastNames = [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>rk<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>bu',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>e',
    '<PERSON><PERSON>y',
    '<PERSON><PERSON>l',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>hrestha',
    '<PERSON>edi',
    '<PERSON><PERSON>',
    '<PERSON>ha<PERSON>',
  ];

  // <PERSON> <PERSON> names
  static const List<String> valveTypes = [
    'Bhalbay',
    'Dhara',
    'Jal Niyantrak',
    'Pani Niyantrak',
    'Paani Valve',
    'Jal Valve',
    'Niyantrak',
    'Pani Bhalbay',
    'Jal Bhalbay',
    'Paani Dhara',
  ];

  // Nepali connector names
  static const List<String> connectorTypes = [
    'Jodne',
    'Sambandha',
    'Jodan',
    'Jodai',
    'Milaan',
    'Sangam',
    'Jodne Yantrak',
    'Sambandha Yantrak',
    'Jodan Yantrak',
  ];

  // Nepali smart meter names
  static const List<String> meterTypes = [
    'Bijuli Meter',
    'Paani Meter',
    'Maapakas',
    'Napne Yantra',
    'Napak',
    'Miti',
    'Napne Ojar',
    'Maapak',
    'Chettra Napak',
    'Parimaan Napak',
  ];

  // Nepali category names
  static const List<String> categoryTypes = [
    'Varg',
    'Samuh',
    'Prakaar',
    'Shreni',
    'Vibhag',
    'Upakaran Varg',
    'Samagri Samuh',
    'Yantra Prakaar',
    'Ojar Shreni',
    'Saadhan Vibhag',
  ];

  // Generate a random Nepali person name
  static String getRandomPersonName() {
    final random = Random();
    final firstName = firstNames[random.nextInt(firstNames.length)];
    final lastName = lastNames[random.nextInt(lastNames.length)];
    return '$firstName $lastName';
  }

  // Generate a random Nepali valve name
  static String getRandomValveName() {
    final random = Random();
    final valveType = valveTypes[random.nextInt(valveTypes.length)];
    final number = random.nextInt(100) + 1;
    return '$valveType $number';
  }

  // Generate a random Nepali connector name
  static String getRandomConnectorName() {
    final random = Random();
    final connectorType = connectorTypes[random.nextInt(connectorTypes.length)];
    final number = random.nextInt(100) + 1;
    return '$connectorType $number';
  }

  // Generate a random Nepali smart meter name
  static String getRandomSmartMeterName() {
    final random = Random();
    final meterType = meterTypes[random.nextInt(meterTypes.length)];
    final number = random.nextInt(100) + 1;
    return '$meterType $number';
  }

  // Generate a random Nepali category name
  static String getRandomCategoryName() {
    final random = Random();
    final categoryType = categoryTypes[random.nextInt(categoryTypes.length)];
    final number = random.nextInt(100) + 1;
    return '$categoryType $number';
  }

  // Get a Nepali-friendly name based on the entity type
  static String getNameByType(String type, String originalName) {
    // If the name already looks Nepali, keep it
    if (_looksLikeNepaliName(originalName)) {
      return originalName;
    }

    switch (type.toLowerCase()) {
      case 'user':
        return getRandomPersonName();
      case 'valve':
        return getRandomValveName();
      case 'connector':
      case 'threeconnector':
      case 'fourconnector':
      case 'reducer':
        return getRandomConnectorName();
      case 'meter':
        return getRandomSmartMeterName();
      case 'category':
        return getRandomCategoryName();
      default:
        return originalName;
    }
  }

  // Check if a name already looks like a Nepali name
  static bool _looksLikeNepaliName(String name) {
    // Check if the name contains any of the common Nepali last names
    for (final lastName in lastNames) {
      if (name.contains(lastName)) {
        return true;
      }
    }

    // Check if the name contains any of the common Nepali first names
    for (final firstName in firstNames) {
      if (name.contains(firstName)) {
        return true;
      }
    }

    return false;
  }

  // App title in Nepali
  static const String appTitle = 'पाइपलाइन नक्शांकन प्रणाली';

  // Navigation titles in Nepali
  static const String mapTitle = 'नक्शा';
  static const String userListTitle = 'प्रयोगकर्ता सूची';
  static const String valveListTitle = 'भल्भ सूची';
  static const String smartMeterListTitle = 'स्मार्ट मिटर सूची';
  static const String connectorListTitle = 'कनेक्टर/पाइपलाइन सूची';
  static const String categoryListTitle = 'सम्पत्ति वर्ग सूची';
  static const String settingsTitle = 'सेटिङ्स';
}
