import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/sync_service.dart';
import '../providers/sync_service_provider.dart';

class AppBarWidget extends ConsumerStatefulWidget
    implements PreferredSizeWidget {
  final String title;
  final VoidCallback onMenuPressed;
  final Function onSyncPressed;
  final VoidCallback onClearPressed;

  const AppBarWidget({
    super.key,
    required this.title,
    required this.onMenuPressed,
    required this.onSyncPressed,
    required this.onClearPressed,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  ConsumerState<AppBarWidget> createState() => _AppBarWidgetState();
}

class _AppBarWidgetState extends ConsumerState<AppBarWidget> {
  bool _isSyncing = false;

  @override
  Widget build(BuildContext context) {
    // Get the sync service to check status
    final syncService = ref.read(syncServiceProvider);
    syncService.forceStatusUpdate();
    final directStatus = syncService.status;

    // Determine sync button color based on status
    Color syncButtonColor = Colors.grey;

    // Check for unsynced items
    bool hasUnsynced = false;
    syncService.hasUnsyncedItems().then((value) {
      hasUnsynced = value;
    });

    if (directStatus == SyncStatus.idle && hasUnsynced) {
      syncButtonColor = Colors.orange;
    } else if (directStatus == SyncStatus.partialSync) {
      syncButtonColor = Colors.orange;
    } else if (directStatus == SyncStatus.failed ||
        directStatus == SyncStatus.noConnection) {
      syncButtonColor = Colors.red;
    } else if (directStatus == SyncStatus.upToDate) {
      syncButtonColor = Colors.green;
    } else if (directStatus == SyncStatus.syncing) {
      syncButtonColor = Colors.blue;
    }

    return AppBar(
      title: Text(widget.title),
      leading: IconButton(
        icon: const Icon(Icons.menu),
        onPressed: widget.onMenuPressed,
      ),
      actions: [
        // Sync button
        IconButton(
          icon: _isSyncing
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Icon(Icons.sync, color: syncButtonColor),
          tooltip: 'Sync data with server',
          onPressed: _isSyncing ? null : _syncData,
        ),
        // Clear button
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.red),
          tooltip: 'Clear local data and reload from server',
          onPressed: widget.onClearPressed,
        ),
      ],
    );
  }

  Future<void> _syncData() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      // Call the sync function
      widget.onSyncPressed();
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }
}
