import 'package:flutter/material.dart';

class AppDrawer extends StatelessWidget {
  final VoidCallback onSettingsPressed;
  final VoidCallback onUserListPressed;

  const AppDrawer({
    Key? key,
    required this.onSettingsPressed,
    required this.onUserListPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: <PERSON>View(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(
              color: Colors.blue,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: const [
                Text(
                  'GIS Pipeline Mapping',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Navigation Menu',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.map),
            title: const Text('Map'),
            onTap: () {
              Navigator.pop(context); // Close the drawer
            },
          ),
          ListTile(
            leading: const Icon(Icons.people),
            title: const Text('User List'),
            onTap: () {
              Navigator.pop(context); // Close the drawer
              onUserListPressed();
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context); // Close the drawer
              onSettingsPressed();
            },
          ),
        ],
      ),
    );
  }
}
