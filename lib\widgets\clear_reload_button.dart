import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../home/<USER>';

class ClearReloadButton extends ConsumerStatefulWidget {
  const ClearReloadButton({super.key});

  @override
  ClearReloadButtonState createState() => ClearReloadButtonState();
}

class ClearReloadButtonState extends ConsumerState<ClearReloadButton> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      heroTag: 'clearReloadButton',
      onPressed: _isLoading ? null : _clearAndReload,
      backgroundColor: _isLoading ? Colors.grey : Colors.red,
      tooltip: 'Clear local data and reload from server',
      child: _isLoading
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            )
          : const Icon(Icons.refresh),
    );
  }

  Future<void> _clearAndReload() async {
    if (_isLoading) return;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Local Data'),
        content: const Text(
            'This will clear all local data and reload everything from the server. This operation cannot be undone. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('CLEAR & RELOAD'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🧹 Starting clear and reload operation...');

      // Use the MapStateNotifier's forceRefreshFromFirestore method
      final mapNotifier = ref.read(mapStateProvider.notifier);
      final success = await mapNotifier.forceRefreshFromFirestore();

      if (success) {
        debugPrint('✅ Successfully refreshed data from Firestore');

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Data cleared and reloaded from server')),
        );
      } else {
        debugPrint('❌ Failed to refresh data from Firestore');

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(
                  'Failed to refresh data from server. Please check your connection and try again.')),
        );
      }
    } catch (e) {
      debugPrint('❌ Error clearing and reloading data: $e');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
