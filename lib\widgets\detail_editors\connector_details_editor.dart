import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../models/firestore_models.dart';
import '../../models/pipeline.dart';
import '../../providers/firestore_provider.dart';
import '../../home/<USER>';
import '../../repositories/firestore_repository.dart';

class ConnectorDetailsEditor extends ConsumerStatefulWidget {
  final String connectorId;
  final String title;
  final Function? onSaved;

  const ConnectorDetailsEditor({
    Key? key,
    required this.connectorId,
    required this.title,
    this.onSaved,
  }) : super(key: key);

  @override
  _ConnectorDetailsEditorState createState() => _ConnectorDetailsEditorState();
}

class _ConnectorDetailsEditorState extends ConsumerState<ConnectorDetailsEditor> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  
  // Form fields
  final TextEditingController _nameController = TextEditingController();
  int _connectorType = ConnectorType.tee;
  final TextEditingController _materialController = TextEditingController();
  final TextEditingController _diameterController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  List<String> _connections = [];

  @override
  void initState() {
    super.initState();
    _loadConnectorDetails();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _materialController.dispose();
    _diameterController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadConnectorDetails() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      final connectorDetails = await firestoreRepository.getElementDetails(
          widget.connectorId, 'connector') as FirestoreConnectorDetails?;

      if (connectorDetails != null) {
        _nameController.text = widget.title;
        _connectorType = connectorDetails.type;
        _materialController.text = 
            connectorDetails.specifications['material'] ?? 'Standard';
        _diameterController.text = 
            (connectorDetails.specifications['diameter'] ?? '').toString();
        _notesController.text = connectorDetails.specifications['notes'] ?? '';
        _connections = connectorDetails.connections;
      } else {
        _nameController.text = widget.title;
        _connectorType = ConnectorType.tee;
        _materialController.text = 'Standard';
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error loading connector details: $e';
      });
      print('Error loading connector details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveConnectorDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      
      // Create specifications map
      final specifications = {
        'material': _materialController.text,
        'diameter': _diameterController.text.isNotEmpty 
            ? int.tryParse(_diameterController.text) ?? 0 
            : 0,
        'notes': _notesController.text,
      };

      // Update connector details
      await firestoreRepository.updateConnectorDetails(
          widget.connectorId, _connectorType, specifications, _connections);
      
      // Update title and connector type in elements collection
      await firestoreRepository.updateElementMetadata(
          widget.connectorId, 
          _nameController.text,
          {'connectorType': _connectorType});

      // Call onSaved callback if provided
      if (widget.onSaved != null) {
        widget.onSaved!();
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Connector details saved successfully')),
      );

      // Close the dialog
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error saving connector details: $e';
      });
      print('Error saving connector details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Connector Details'),
        actions: [
          IconButton(
            icon: Icon(Icons.save),
            onPressed: _isLoading ? null : _saveConnectorDetails,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _hasError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 10),
                      Text(_errorMessage),
                      SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: _loadConnectorDetails,
                        child: Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: _nameController,
                          decoration: InputDecoration(
                            labelText: 'Name',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a name';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16),
                        DropdownButtonFormField<int>(
                          value: _connectorType,
                          decoration: InputDecoration(
                            labelText: 'Connector Type',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            DropdownMenuItem(
                              value: ConnectorType.tee,
                              child: Row(
                                children: [
                                  Icon(ConnectorType.getIcon(ConnectorType.tee)),
                                  SizedBox(width: 10),
                                  Text('Tee Connector'),
                                ],
                              ),
                            ),
                            DropdownMenuItem(
                              value: ConnectorType.elbow,
                              child: Row(
                                children: [
                                  Icon(ConnectorType.getIcon(ConnectorType.elbow)),
                                  SizedBox(width: 10),
                                  Text('Elbow Connector'),
                                ],
                              ),
                            ),
                            DropdownMenuItem(
                              value: ConnectorType.coupling,
                              child: Row(
                                children: [
                                  Icon(ConnectorType.getIcon(ConnectorType.coupling)),
                                  SizedBox(width: 10),
                                  Text('Coupling Connector'),
                                ],
                              ),
                            ),
                            DropdownMenuItem(
                              value: ConnectorType.cross,
                              child: Row(
                                children: [
                                  Icon(ConnectorType.getIcon(ConnectorType.cross)),
                                  SizedBox(width: 10),
                                  Text('Cross Connector'),
                                ],
                              ),
                            ),
                          ],
                          onChanged: (int? newValue) {
                            if (newValue != null) {
                              setState(() {
                                _connectorType = newValue;
                              });
                            }
                          },
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _materialController,
                          decoration: InputDecoration(
                            labelText: 'Material',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _diameterController,
                          decoration: InputDecoration(
                            labelText: 'Diameter (mm)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'Notes',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 3,
                        ),
                        SizedBox(height: 24),
                        Text(
                          'Connected Pipelines',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        _connections.isEmpty
                            ? Text('No connected pipelines')
                            : ListView.builder(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: _connections.length,
                                itemBuilder: (context, index) {
                                  return ListTile(
                                    title: Text('Pipeline ${_connections[index]}'),
                                    trailing: IconButton(
                                      icon: Icon(Icons.delete, color: Colors.red),
                                      onPressed: () {
                                        setState(() {
                                          _connections.removeAt(index);
                                        });
                                      },
                                    ),
                                  );
                                },
                              ),
                      ],
                    ),
                  ),
                ),
    );
  }
}
