import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../models/firestore_models.dart';
import '../../providers/firestore_provider.dart';
import '../../home/<USER>';
import '../../repositories/firestore_repository.dart';

class PipelineDetailsEditor extends ConsumerStatefulWidget {
  final String pipelineId;
  final String title;
  final Function? onSaved;

  const PipelineDetailsEditor({
    Key? key,
    required this.pipelineId,
    required this.title,
    this.onSaved,
  }) : super(key: key);

  @override
  _PipelineDetailsEditorState createState() => _PipelineDetailsEditorState();
}

class _PipelineDetailsEditorState extends ConsumerState<PipelineDetailsEditor> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  // Form fields
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _materialController = TextEditingController();
  final TextEditingController _diameterController = TextEditingController();
  final TextEditingController _pressureController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  DateTime? _installationDate;
  String _status = 'Active';
  List<String> _connections = [];

  final List<String> _statusOptions = [
    'Active',
    'Inactive',
    'Under Maintenance',
    'Planned',
    'Decommissioned'
  ];

  @override
  void initState() {
    super.initState();
    _loadPipelineDetails();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _materialController.dispose();
    _diameterController.dispose();
    _pressureController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadPipelineDetails() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      final pipelineDetails = await firestoreRepository.getElementDetails(
          widget.pipelineId, 'pipeline') as FirestorePipelineDetails?;

      if (pipelineDetails != null) {
        _nameController.text = widget.title;
        _materialController.text =
            pipelineDetails.properties['material'] ?? 'Standard';
        _diameterController.text =
            (pipelineDetails.properties['diameter'] ?? '').toString();
        _pressureController.text =
            (pipelineDetails.properties['pressure'] ?? '').toString();
        _notesController.text = pipelineDetails.properties['notes'] ?? '';
        // Check for state first, then fall back to status for backward compatibility
        _status = pipelineDetails.properties['state'] ??
            pipelineDetails.properties['status'] ??
            'Active';

        if (pipelineDetails.properties.containsKey('installationDate')) {
          final timestamp = pipelineDetails.properties['installationDate'];
          if (timestamp != null) {
            _installationDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
          }
        }

        _connections = pipelineDetails.connections;
      } else {
        _nameController.text = widget.title;
        _materialController.text = 'Standard';
        _status = 'Active';
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error loading pipeline details: $e';
      });
      debugPrint('Error loading pipeline details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _savePipelineDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Create properties map
      final properties = {
        'material': _materialController.text,
        'diameter': _diameterController.text.isNotEmpty
            ? int.tryParse(_diameterController.text) ?? 0
            : 0,
        'pressure': _pressureController.text.isNotEmpty
            ? double.tryParse(_pressureController.text) ?? 0.0
            : 0.0,
        'notes': _notesController.text,
        'state':
            _status, // Use 'state' instead of 'status' to match with FirestoreRepository
        'status': _status, // Keep 'status' for backward compatibility
        'installationDate': _installationDate?.millisecondsSinceEpoch,
      };

      // Update pipeline details
      await firestoreRepository.updatePipelineDetails(
          widget.pipelineId, properties);

      // Update title in elements collection if name changed
      if (_nameController.text != widget.title) {
        await firestoreRepository.updateElementTitle(
            widget.pipelineId, _nameController.text);
      }

      // Call onSaved callback if provided
      if (widget.onSaved != null) {
        widget.onSaved!();
      }

      // Check if widget is still mounted before using context
      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Pipeline details saved successfully')),
        );

        // Close the dialog
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error saving pipeline details: $e';
      });
      debugPrint('Error saving pipeline details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Pipeline Details'),
        actions: [
          IconButton(
            icon: Icon(Icons.save),
            onPressed: _isLoading ? null : _savePipelineDetails,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _hasError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 10),
                      Text(_errorMessage),
                      SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: _loadPipelineDetails,
                        child: Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: _nameController,
                          decoration: InputDecoration(
                            labelText: 'Name',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a name';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: _status,
                          decoration: InputDecoration(
                            labelText: 'Status',
                            border: OutlineInputBorder(),
                          ),
                          items: _statusOptions.map((String status) {
                            return DropdownMenuItem<String>(
                              value: status,
                              child: Text(status),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null) {
                              setState(() {
                                _status = newValue;
                              });
                            }
                          },
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _materialController,
                          decoration: InputDecoration(
                            labelText: 'Material',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _diameterController,
                          decoration: InputDecoration(
                            labelText: 'Diameter (mm)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _pressureController,
                          decoration: InputDecoration(
                            labelText: 'Pressure Rating (MPa)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'Installation Date: ${_installationDate != null ? _formatDate(_installationDate!) : 'Not set'}',
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                            TextButton(
                              onPressed: () => _selectDate(context),
                              child: Text('Select Date'),
                            ),
                            if (_installationDate != null)
                              IconButton(
                                icon: Icon(Icons.clear),
                                onPressed: () {
                                  setState(() {
                                    _installationDate = null;
                                  });
                                },
                              ),
                          ],
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'Notes',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 3,
                        ),
                        SizedBox(height: 24),
                        Text(
                          'Connected Elements',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        _connections.isEmpty
                            ? Text('No connected elements')
                            : ListView.builder(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: _connections.length,
                                itemBuilder: (context, index) {
                                  return ListTile(
                                    title:
                                        Text('Element ${_connections[index]}'),
                                    trailing: IconButton(
                                      icon:
                                          Icon(Icons.delete, color: Colors.red),
                                      onPressed: () {
                                        setState(() {
                                          _connections.removeAt(index);
                                        });
                                      },
                                    ),
                                  );
                                },
                              ),
                      ],
                    ),
                  ),
                ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _installationDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(
          Duration(days: 365 * 10)), // Allow future dates for planned pipelines
    );
    if (picked != null && picked != _installationDate) {
      setState(() {
        _installationDate = picked;
      });
    }
  }
}
