import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../models/pipeline.dart';
import '../../models/hive_models/hive_smart_meter_model.dart';
import '../../home/<USER>';
import '../../services/hive_service.dart';

class SmartMeterDetailsEditor extends HookConsumerWidget {
  final UserValvePosition position;

  const SmartMeterDetailsEditor({
    Key? key,
    required this.position,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mapNotifier = ref.read(mapStateProvider.notifier);

    // Get the smart meter from Hive
    final smartMeters = HiveService.getAllSmartMeters();
    final smartMeter = smartMeters.firstWhere(
      (meter) => meter.id == position.id,
      orElse: () => HiveSmartMeterModel(
        latitude: position.position.latitude,
        longitude: position.position.longitude,
        title: position.title,
        areaId: 'default',
      ),
    );

    // Controllers
    final titleController = TextEditingController(text: smartMeter.title);
    final typeController = TextEditingController(text: smartMeter.type);
    final readingController = TextEditingController(text: smartMeter.reading);
    final statusController = TextEditingController(text: smartMeter.status);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Smart Meter Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: typeController,
                decoration: const InputDecoration(
                  labelText: 'Meter Type',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: readingController,
                decoration: const InputDecoration(
                  labelText: 'Current Reading',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: smartMeter.status,
                decoration: const InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'Active', child: Text('Active')),
                  DropdownMenuItem(value: 'Inactive', child: Text('Inactive')),
                  DropdownMenuItem(
                      value: 'Maintenance', child: Text('Maintenance')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    statusController.text = value;
                  }
                },
              ),
              const SizedBox(height: 32),
              Row(
                children: [
                  Text(
                    'Location: (${position.position.latitude.toStringAsFixed(6)}, ${position.position.longitude.toStringAsFixed(6)})',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    // Update the smart meter
                    smartMeter.title = titleController.text;
                    smartMeter.type = typeController.text;
                    smartMeter.reading = readingController.text;
                    smartMeter.status = statusController.text;

                    // Mark as updated
                    smartMeter.markAsUpdated();

                    // Save to Hive
                    await HiveService.saveSmartMeter(smartMeter);

                    // Update the UI
                    mapNotifier.updateMarkerTitle(
                        position.id, titleController.text);

                    // Close the editor
                    if (context.mounted) {
                      Navigator.pop(context);
                    }
                  },
                  child: const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                    child: Text('Save Changes'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
