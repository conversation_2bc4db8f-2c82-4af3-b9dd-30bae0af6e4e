import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../models/firestore_models.dart';
import '../../providers/firestore_provider.dart';
import '../../home/<USER>';
import '../../repositories/firestore_repository.dart';

class UserDetailsEditor extends ConsumerStatefulWidget {
  final String userId;
  final String title;
  final Function? onSaved;

  const UserDetailsEditor({
    Key? key,
    required this.userId,
    required this.title,
    this.onSaved,
  }) : super(key: key);

  @override
  _UserDetailsEditorState createState() => _UserDetailsEditorState();
}

class _UserDetailsEditorState extends ConsumerState<UserDetailsEditor> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  
  // Form fields
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUserDetails();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _contactController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadUserDetails() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      final userDetails = await firestoreRepository.getElementDetails(
          widget.userId, 'user') as FirestoreUserDetails?;

      if (userDetails != null) {
        _nameController.text = userDetails.details['name'] ?? widget.title;
        _contactController.text = userDetails.details['contact'] ?? '';
        _addressController.text = userDetails.details['address'] ?? '';
        _notesController.text = userDetails.details['notes'] ?? '';
      } else {
        _nameController.text = widget.title;
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error loading user details: $e';
      });
      print('Error loading user details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveUserDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      
      // Create details map
      final details = {
        'name': _nameController.text,
        'contact': _contactController.text,
        'address': _addressController.text,
        'notes': _notesController.text,
        'lastUpdated': DateTime.now().millisecondsSinceEpoch,
      };

      // Update user details
      await firestoreRepository.updateUserDetails(widget.userId, details);
      
      // Update title in elements collection if name changed
      if (_nameController.text != widget.title) {
        await firestoreRepository.updateElementTitle(
            widget.userId, _nameController.text);
      }

      // Call onSaved callback if provided
      if (widget.onSaved != null) {
        widget.onSaved!();
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User details saved successfully')),
      );

      // Close the dialog
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error saving user details: $e';
      });
      print('Error saving user details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit User Details'),
        actions: [
          IconButton(
            icon: Icon(Icons.save),
            onPressed: _isLoading ? null : _saveUserDetails,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _hasError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 10),
                      Text(_errorMessage),
                      SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: _loadUserDetails,
                        child: Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: _nameController,
                          decoration: InputDecoration(
                            labelText: 'Name',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a name';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _contactController,
                          decoration: InputDecoration(
                            labelText: 'Contact Information',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _addressController,
                          decoration: InputDecoration(
                            labelText: 'Address',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'Notes',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ),
    );
  }
}
