import 'dart:math';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../home/<USER>';
import '../widgets/sync_button.dart';
import '../widgets/clear_reload_button.dart';
import '../models/pipeline.dart';

class MapWidget extends HookConsumerWidget {
  final CameraPosition initialPosition;

  const MapWidget({
    Key? key,
    this.initialPosition = const CameraPosition(
      target: LatLng(27.7120, 85.2994),
      zoom: 14,
    ),
  }) : super(key: key);

  // Check if map bounds have changed significantly to reload data
  bool _isBoundsChangedSignificantly(
      LatLngBounds oldBounds, LatLngBounds newBounds) {
    // Calculate the center points of both bounds
    final oldCenter = LatLng(
      (oldBounds.northeast.latitude + oldBounds.southwest.latitude) / 2,
      (oldBounds.northeast.longitude + oldBounds.southwest.longitude) / 2,
    );

    final newCenter = LatLng(
      (newBounds.northeast.latitude + newBounds.southwest.latitude) / 2,
      (newBounds.northeast.longitude + newBounds.southwest.longitude) / 2,
    );

    // Calculate the distance between centers
    final distance = _calculateDistance(oldCenter, newCenter);

    // Calculate the diagonal distance of the old bounds (as a reference)
    final oldDiagonal = _calculateDistance(
      oldBounds.northeast,
      oldBounds.southwest,
    );

    // If the center has moved more than 50% of the diagonal, consider it significant
    return distance > (oldDiagonal * 0.5);
  }

  // Calculate distance between two points in meters
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // in meters

    final lat1 = point1.latitude * pi / 180;
    final lat2 = point2.latitude * pi / 180;
    final dLat = (point2.latitude - point1.latitude) * pi / 180;
    final dLon = (point2.longitude - point1.longitude) * pi / 180;

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mapState = ref.watch(mapStateProvider);
    final mapNotifier = ref.read(mapStateProvider.notifier);

    Set<Marker> _getMarkers() {
      return mapState.userValvePositions.map((position) {
        // Determine the appropriate icon based on position type
        BitmapDescriptor icon;
        switch (position.type) {
          case PositionType.user:
            icon = mapState.userIcon;
            break;
          case PositionType.valve:
            icon = mapState.valveIcon;
            break;
          case PositionType.connector:
            icon = mapState.connectorIcon;
            break;
          case PositionType.meter:
            icon = mapState.meter;
            break;
          default:
            icon = BitmapDescriptor.defaultMarker;
        }

        // Make valve, user, and smart meter markers draggable
        final bool isDraggable = position.type == PositionType.valve ||
            position.type == PositionType.user ||
            position.type == PositionType.meter;

        return Marker(
          markerId: MarkerId(position.id),
          position: position.position,
          icon: icon,
          infoWindow: InfoWindow(
            title: position.title,
            snippet: isDraggable ? 'Long press to drag' : null,
          ),
          draggable: isDraggable,
          onDragEnd: isDraggable
              ? (newPosition) async {
                  if (position.type == PositionType.valve) {
                    await mapNotifier.updateValvePosition(
                      position.id,
                      newPosition,
                      context: context,
                    );
                  } else if (position.type == PositionType.user) {
                    await mapNotifier.updateUserPosition(
                      position.id,
                      newPosition,
                      context: context,
                    );
                  } else if (position.type == PositionType.meter) {
                    await mapNotifier.updateSmartMeterPosition(
                      position.id,
                      newPosition,
                      context: context,
                    );
                  }
                }
              : null,
          onTap: () {
            if (!mapState.isDrawing) {
              mapNotifier.showMarkerOptions(context, position);
            }
          },
        );
      }).toSet();
    }

    Set<Polyline> _getPolylines() {
      final Set<Polyline> polylines = {};

      print(
          'DEBUG: Building polylines for ${mapState.pipelines.length} pipelines');

      // Add existing pipeline segments
      for (final pipeline in mapState.pipelines) {
        print(
            'DEBUG: Processing pipeline ${pipeline.id} with ${pipeline.points.length} points');

        if (pipeline.points.length < 2) {
          print(
              'DEBUG: Pipeline ${pipeline.id} has fewer than 2 points, skipping');
          continue;
        }

        // Get the overall pipeline state
        final pipelineState = pipeline.state;

        for (int i = 0; i < pipeline.points.length - 1; i++) {
          final start = pipeline.points[i];
          final end = pipeline.points[i + 1];
          final segmentId = mapNotifier.generateSegmentId(start, end);

          // Get segment state if available, otherwise use pipeline state
          String segmentState = pipelineState;
          if (pipeline.segments.containsKey(segmentId)) {
            segmentState = pipeline.segments[segmentId]!.state;
          }

          // Get color based on state
          Color segmentColor = pipeline.id == mapState.editingPipelineId
              ? Colors.red
              : Pipeline.getColorForState(segmentState);

          print(
              'DEBUG: Adding polyline segment $segmentId from $start to $end with state $segmentState');

          polylines.add(Polyline(
            polylineId: PolylineId(segmentId),
            points: [start, end],
            color: segmentColor,
            width: 8,
            onTap: (mapState.isDrawing || mapState.addingMeter)
                ? null
                : () {
                    mapNotifier.showPipelineOptions(context, segmentId,
                        segmentState: segmentState);
                  },
          ));
        }
      }

      print('DEBUG: Created ${polylines.length} polyline segments');

      // Add current drawing line if any
      if (mapState.isDrawing && mapState.currentPoints.isNotEmpty) {
        for (int i = 0; i < mapState.currentPoints.length - 1; i++) {
          polylines.add(Polyline(
            polylineId: PolylineId('drawing_${i}'),
            points: [mapState.currentPoints[i], mapState.currentPoints[i + 1]],
            color: Colors.red,
            width: 8,
          ));
        }
      }

      return polylines;
    }

    return Scaffold(
      body: Stack(
        children: [
          GoogleMap(
            initialCameraPosition: initialPosition,
            polylines: _getPolylines(),
            markers: _getMarkers(),
            onTap: (point) async {
              await mapNotifier.onMapTap(point);
            },
            myLocationEnabled: mapState.showMyLocation,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            onCameraMove: (CameraPosition position) {
              mapNotifier.updateZoom(position.zoom);
            },
            onCameraIdle: () async {
              if (mapState.mapController != null) {
                final bounds = await mapNotifier.getBoundsFromVisibleRegion();
                if (bounds != null) {
                  mapNotifier.updateMapBounds(bounds);

                  // Reload data when map bounds change significantly
                  if (mapState.currentMapBounds != null &&
                      _isBoundsChangedSignificantly(
                          mapState.currentMapBounds!, bounds)) {
                    await mapNotifier.loadInitialData();
                  }
                }
              }
            },
            onMapCreated: (GoogleMapController controller) {
              mapNotifier.setMapController(controller);
            },
          ),
          // Add editing controls when in editing mode
          if (mapState.editingPipelineId != null)
            Positioned(
              top: 16,
              left: 16,
              child: SafeArea(
                child: FloatingActionButton(
                  heroTag: 'cancel_edit',
                  onPressed: () => mapNotifier.cancelEditing(),
                  child: Icon(Icons.close),
                  backgroundColor: Colors.red,
                  mini: true,
                ),
              ),
            ),
          // Sync and Clear buttons
          Positioned(
            right: 16,
            bottom: 16,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const SyncButton(),
                const SizedBox(height: 10),
                const ClearReloadButton(),
                const SizedBox(height: 20),
              ],
            ),
          ),
          // Existing floating action buttons
          Positioned(
            left: 16,
            bottom: 16,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (!mapState.isDrawing)
                  FloatingActionButton(
                    heroTag: 'start_drawing',
                    onPressed: () => mapNotifier.startNewPipeline(),
                    child: const Icon(Icons.add),
                  ),
                if (mapState.isDrawing)
                  FloatingActionButton(
                    heroTag: 'complete_drawing',
                    onPressed: () => mapNotifier.completePipeline(),
                    child: const Icon(Icons.check),
                  ),
                const SizedBox(height: 10),
                // Scrollable row for add options
                Container(
                  height: 60,
                  width: 200,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          heroTag: 'addUser',
                          onPressed: () => mapNotifier.toggleUserMode(),
                          backgroundColor:
                              mapState.addingUser ? Colors.green : null,
                          child: const Icon(Icons.person_2_outlined),
                          tooltip: 'Add User',
                          mini: true,
                        ),
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          heroTag: 'addValve',
                          onPressed: () => mapNotifier.toggleValveMode(),
                          backgroundColor:
                              mapState.addingValve ? Colors.green : null,
                          child: const Icon(Icons.plumbing_outlined),
                          tooltip: 'Add Valve',
                          mini: true,
                        ),
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          heroTag: 'addConnector',
                          onPressed: () => mapNotifier.toggleConnectorMode(),
                          backgroundColor:
                              mapState.addingConnector ? Colors.green : null,
                          child: const Icon(Icons.hub_outlined),
                          tooltip: 'Add Connector',
                          mini: true,
                        ),
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          heroTag: 'addMeter',
                          onPressed: () => mapNotifier.toggleMeterMode(),
                          backgroundColor:
                              mapState.addingMeter ? Colors.green : null,
                          child: const Icon(Icons.speed_outlined),
                          tooltip: 'Add Smart Meter',
                          mini: true,
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                FloatingActionButton(
                  heroTag: 'undo',
                  onPressed: () => mapNotifier.undoLastPoint(),
                  child: const Icon(Icons.undo),
                  tooltip: 'Undo Last Point',
                ),
              ],
            ),
          ),
          // Loading indicator
          if (mapState.isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
