import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/sync_service.dart';
import '../providers/local_storage_provider.dart';

class SyncButton extends ConsumerStatefulWidget {
  const SyncButton({Key? key}) : super(key: key);

  @override
  _SyncButtonState createState() => _SyncButtonState();
}

class _SyncButtonState extends ConsumerState<SyncButton> {
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    debugPrint('SyncButton initState called');
    // Force an initial status check
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('SyncButton post-frame callback executing');
      final syncService = ref.read(syncServiceProvider);
      // Force a status update
      syncService.forceStatusUpdate();
      // Get current statistics
      final stats = syncService.getSyncStatistics();
      debugPrint('Current sync status: ${stats.status}');
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Check for unsynced items when dependencies change
    final syncService = ref.read(syncServiceProvider);
    syncService.forceStatusUpdate();
  }

  @override
  Widget build(BuildContext context) {
    // Get the direct status from the service
    final syncService = ref.read(syncServiceProvider);

    // Force a status update to ensure we have the latest status
    syncService.forceStatusUpdate();

    final directStatus = syncService.status;
    debugPrint('SyncButton build: Direct status = $directStatus');

    // Check for unsynced items and update UI accordingly
    if (directStatus == SyncStatus.idle ||
        directStatus == SyncStatus.upToDate) {
      // Use FutureBuilder to check for unsynced items
      return FutureBuilder<bool>(
        future: syncService.hasUnsyncedItems(),
        builder: (context, snapshot) {
          // If we have data and there are unsynced items, show a different status
          if (snapshot.hasData && snapshot.data == true) {
            debugPrint(
                'SyncButton: Found unsynced items, showing sync needed status');
            return _buildButtonForStatus(SyncStatus.idle);
          }

          // Otherwise, use the direct status
          return _buildButtonForStatus(directStatus);
        },
      );
    }

    // For other statuses, just use the direct status
    return _buildButtonForStatus(directStatus);
  }

  // Helper method to build a button for a specific status
  Widget _buildButtonForStatus(SyncStatus status) {
    // Determine icon and color based on sync status
    IconData icon = Icons.sync; // Default icon
    Color color = Colors.grey; // Default color
    String tooltip = 'Sync data with server'; // Default tooltip

    switch (status) {
      case SyncStatus.idle:
        icon = Icons.sync;
        color = Colors.grey;
        tooltip = 'Sync data with server';
        break;
      case SyncStatus.syncing:
        icon = Icons.sync;
        color = Colors.blue;
        tooltip = 'Syncing in progress...';
        break;
      case SyncStatus.completed:
        icon = Icons.check_circle;
        color = Colors.green;
        tooltip = 'All data synced';
        break;
      case SyncStatus.partialSync:
        icon = Icons.sync_problem;
        color = Colors.orange;
        tooltip = 'Some items failed to sync';
        break;
      case SyncStatus.failed:
        icon = Icons.error;
        color = Colors.red;
        tooltip = 'Sync failed';
        break;
      case SyncStatus.noConnection:
        icon = Icons.signal_wifi_off;
        color = Colors.red;
        tooltip = 'No internet connection';
        break;
      case SyncStatus.upToDate:
        icon = Icons.check_circle;
        color = Colors.green;
        tooltip = 'All data synced';
        break;
      default:
        // Handle any future enum values
        icon = Icons.sync;
        color = Colors.grey;
        tooltip = 'Sync data with server';
        break;
    }

    return FloatingActionButton(
      heroTag: 'syncButton',
      onPressed: _isSyncing ? null : _syncData,
      backgroundColor: _isSyncing ? Colors.grey : color,
      tooltip: tooltip,
      child: _isSyncing
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            )
          : Icon(icon),
    );
  }

  Future<void> _syncData() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    debugPrint('SyncButton: Starting sync operation');

    try {
      final syncService = ref.read(syncServiceProvider);

      // Force a status update before syncing
      syncService.forceStatusUpdate();

      final success = await syncService.syncAll();

      // Force another status update after syncing
      syncService.forceStatusUpdate();

      // Check if widget is still mounted before using context
      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('All data synced successfully')),
        );
      } else {
        final stats = syncService.getSyncStatistics();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Sync completed with issues: ${stats.syncedItems}/${stats.totalItems} items synced'),
            action: SnackBarAction(
              label: 'RETRY',
              onPressed: _syncData,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('SyncButton: Error during sync: $e');

      // Check if widget is still mounted before using context
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Sync error: $e')),
      );
    } finally {
      debugPrint('SyncButton: Sync operation completed');

      if (mounted) {
        setState(() {
          _isSyncing = false;
        });

        // Force a final status update to reflect the current state
        final syncService = ref.read(syncServiceProvider);
        syncService.forceStatusUpdate();
      }
    }
  }
}
