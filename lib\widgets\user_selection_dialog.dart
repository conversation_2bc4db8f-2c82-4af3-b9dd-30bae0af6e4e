import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/user_model.dart';
import '../users/user_state_provider.dart';
import '../home/<USER>';

class UserSelectionDialog extends ConsumerStatefulWidget {
  final LatLng position;

  const UserSelectionDialog({
    Key? key,
    required this.position,
  }) : super(key: key);

  @override
  ConsumerState<UserSelectionDialog> createState() =>
      _UserSelectionDialogState();
}

class _UserSelectionDialogState extends ConsumerState<UserSelectionDialog> {
  UserModel? _selectedUser;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load users when the dialog is initialized
    Future.microtask(() => ref.read(userStateProvider.notifier).loadUsers());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _selectUser(UserModel user) {
    setState(() {
      _selectedUser = user;
    });
  }

  void _addSelectedUserToMap() async {
    if (_selectedUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a user first')),
      );
      return;
    }

    // Add the selected user to the map at the specified position
    // Pass the user ID from the user collection to ensure the elements node ID is the same
    final mapNotifier = ref.read(mapStateProvider.notifier);
    await mapNotifier.addUserWithDetails(
      widget.position,
      _selectedUser!.name,
      {
        'name': _selectedUser!.name,
        'userId': _selectedUser!.id, // Pass the existing user ID
        'contact': _selectedUser!.contact,
        'address': _selectedUser!.address,
      },
      context: context, // Pass context for showing error messages
    );

    if (mounted) {
      Navigator.of(context).pop(true); // Return true to indicate success
    }
  }

  List<UserModel> _getFilteredUsers(List<UserModel> users) {
    if (_searchQuery.isEmpty) {
      return users;
    }

    return users.where((user) {
      final query = _searchQuery.toLowerCase();
      return user.name.toLowerCase().contains(query) ||
          user.contact.toLowerCase().contains(query) ||
          user.address.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    // Watch the userStateProvider to rebuild when the state changes
    final userState = ref.watch(userStateProvider);
    final filteredUsers = _getFilteredUsers(userState.users);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select User',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Search bar
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search users...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),
            userState.isLoading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : Container(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.5,
                    ),
                    child: filteredUsers.isEmpty
                        ? const Center(
                            child: Text('No users found'),
                          )
                        : ListView.builder(
                            shrinkWrap: true,
                            itemCount: filteredUsers.length,
                            itemBuilder: (context, index) {
                              final user = filteredUsers[index];
                              return ListTile(
                                title: Text(user.name),
                                subtitle: Text(user.contact),
                                selected: _selectedUser?.id == user.id,
                                onTap: () => _selectUser(user),
                                trailing: _selectedUser?.id == user.id
                                    ? const Icon(Icons.check_circle,
                                        color: Colors.green)
                                    : null,
                              );
                            },
                          ),
                  ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _addSelectedUserToMap,
                  child: const Text('Add User'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
